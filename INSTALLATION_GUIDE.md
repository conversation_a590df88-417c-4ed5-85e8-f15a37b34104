# Tutor LMS Advanced Search - Installation Guide

## Quick Installation

### Step 1: Verify Prerequisites
- ✅ WordPress 5.0 or higher
- ✅ PHP 7.4 or higher  
- ✅ Tutor LMS plugin installed and activated
- ✅ Instructor account with quiz attempts to search

### Step 2: Install the Plugin
The plugin is already created in your WordPress installation at:
```
wp-content/plugins/tutor-lms-advanced-search/
```

### Step 3: Activate the Plugin
1. Go to **WordPress Admin → Plugins**
2. Find "Tutor LMS Advanced Search"
3. Click **Activate**

### Step 4: Test the Functionality
1. Log in as an instructor
2. Navigate to **Dashboard → Quiz Attempts**
3. You should see the new search interface with:
   - Search input field at the top
   - Enhanced filter options
   - "Search" and "Clear All" buttons

## What You'll See

### Before (Original Tutor LMS)
- Basic filters: Course, Sort By, Date
- No search functionality
- Limited filtering options

### After (With Advanced Search)
- **New Search Field**: Search by student name or email
- **Enhanced Interface**: Better organized filters
- **Search Results Info**: Shows what you're searching for
- **Clear Functionality**: Easy way to reset all filters
- **Responsive Design**: Works on mobile devices

## Testing the Search

### Test Search by Student Name
1. Enter a student's first or last name in the search field
2. Click "Search" or press Enter
3. Results should show only attempts from that student

### Test Search by Email
1. Enter a student's email address (partial or complete)
2. Submit the search
3. Results should filter to that student's attempts

### Test Combined Filters
1. Enter a search term
2. Select a specific course
3. Choose a date
4. Results should match ALL criteria

### Test Clear Functionality
1. Apply some search terms and filters
2. Click "Clear All" button
3. All filters should reset and show all attempts

## Troubleshooting

### Plugin Not Appearing
- Check that Tutor LMS is installed and activated first
- Verify the plugin files are in the correct directory
- Check WordPress admin for any error messages

### Search Not Working
- Ensure you have quiz attempts in your courses
- Verify you're logged in as an instructor
- Check that students have actually taken quizzes

### Styling Issues
- Clear browser cache
- Check for theme conflicts
- Ensure no other plugins are overriding Tutor LMS styles

### Template Not Loading
- Verify file permissions on the plugin directory
- Check WordPress debug log for PHP errors
- Ensure Tutor LMS is up to date

## Features Overview

### Search Capabilities
- **Student Names**: First name, last name, or display name
- **Email Addresses**: Full or partial email matching
- **Quiz Titles**: Search by quiz name
- **Course Titles**: Search by course name

### User Interface
- **Responsive Design**: Works on desktop, tablet, and mobile
- **Keyboard Shortcuts**: Ctrl/Cmd+K to focus search
- **Real-time Search**: Optional as-you-type search
- **Visual Feedback**: Loading states and result counts

### Integration
- **No Core Changes**: Uses Tutor LMS hooks and filters
- **Backward Compatible**: Works with existing functionality
- **Update Safe**: Won't break when Tutor LMS updates
- **Performance Optimized**: Uses existing database queries

## Configuration Options

### Enable Real-time Search
Edit `assets/js/advanced-search.js`:
```javascript
config: {
    enableRealTimeSearch: true,  // Change to true
    searchDelay: 500,           // Milliseconds to wait
    minSearchLength: 2          // Minimum characters
}
```

### Customize Search Delay
```javascript
config: {
    searchDelay: 300  // Faster response
}
```

## Support

If you encounter any issues:

1. **Check Prerequisites**: Ensure all requirements are met
2. **Review Logs**: Check WordPress debug log for errors
3. **Test Environment**: Try on a staging site first
4. **Documentation**: Review the README.md file
5. **Tutor LMS Docs**: Check official Tutor LMS documentation

## Success Indicators

You'll know the installation is successful when:

✅ Search field appears on Quiz Attempts page  
✅ Search returns filtered results  
✅ Existing filters still work  
✅ Pagination works with search  
✅ Clear buttons reset the interface  
✅ Mobile interface is responsive  

## Next Steps

After successful installation:

1. **Train Instructors**: Show them how to use the new search
2. **Test Thoroughly**: Try various search scenarios
3. **Monitor Performance**: Ensure no slowdowns
4. **Gather Feedback**: Ask instructors for improvement suggestions
5. **Regular Updates**: Keep Tutor LMS and WordPress updated

The advanced search functionality is now ready to use! Instructors can efficiently find specific quiz attempts using student names, emails, or other criteria.
