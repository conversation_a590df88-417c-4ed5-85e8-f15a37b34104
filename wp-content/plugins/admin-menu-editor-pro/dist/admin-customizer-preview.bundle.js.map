{"version": 3, "file": "admin-customizer-preview.bundle.js", "mappings": ";;;;;;;;;;;;;AAAa;AAEkE;AACb;AACS;AAI3E,kFAAkF;AAClF,oDAAoD;AAEpD,IAAU,yBAAyB,CAkLlC;AAlLD,WAAU,yBAAyB;IAClC,IAAO,wBAAwB,GAAG,+GAAwC,CAAC;IAC3E,MAAM,CAAC,GAAG,MAAM,CAAC;IAMjB,MAAa,cAAe,SAAQ,iGAA0C;QAO7E,YAAY,UAA6B;YACxC,KAAK,CAAC,UAAU,CAAC,CAAC;YAHF,yBAAoB,GAAwB,EAAE,CAAC;YAI/D,IAAI,CAAC,aAAa,GAAG,UAAU,CAAC,aAAa,CAAC;YAE9C,IAAI,CAAC,eAAe,GAAG,IAAI,wBAAwB,CAClD,CAAC,SAAiB,EAAE,aAAkB,EAAE,EAAE;gBACzC,IAAI,IAAI,CAAC,oBAAoB,CAAC,cAAc,CAAC,SAAS,CAAC,EAAE;oBACxD,OAAO,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;iBAC5C;gBACD,2EAA2E;gBAC3E,IACC,UAAU,CAAC,QAAQ,CAAC,cAAc,CAAC,SAAS,CAAC;uBAC1C,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,OAAO,CAAC,EACxD;oBACD,OAAO,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC;iBAC5C;gBACD,OAAO,aAAa,CAAC;YACtB,CAAC,CACD,CAAC;YAEF,IAAI,CAAC,UAAU,GAAG,iBAAiB,CAAC,eAAe,CAClD;gBACC,gBAAgB,EAAE,CAAC,SAAiB,EAAE,KAAU,EAAE,EAAE;oBACnD,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;oBAE7C,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE;wBAChD,OAAO,KAAK,CAAC;qBACb;oBACD,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;oBAC7C,OAAO,IAAI,CAAC;gBACb,CAAC;gBACD,eAAe,EAAE,GAAG,EAAE;oBACrB,OAAO,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;gBAC7B,CAAC;aACD,EACD,UAAU,CAAC,kBAAkB,EAC7B,UAAU,CAAC,gBAAgB,CAC3B,CAAC;YAEF,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE;gBAClC,IAAI,OAAO,CAAC,KAAK,WAAW,EAAE;oBAC7B,IAAI,OAAO,IAAI,OAAO,CAAC,IAAI,EAAE;wBAC5B,OAAO,CAAC,IAAI,CAAC,qFAAqF,CAAC,CAAC;qBACpG;oBACD,OAAO,CAAC,2BAA2B;iBACnC;gBAED,sEAAsE;gBACtE,2EAA2E;gBAC3E,0EAA0E;gBAC1E,CAAC,CAAC,OAAO,CAAC,yBAAyB,EAAE,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAC5D,CAAC,CAAC,CAAC;YAEH,CAAC,CAAC,GAAG,EAAE;gBACN,IAAI,CAAC,uBAAuB,EAAE,CAAC;gBAE/B,yBAAyB;gBACzB,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,sBAAsB,EAAE,GAAG,EAAE,CAAC,KAAK,EAAE,EAAE;oBAC1D,OAAO,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;gBACpC,CAAC,CAAC,CAAC;gBAEH,uEAAuE;gBACvE,qBAAqB;gBACrB,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,uBAAuB,EAAE,MAAM,EAAE,UAAU,KAAK;oBACnE,KAAK,CAAC,cAAc,EAAE,CAAC;gBACxB,CAAC,CAAC,CAAC;YACJ,CAAC,CAAC,CAAC;YAEH,mEAAmE;YACnE,KAAK,MAAM,aAAa,IAAI,CAAC,UAAU,CAAC,mBAAmB,IAAI,EAAE,CAAC,EAAE;gBACnE,MAAM,eAAe,GAAG,IAAI,gHAA+C,CAAC,aAAa,CAAC,CAAC;gBAC3F,IAAI,CAAC,eAAe,CAAC,sBAAsB,CAC1C,eAAe,CAAC,wBAAwB,EAAE,EAC1C,eAAe,CACf,CAAC;aACF;QACF,CAAC;QAED;;WAEG;QACH,uBAAuB;YACtB,MAAM,IAAI,GAAG,IAAI,CAAC;YAClB,CAAC,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC;gBACjB,MAAM,OAAO,GAAG,IAAI,CAAC;gBACrB,IAAI,CAAC,CAAC,OAAO,YAAY,iBAAiB,CAAC,EAAE;oBAC5C,OAAO;iBACP;gBACD,MAAM,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;gBAEtB,4CAA4C;gBAC5C,IAAI,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE;oBACjC,OAAO;iBACP;gBAED,sCAAsC;gBACtC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,EAAE;oBACrC,KAAK,CAAC,QAAQ,CAAC,wBAAwB,CAAC,CAAC;oBACzC,OAAO;iBACP;gBAED,qCAAqC;gBACrC,MAAM,MAAM,GAAG,IAAI,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;gBACnD,MAAM,CAAC,GAAG,CAAC,gBAAgB,EAAE,GAAG,CAAC,CAAC;gBAClC,MAAM,CAAC,GAAG,CAAC,kBAAkB,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;gBACnD,OAAO,CAAC,MAAM,GAAG,GAAG,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;YAC1C,CAAC,CAAC,CAAC;QACJ,CAAC;QAED,iBAAiB,CAAC,OAA0B;YAC3C,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QACvC,CAAC;QAED,gBAAgB,CAAC,KAAa;YAC7B,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAChC,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;gBAChC,OAAO,KAAK,CAAC;aACb;YACD,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC;QACvC,CAAC;QAED,eAAe,CAAC,KAAwB;YACvC,MAAM,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YAE3C,6BAA6B;YAC7B,IAAI,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE;gBACjC,OAAO;aACP;YAED,+DAA+D;YAC/D,MAAM,aAAa,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAsB,CAAC;YACxD,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,EAAE;gBAC3C,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,OAAO;aACP;YAED,+DAA+D;YAC/D,IAAI,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE;gBAChC,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,eAAe,EAAE,aAAa,CAAC,IAAI,CAAC,CAAC;aAC7D;QACF,CAAC;QAED,4DAA4D;QAC5D,iBAAiB,CAAC,SAAiB,EAAE,QAAiC;YACrE,IAAI,CAAC,eAAe,CAAC,uBAAuB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;QACnE,CAAC;QAED,iEAAiE;QACjE,sBAAsB,CAAC,UAAoB,EAAE,OAAuC;YACnF,IAAI,CAAC,eAAe,CAAC,sBAAsB,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAClE,CAAC;QAED,qCAAqC;QACrC,iBAAiB,CAAC,UAAkB,EAAE,OAA8B;YACnE,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QACnD,CAAC;KACD;IApKY,wCAAc,iBAoK1B;IAED,MAAM,cAAc,GAAG,IAAI,cAAc,CAAC,kBAAkB,CAAC,CAAC;IAC9D,MAAM,CAAC,0BAA0B,CAAC,GAAG,cAAc,CAAC;IAEpD,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,gCAAgC,EAAE,CAAC,cAAc,CAAC,CAAC,CAAC;AACvE,CAAC,EAlLS,yBAAyB,KAAzB,yBAAyB,QAkLlC;;;;;;;;;;;;;;;AC7LY;AACN;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,wDAAwD;AACzD;;;;;;;;;;;;;;ACvCO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,IAAI;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B,IAAI,IAAI,MAAM,IAAI,KAAK,IAAI,QAAQ;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sDAAsD;AACtD,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0CAA0C;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,WAAW,KAAK,SAAS,KAAK,KAAK,KAAK,OAAO;AAC9E;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC,sBAAsB;AACzD,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC,SAAS,IAAI,MAAM,EAAE,MAAM;AAChE;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,sDAAsD,kCAAkC;AACxF,SAAS;AACT;AACA;AACA;AACA,sDAAsD,iCAAiC;AACvF,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yDAAyD,SAAS;AAClE;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wCAAwC;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2CAA2C;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6EAA6E,gBAAgB;AAC7F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,WAAW,gBAAgB;AAC5C;AACA;AACA;AACA;AACA,uBAAuB,iBAAiB;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,WAAW,gBAAgB;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sDAAsD,gBAAgB,EAAE,SAAS;AACjF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kDAAkD,wCAAwC;AAC1F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iDAAiD,kCAAkC;AACnF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA,iBAAiB,WAAW,gBAAgB;AAC5C;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK,wEAAwE;AAC7E,CAAC,8CAA8C;AAC/C", "sources": ["webpack:///./extras/modules/admin-customizer/preview-handler.ts", "webpack:///./extras/modules/admin-customizer/admin-customizer-base.js", "webpack:///./extras/style-generator/style-generator.js"], "sourcesContent": ["'use strict';\n\nimport {AmeCustomizable} from '../../pro-customizables/assets/customizable.js';\nimport {AmeAdminCustomizerBase} from './admin-customizer-base.js';\nimport {AmeStyleGenerator} from '../../style-generator/style-generator.js';\n\ndeclare var wsAmeAcPreviewData: AmeAdminCustomizerPreview.PreviewScriptData;\n\n//Compatibility note: This script is not compatible with IE11 because it uses some\n//modern JS features like the URLSearchParams class.\n\nnamespace AmeAdminCustomizerPreview {\n\timport ThrottledPreviewRegistry = AmeCustomizable.ThrottledPreviewRegistry;\n\tconst $ = jQuery;\n\n\texport interface PreviewScriptData extends AmeAdminCustomizerBase.ScriptData {\n\t\tstylePreviewConfigs?: AmeStyleGenerator.Preview.StyleGeneratorPreviewConfig[];\n\t}\n\n\texport class PreviewHandler extends AmeAdminCustomizerBase.AdminCustomizerBase {\n\t\tprivate readonly changesetName: string;\n\t\tprivate readonly connection: ReturnType<typeof AmeAcCommunicator.connectToParent>;\n\n\t\tprivate readonly previewRegistry: ThrottledPreviewRegistry;\n\t\tprivate readonly currentPreviewValues: Record<string, any> = {};\n\n\t\tconstructor(scriptData: PreviewScriptData) {\n\t\t\tsuper(scriptData);\n\t\t\tthis.changesetName = scriptData.changesetName;\n\n\t\t\tthis.previewRegistry = new ThrottledPreviewRegistry(\n\t\t\t\t(settingId: string, defaultResult: any) => {\n\t\t\t\t\tif (this.currentPreviewValues.hasOwnProperty(settingId)) {\n\t\t\t\t\t\treturn this.currentPreviewValues[settingId];\n\t\t\t\t\t}\n\t\t\t\t\t//Try the script data. It should have the current value from the changeset.\n\t\t\t\t\tif (\n\t\t\t\t\t\tscriptData.settings.hasOwnProperty(settingId)\n\t\t\t\t\t\t&& scriptData.settings[settingId].hasOwnProperty('value')\n\t\t\t\t\t) {\n\t\t\t\t\t\treturn scriptData.settings[settingId].value;\n\t\t\t\t\t}\n\t\t\t\t\treturn defaultResult;\n\t\t\t\t}\n\t\t\t);\n\n\t\t\tthis.connection = AmeAcCommunicator.connectToParent(\n\t\t\t\t{\n\t\t\t\t\t'previewSetting': (settingId: string, value: any) => {\n\t\t\t\t\t\tthis.currentPreviewValues[settingId] = value;\n\n\t\t\t\t\t\tif (!this.previewRegistry.canPreview(settingId)) {\n\t\t\t\t\t\t\treturn false;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tthis.previewRegistry.queuePreview(settingId);\n\t\t\t\t\t\treturn true;\n\t\t\t\t\t},\n\t\t\t\t\t'getCurrentUrl': () => {\n\t\t\t\t\t\treturn window.location.href;\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tscriptData.allowedCommOrigins,\n\t\t\t\tscriptData.isWpDebugEnabled\n\t\t\t);\n\n\t\t\tthis.connection.promise.then((c) => {\n\t\t\t\tif (typeof c === 'undefined') {\n\t\t\t\t\tif (console && console.warn) {\n\t\t\t\t\t\tconsole.warn('Connection succeeded, but the communicator is undefined. This should be impossible.');\n\t\t\t\t\t}\n\t\t\t\t\treturn; //This should never happen.\n\t\t\t\t}\n\n\t\t\t\t//Let the parent know the current URL. The parent might not be able to\n\t\t\t\t//read it due to cross-domain restrictions, and if there are any redirects,\n\t\t\t\t//the actual URL might not match the frame src that was set by the parent.\n\t\t\t\tc.execute('notifyPreviewUrlChanged', window.location.href);\n\t\t\t});\n\n\t\t\t$(() => {\n\t\t\t\tthis.addPreviewParamsToLinks();\n\n\t\t\t\t//Handle clicks on links.\n\t\t\t\t$(document.body).on('click.ame-ac-preview', 'a', (event) => {\n\t\t\t\t\treturn this.handleLinkClick(event);\n\t\t\t\t});\n\n\t\t\t\t//Block form submissions. Theme Customizer supports those, but we don't\n\t\t\t\t//(at least for now).\n\t\t\t\t$(document.body).on('submit.ame-ac-preview', 'form', function (event) {\n\t\t\t\t\tevent.preventDefault();\n\t\t\t\t});\n\t\t\t});\n\n\t\t\t//For convenience, support for StyleGenerator previews is built-in.\n\t\t\tfor (const previewConfig of (scriptData.stylePreviewConfigs || [])) {\n\t\t\t\tconst previewInstance = new AmeStyleGenerator.Preview.StyleGeneratorPreview(previewConfig);\n\t\t\t\tthis.previewRegistry.registerPreviewUpdater(\n\t\t\t\t\tpreviewInstance.getPreviewableSettingIDs(),\n\t\t\t\t\tpreviewInstance\n\t\t\t\t);\n\t\t\t}\n\t\t}\n\n\t\t/**\n\t\t * Add preview-specific query parameters to all links.\n\t\t */\n\t\taddPreviewParamsToLinks() {\n\t\t\tconst self = this;\n\t\t\t$('a[href]').each(function (this: HTMLElement) {\n\t\t\t\tconst element = this;\n\t\t\t\tif (!(element instanceof HTMLAnchorElement)) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tconst $link = $(this);\n\n\t\t\t\t//Don't modify internal anchors like \"#abc\".\n\t\t\t\tif (self.isInternalAnchor($link)) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\t//Flag and skip non-previewable links.\n\t\t\t\tif (!self.isPreviewableLink(element)) {\n\t\t\t\t\t$link.addClass('ame-ac-not-previewable');\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\t//Add the preview query parameter(s).\n\t\t\t\tconst params = new URLSearchParams(element.search);\n\t\t\t\tparams.set('ame-ac-preview', '1');\n\t\t\t\tparams.set('ame-ac-changeset', self.changesetName);\n\t\t\t\telement.search = '?' + params.toString();\n\t\t\t});\n\t\t}\n\n\t\tisPreviewableLink(element: HTMLAnchorElement): boolean {\n\t\t\treturn this.isPreviewableUrl(element);\n\t\t}\n\n\t\tisInternalAnchor($link: JQuery): boolean {\n\t\t\tconst href = $link.attr('href');\n\t\t\tif (typeof href === 'undefined') {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\treturn (href.substring(0, 1) === '#');\n\t\t}\n\n\t\thandleLinkClick(event: JQueryEventObject) {\n\t\t\tconst $link = $(event.target).closest('a');\n\n\t\t\t//Let anchors work as normal.\n\t\t\tif (this.isInternalAnchor($link)) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t//Prevent the browser from navigating to non-previewable links.\n\t\t\tconst anchorElement = $link.get(0) as HTMLAnchorElement;\n\t\t\tif (!this.isPreviewableLink(anchorElement)) {\n\t\t\t\tevent.preventDefault();\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t//Tell the parent (i.e. the admin customizer) to load the link.\n\t\t\tif (this.connection.isConnected) {\n\t\t\t\tevent.preventDefault();\n\t\t\t\tthis.connection.execute('setPreviewUrl', anchorElement.href);\n\t\t\t}\n\t\t}\n\n\t\t// noinspection JSUnusedGlobalSymbols Used in other modules.\n\t\tregisterPreviewer(settingId: string, callback: (newValue: any) => void) {\n\t\t\tthis.previewRegistry.registerPreviewCallback(settingId, callback);\n\t\t}\n\n\t\t// noinspection JSUnusedGlobalSymbols Also used in other modules.\n\t\tregisterPreviewUpdater(settingIds: string[], updater: AmeCustomizable.PreviewUpdater) {\n\t\t\tthis.previewRegistry.registerPreviewUpdater(settingIds, updater);\n\t\t}\n\n\t\t// noinspection JSUnusedGlobalSymbols\n\t\tregisterRpcMethod(methodName: string, handler: (...args: any) => any) {\n\t\t\tthis.connection.addRpcMethod(methodName, handler);\n\t\t}\n\t}\n\n\tconst previewHandler = new PreviewHandler(wsAmeAcPreviewData);\n\twindow['wsAdminCustomizerPreview'] = previewHandler;\n\n\t$('body').trigger('adminMenuEditor:acPreviewStart', [previewHandler]);\n}\n\ndeclare global {\n\tinterface Window {\n\t\twsAdminCustomizerPreview: AmeAdminCustomizerPreview.PreviewHandler;\n\t}\n}", "'use strict';\r\nexport var AmeAdminCustomizerBase;\r\n(function (AmeAdminCustomizerBase) {\r\n    class AdminCustomizerBase {\r\n        constructor(scriptData) {\r\n            this.allowedCommOrigins = scriptData.allowedCommOrigins;\r\n            if (this.allowedCommOrigins.length === 0) {\r\n                this.allowedCommOrigins = [window.location.origin];\r\n            }\r\n            this.allowedPreviewUrls = scriptData.allowedPreviewUrls;\r\n            this.parsedAllowedUrls = this.allowedPreviewUrls.map(url => new URL(url));\r\n        }\r\n        isPreviewableUrl(url) {\r\n            if (typeof url === 'string') {\r\n                url = new URL(url);\r\n            }\r\n            if (typeof url.protocol === 'undefined') {\r\n                return false;\r\n            }\r\n            //Only HTTP(S) links are previewable.\r\n            if ((url.protocol !== 'http:') && (url.protocol !== 'https:')) {\r\n                return false;\r\n            }\r\n            //Check against the list of allowed URLs.\r\n            for (const allowedUrl of this.parsedAllowedUrls) {\r\n                //Protocol and host must match. The path must start with the path\r\n                //of the allowed URL (possibly without a trailing slash).\r\n                if ((url.protocol === allowedUrl.protocol) && (url.host === allowedUrl.host)) {\r\n                    const allowedPath = allowedUrl.pathname.replace(/\\/$/, '');\r\n                    if (url.pathname.indexOf(allowedPath) === 0) {\r\n                        return true;\r\n                    }\r\n                }\r\n            }\r\n            return false;\r\n        }\r\n    }\r\n    AmeAdminCustomizerBase.AdminCustomizerBase = AdminCustomizerBase;\r\n})(AmeAdminCustomizerBase || (AmeAdminCustomizerBase = {}));\r\n//# sourceMappingURL=admin-customizer-base.js.map", "export var AmeStyleGenerator;\r\n(function (AmeStyleGenerator) {\r\n    const $ = jQuery;\r\n    class ValueDescriptor {\r\n    }\r\n    class ConstantValue extends ValueDescriptor {\r\n        constructor(value) {\r\n            super();\r\n            this.value = value;\r\n        }\r\n        getValue() {\r\n            return this.value;\r\n        }\r\n    }\r\n    class ArrayValue extends ValueDescriptor {\r\n        constructor(items) {\r\n            super();\r\n            this.items = items;\r\n        }\r\n        getValue() {\r\n            return this.items.map(item => item.getValue());\r\n        }\r\n        getItemDescriptors() {\r\n            return this.items;\r\n        }\r\n    }\r\n    class SettingReference extends ValueDescriptor {\r\n        constructor(settingId, valueGetter) {\r\n            super();\r\n            this.settingId = settingId;\r\n            this.valueGetter = valueGetter;\r\n        }\r\n        getValue() {\r\n            return this.valueGetter(this.settingId);\r\n        }\r\n    }\r\n    class VariableReference extends ValueDescriptor {\r\n        constructor(name, valueGetter) {\r\n            super();\r\n            this.name = name;\r\n            this.valueGetter = valueGetter;\r\n        }\r\n        getValue() {\r\n            return this.valueGetter(this.name);\r\n        }\r\n    }\r\n    class FunctionCall extends ValueDescriptor {\r\n        constructor(args, callback) {\r\n            super();\r\n            this.args = args;\r\n            this.callback = callback;\r\n        }\r\n        getValue() {\r\n            return this.callback(this.resolveArgs(this.args));\r\n        }\r\n        resolveArgs(args) {\r\n            if (Array.isArray(args)) {\r\n                return args.map(arg => arg.getValue());\r\n            }\r\n            return Object.keys(args).reduce((result, key) => {\r\n                result[key] = args[key].getValue();\r\n                return result;\r\n            }, {});\r\n        }\r\n    }\r\n    //endregion\r\n    function isEmptyCssValue(value) {\r\n        return (typeof value === 'undefined') || (value === '') || (value === null);\r\n    }\r\n    function convertToRgba(color, opacity = 1.0) {\r\n        color = color.trim();\r\n        if (color === '') {\r\n            return 'transparent';\r\n        }\r\n        //Strip the leading hash, if any.\r\n        if (color[0] === '#') {\r\n            color = color.substring(1);\r\n        }\r\n        //If the color is in the shorthand format, expand it.\r\n        if (color.length === 3) {\r\n            color = color[0] + color[0] + color[1] + color[1] + color[2] + color[2];\r\n        }\r\n        //The color should now be in the full 6-digit format. Convert it to RGBA.\r\n        if (color.length === 6) {\r\n            const red = parseInt(color.substring(0, 2), 16);\r\n            const green = parseInt(color.substring(2, 4), 16);\r\n            const blue = parseInt(color.substring(4, 6), 16);\r\n            return `rgba(${red}, ${green}, ${blue}, ${opacity})`;\r\n        }\r\n        //The color may be invalid, or it's not in a hex format we recognize.\r\n        return color;\r\n    }\r\n    function uniqueArrayValues(array) {\r\n        return array.filter((value, index) => array.indexOf(value) === index);\r\n    }\r\n    function constrain(value, min, max) {\r\n        return Math.min(Math.max(value, min), max);\r\n    }\r\n    function modifyHexColorAsHsl(args, operation) {\r\n        const color = args.color || '';\r\n        if (isEmptyCssValue(color)) {\r\n            return '';\r\n        }\r\n        const hue = args.hue || null;\r\n        const saturation = args.saturation || null;\r\n        const lightness = args.lightness || null;\r\n        if ((hue === null) && (saturation === null) && (lightness === null)) {\r\n            return color;\r\n        }\r\n        let output = $.Color(color);\r\n        output = operation(output, hue, saturation, lightness);\r\n        return output.toHexString();\r\n    }\r\n    // noinspection JSUnusedGlobalSymbols -- Used dynamically by declaration generators received from the server.\r\n    const builtinFunctions = {\r\n        simpleProperty: function (args) {\r\n            if (isEmptyCssValue(args.value)) {\r\n                return [];\r\n            }\r\n            return [args.name + ': ' + args.value + ';'];\r\n        },\r\n        formatLength: function (args) {\r\n            if (isEmptyCssValue(args.value)) {\r\n                return '';\r\n            }\r\n            //Normalize numeric values. For example, while JS accepts \"1.\" as a number,\r\n            //\"1.px\" is not a valid CSS length value, so it should be converted to \"1px\".\r\n            const numericValue = parseFloat(String(args.value));\r\n            if (isNaN(numericValue)) {\r\n                return '';\r\n            }\r\n            return '' + numericValue + (args.unit || '');\r\n        },\r\n        shadow: function (args) {\r\n            const mode = args.mode || 'default';\r\n            const color = args.color || '';\r\n            if (mode === 'default') {\r\n                return [];\r\n            }\r\n            if ((mode === 'none') || (color === '') || (color === null) || (color === 'transparent')) {\r\n                return ['box-shadow: none;'];\r\n            }\r\n            if (mode !== 'custom') {\r\n                return [];\r\n            }\r\n            const components = [];\r\n            if (args.inset) {\r\n                components.push('inset');\r\n            }\r\n            const horizontal = args['offset-x'] || 0;\r\n            const vertical = args['offset-y'] || 0;\r\n            const blur = args.blur || 0;\r\n            const spread = args.spread || 0;\r\n            components.push(`${horizontal}px ${vertical}px ${blur}px ${spread}px`);\r\n            const colorOpacity = args.colorOpacity || 1.0;\r\n            if (colorOpacity < 1.0) {\r\n                components.push(convertToRgba(color, colorOpacity));\r\n            }\r\n            else {\r\n                components.push(color);\r\n            }\r\n            return [`box-shadow: ${components.join(' ')};`];\r\n        },\r\n        boxSides: function (args) {\r\n            if (typeof args.cssPropertyPrefix !== 'string') {\r\n                throw new Error('Invalid config for the boxSides generator: missing cssPropertyPrefix');\r\n            }\r\n            const compositeValue = args.value || {};\r\n            const unit = compositeValue.unit || '';\r\n            const declarations = [];\r\n            for (const side of ['top', 'right', 'bottom', 'left']) {\r\n                const value = compositeValue[side];\r\n                if (isEmptyCssValue(value)) {\r\n                    continue;\r\n                }\r\n                const property = args.cssPropertyPrefix + side;\r\n                declarations.push(`${property}: ${value}${unit};`);\r\n            }\r\n            return declarations;\r\n        },\r\n        firstNonEmpty(args) {\r\n            for (const arg of args) {\r\n                if (!isEmptyCssValue(arg)) {\r\n                    return arg;\r\n                }\r\n            }\r\n            return null;\r\n        },\r\n        /**\r\n         * Take a HEX color, convert it to HSL to edit its components,\r\n         * then convert back to HEX.\r\n         *\r\n         * @param args\r\n         */\r\n        editHexAsHsl: function (args) {\r\n            return modifyHexColorAsHsl(args, (color, hue, saturation, lightness) => {\r\n                if (hue !== null) {\r\n                    color = color.hue(hue);\r\n                }\r\n                if (saturation !== null) {\r\n                    color = color.saturation(saturation);\r\n                }\r\n                if (lightness !== null) {\r\n                    color = color.lightness(lightness);\r\n                }\r\n                return color;\r\n            });\r\n        },\r\n        adjustHexAsHsl: function (args) {\r\n            return modifyHexColorAsHsl(args, (color, hue, saturation, lightness) => {\r\n                if (hue !== null) {\r\n                    color = color.hue(constrain(color.hue() + hue, 0, 360));\r\n                }\r\n                if (saturation !== null) {\r\n                    color = color.saturation(constrain(color.saturation() + saturation, 0, 1.0));\r\n                }\r\n                if (lightness !== null) {\r\n                    color = color.lightness(constrain(color.lightness() + lightness, 0, 1.0));\r\n                }\r\n                return color;\r\n            });\r\n        },\r\n        mixColors: function (args) {\r\n            const color1 = args.color1 || '';\r\n            const color2 = args.color2 || '';\r\n            if (isEmptyCssValue(color1) || isEmptyCssValue(color2)) {\r\n                return '';\r\n            }\r\n            const weight = args.weight || 50;\r\n            if (weight <= 0) {\r\n                return color2;\r\n            }\r\n            else if (weight >= 100) {\r\n                return color1;\r\n            }\r\n            return $.Color(color2).transition($.Color(color1), weight / 100).toHexString();\r\n        },\r\n        changeLightness: function (args) {\r\n            const color = args.color || '';\r\n            if (isEmptyCssValue(color)) {\r\n                return '';\r\n            }\r\n            const amount = args.amount || 0;\r\n            if (amount === 0) {\r\n                return color;\r\n            }\r\n            let output = $.Color(color);\r\n            //Amount is a number between 0 and 100, while lightness is between 0.0 and 1.0.\r\n            let newLightness = output.lightness() + (amount / 100);\r\n            //Clamp to 0.0 - 1.0.\r\n            newLightness = constrain(newLightness, 0.0, 1.0);\r\n            return output.lightness(newLightness).toHexString();\r\n        },\r\n        darken: function (args) {\r\n            const color = args.color || '';\r\n            const amount = args.amount || 0;\r\n            return builtinFunctions.changeLightness({ color, amount: -Math.abs(amount) });\r\n        },\r\n        lighten: function (args) {\r\n            const color = args.color || '';\r\n            const amount = args.amount || 0;\r\n            return builtinFunctions.changeLightness({ color, amount: Math.abs(amount) });\r\n        },\r\n        compare: function (args) {\r\n            const value1 = args.value1;\r\n            const value2 = args.value2;\r\n            const operator = args.op;\r\n            const thenResult = (typeof args.thenResult !== 'undefined') ? args.thenResult : true;\r\n            const elseResult = (typeof args.elseResult !== 'undefined') ? args.elseResult : null;\r\n            let result;\r\n            switch (operator) {\r\n                case '==':\r\n                    result = value1 == value2;\r\n                    break;\r\n                case '!=':\r\n                    result = value1 != value2;\r\n                    break;\r\n                case '>':\r\n                    result = value1 > value2;\r\n                    break;\r\n                case '>=':\r\n                    result = value1 >= value2;\r\n                    break;\r\n                case '<':\r\n                    result = value1 < value2;\r\n                    break;\r\n                case '<=':\r\n                    result = value1 <= value2;\r\n                    break;\r\n                default:\r\n                    throw new Error(`Unknown operator: ${operator}`);\r\n            }\r\n            return result ? thenResult : elseResult;\r\n        },\r\n        ifTruthy: function (args) {\r\n            const value = args.value;\r\n            const thenResult = (typeof args.thenResult !== 'undefined') ? args.thenResult : true;\r\n            const elseResult = (typeof args.elseResult !== 'undefined') ? args.elseResult : null;\r\n            return value ? thenResult : elseResult;\r\n        },\r\n        ifSome: function (args) {\r\n            const values = args.values;\r\n            const thenResult = args.thenResult;\r\n            const elseResult = (typeof args.elseResult !== 'undefined') ? args.elseResult : null;\r\n            for (const value of values) {\r\n                if (!!value) {\r\n                    return thenResult;\r\n                }\r\n            }\r\n            return elseResult;\r\n        },\r\n        ifAll: function (args) {\r\n            const values = args.values;\r\n            const thenResult = args.thenResult;\r\n            const elseResult = args.elseResult !== undefined ? args.elseResult : null;\r\n            if (!values || (values.length === 0)) {\r\n                return elseResult;\r\n            }\r\n            for (const value of values) {\r\n                if (!value) {\r\n                    return elseResult;\r\n                }\r\n            }\r\n            return thenResult;\r\n        },\r\n        ifImageSettingContainsImage: function (args) {\r\n            const thenResult = args.thenResult !== undefined ? args.thenResult : true;\r\n            const elseResult = args.elseResult !== undefined ? args.elseResult : null;\r\n            if ((typeof args.value !== 'object') || !args.value) {\r\n                return elseResult;\r\n            }\r\n            const image = args.value;\r\n            const hasAttachment = !!image.attachmentId;\r\n            const hasExternalUrl = !!image.externalUrl;\r\n            const hasImage = hasAttachment || hasExternalUrl;\r\n            return hasImage ? thenResult : elseResult;\r\n        }\r\n    };\r\n    let Preview;\r\n    (function (Preview) {\r\n        const $ = jQuery;\r\n        function isConditionalAtRuleConfig(config) {\r\n            if ((typeof config !== 'object') || (config === null)) {\r\n                return false;\r\n            }\r\n            const configAsRecord = config;\r\n            return ((typeof configAsRecord['t'] === 'string')\r\n                && (configAsRecord['t'] === 'conditionalAtRule')\r\n                && (typeof configAsRecord['identifier'] === 'string'));\r\n        }\r\n        function isRuleSetConfig(config) {\r\n            return ((config !== null)\r\n                && (Array.isArray(config['selectors']))\r\n                && (Array.isArray(config['generators'])));\r\n        }\r\n        const inactiveSettingMarker = { '_ame_inactive_setting': true };\r\n        class PreviewSession {\r\n            constructor(config) {\r\n                this.settings = {};\r\n                this.valueReaders = new Set();\r\n                this.notFound = {};\r\n                this.variables = {};\r\n                this.styleBlocks = [];\r\n                this.stylesheetsToDisable = [];\r\n                this.stylesheetWasEnabled = {};\r\n                /**\r\n                 * Whether this is the first time the preview is being updated.\r\n                 * This is set to false after preview() is called for the first time.\r\n                 */\r\n                this._isBeforeFirstUpdate = true;\r\n                //Optimization: Create bound getters once instead of every time we need\r\n                //to create a setting or variable reference.\r\n                this.settingValueGetter = this.getSettingPreviewValue.bind(this);\r\n                this.variableValueGetter = (variableName) => {\r\n                    if (variableName in this.variables) {\r\n                        return this.variables[variableName].getValue();\r\n                    }\r\n                    return null;\r\n                };\r\n                //Optionally, disable already generated custom stylesheets while the preview\r\n                //is active to prevent old settings from interfering with the preview of new settings.\r\n                if (Array.isArray(config.stylesheetsToDisable)) {\r\n                    this.stylesheetsToDisable = config.stylesheetsToDisable;\r\n                }\r\n                //Variables\r\n                for (const variableName in config.variables) {\r\n                    if (!config.variables.hasOwnProperty(variableName)) {\r\n                        continue;\r\n                    }\r\n                    this.variables[variableName] = this.createValueDescriptor(config.variables[variableName], true);\r\n                }\r\n                //CSS statement groups\r\n                for (const conditionConfig of config.statementGroups) {\r\n                    const statements = this.createCssStatements(conditionConfig.statements);\r\n                    if (statements.length < 1) {\r\n                        continue;\r\n                    }\r\n                    const condition = this.createValueDescriptor(conditionConfig.expression, true);\r\n                    const usedSettingIds = this.getSettingIdsUsedBy(condition);\r\n                    const conditionCallback = () => {\r\n                        //For performance, conditions that reference settings should\r\n                        //only be checked when at least one setting is active.\r\n                        if (usedSettingIds.length > 0) {\r\n                            if (!usedSettingIds.some((id) => this.isSettingActive(id))) {\r\n                                return false;\r\n                            }\r\n                        }\r\n                        const isTruthy = condition.getValue();\r\n                        return !!isTruthy; //Convert to boolean.\r\n                    };\r\n                    this.styleBlocks.push(new PreviewStyleBlock(statements, conditionCallback));\r\n                }\r\n            }\r\n            createValueDescriptor(data, allowUnknownVariables = false) {\r\n                switch (data.t) {\r\n                    case 'constant':\r\n                        return new ConstantValue(data.value);\r\n                    case 'array':\r\n                        return new ArrayValue(data.items.map((valueData) => this.createValueDescriptor(valueData, allowUnknownVariables)));\r\n                    case 'setting':\r\n                        this.registerPreviewableSettingId(data.id);\r\n                        return new SettingReference(data.id, this.settingValueGetter);\r\n                    case 'var':\r\n                        if (!this.variables.hasOwnProperty(data.name) && !allowUnknownVariables) {\r\n                            throw new Error('Unknown variable: ' + data.name);\r\n                        }\r\n                        return new VariableReference(data.name, this.variableValueGetter);\r\n                    case 'funcCall':\r\n                        let functionName;\r\n                        if (data.name in builtinFunctions) {\r\n                            functionName = data.name;\r\n                        }\r\n                        else {\r\n                            throw new Error('Unknown function: ' + data.name);\r\n                        }\r\n                        const func = builtinFunctions[functionName];\r\n                        //Initialize the function arguments.\r\n                        let args;\r\n                        if (Array.isArray(data.args)) {\r\n                            args = data.args.map(arg => this.createValueDescriptor(arg, allowUnknownVariables));\r\n                        }\r\n                        else {\r\n                            args = {};\r\n                            for (const argName in data.args) {\r\n                                if (!data.args.hasOwnProperty(argName)) {\r\n                                    continue;\r\n                                }\r\n                                args[argName] = this.createValueDescriptor(data.args[argName], allowUnknownVariables);\r\n                            }\r\n                        }\r\n                        // @ts-ignore - Can't really statically check this since the values come from the server.\r\n                        return new FunctionCall(args, func);\r\n                }\r\n            }\r\n            /**\r\n             * Get the IDs of all settings that are referenced by the given descriptor.\r\n             *\r\n             * @param descriptor\r\n             * @private\r\n             */\r\n            getSettingIdsUsedBy(descriptor) {\r\n                if (descriptor instanceof SettingReference) {\r\n                    return [descriptor.settingId];\r\n                }\r\n                if (descriptor instanceof ArrayValue) {\r\n                    let result = [];\r\n                    for (const item of descriptor.getItemDescriptors()) {\r\n                        result = result.concat(this.getSettingIdsUsedBy(item));\r\n                    }\r\n                    return uniqueArrayValues(result);\r\n                }\r\n                if (descriptor instanceof FunctionCall) {\r\n                    let result = [];\r\n                    const args = descriptor.args;\r\n                    if (Array.isArray(args)) {\r\n                        for (const arg of args) {\r\n                            result = result.concat(this.getSettingIdsUsedBy(arg));\r\n                        }\r\n                    }\r\n                    else {\r\n                        for (const argName in args) {\r\n                            if (args.hasOwnProperty(argName)) {\r\n                                result = result.concat(this.getSettingIdsUsedBy(args[argName]));\r\n                            }\r\n                        }\r\n                    }\r\n                    return uniqueArrayValues(result);\r\n                }\r\n                if (descriptor instanceof VariableReference) {\r\n                    const varDef = this.getVariableDefinition(descriptor.name);\r\n                    if (varDef === null) {\r\n                        return [];\r\n                    }\r\n                    return this.getSettingIdsUsedBy(varDef);\r\n                }\r\n                return [];\r\n            }\r\n            getVariableDefinition(variableName) {\r\n                if (!this.variables.hasOwnProperty(variableName)) {\r\n                    return null;\r\n                }\r\n                return this.variables[variableName];\r\n            }\r\n            createCssStatements(configs) {\r\n                let results = [];\r\n                for (const config of configs) {\r\n                    if (isRuleSetConfig(config)) {\r\n                        results.push(this.createRuleSetFromConfig(config));\r\n                    }\r\n                    else if (isConditionalAtRuleConfig(config)) {\r\n                        results.push(new ConditionalAtRule(config.identifier, config.condition, (typeof config.nestedStatements === 'undefined')\r\n                            ? []\r\n                            : this.createCssStatements(config.nestedStatements)));\r\n                    }\r\n                    else {\r\n                        console.error('Unknown CSS statement type: ', config);\r\n                    }\r\n                }\r\n                return results;\r\n            }\r\n            createRuleSetFromConfig(config, parent = null) {\r\n                const generatorWrappers = this.makeGeneratorWrappers(config.generators);\r\n                const ruleSet = new CssRuleSet(config.selectors, generatorWrappers, parent);\r\n                const nestedRuleSets = this.createNestedRuleSets(config.nestedStatements, ruleSet);\r\n                ruleSet.setNestedRuleSets(nestedRuleSets);\r\n                return ruleSet;\r\n            }\r\n            createNestedRuleSets(configs, parent = null) {\r\n                let results = [];\r\n                if (!configs) {\r\n                    return results;\r\n                }\r\n                for (const config of configs) {\r\n                    if (!isRuleSetConfig(config)) {\r\n                        throw new Error('A CSS rule set can only contain other rule sets, not other types of statements.');\r\n                    }\r\n                    results.push(this.createRuleSetFromConfig(config, parent));\r\n                }\r\n                return results;\r\n            }\r\n            getPreviewableSettingIDs() {\r\n                return Object.keys(this.settings);\r\n            }\r\n            preview(settingId, value, otherSettingReader) {\r\n                if (this._isBeforeFirstUpdate) {\r\n                    this._isBeforeFirstUpdate = false;\r\n                    this.disableAssociatedStylesheets();\r\n                }\r\n                this.valueReaders.add(otherSettingReader);\r\n                if (!this.settings.hasOwnProperty(settingId)) {\r\n                    this.settings[settingId] = ko.observable(value);\r\n                }\r\n                else {\r\n                    this.settings[settingId](value);\r\n                }\r\n            }\r\n            dispose() {\r\n                //Dispose of all style blocks.\r\n                for (const block of this.styleBlocks) {\r\n                    block.dispose();\r\n                }\r\n                this.reEnableAssociatedStylesheets();\r\n            }\r\n            disableAssociatedStylesheets() {\r\n                for (const stylesheetSelector of this.stylesheetsToDisable) {\r\n                    const $link = $(stylesheetSelector);\r\n                    if ($link.length > 0) {\r\n                        this.stylesheetWasEnabled[stylesheetSelector] = $link.prop('disabled');\r\n                        $link.prop('disabled', true);\r\n                    }\r\n                }\r\n            }\r\n            reEnableAssociatedStylesheets() {\r\n                for (const stylesheetSelector of this.stylesheetsToDisable) {\r\n                    const $link = $(stylesheetSelector);\r\n                    if (($link.length > 0) && this.stylesheetWasEnabled.hasOwnProperty(stylesheetSelector)) {\r\n                        $link.prop('disabled', this.stylesheetWasEnabled[stylesheetSelector]);\r\n                    }\r\n                }\r\n            }\r\n            isSettingActive(settingId) {\r\n                if (this.settings.hasOwnProperty(settingId)) {\r\n                    return this.settings[settingId]() !== inactiveSettingMarker;\r\n                }\r\n                return false;\r\n            }\r\n            getSettingPreviewValue(settingId) {\r\n                if (!this.settings.hasOwnProperty(settingId)) {\r\n                    const value = this.getSettingFromReaders(settingId);\r\n                    this.settings[settingId] = ko.observable(value).extend({ deferred: true });\r\n                }\r\n                const observable = this.settings[settingId];\r\n                let value = observable();\r\n                if (value === inactiveSettingMarker) {\r\n                    value = this.getSettingFromReaders(settingId);\r\n                    observable(value);\r\n                }\r\n                return value;\r\n            }\r\n            getSettingFromReaders(settingId) {\r\n                for (const reader of this.valueReaders) {\r\n                    const value = reader(settingId, this.notFound);\r\n                    if (value !== this.notFound) {\r\n                        return value;\r\n                    }\r\n                }\r\n                throw new Error('Setting not found for preview: ' + settingId);\r\n            }\r\n            makeGeneratorWrappers(generatorConfigs) {\r\n                let generatorWrappers = [];\r\n                for (const generatorConfig of generatorConfigs) {\r\n                    const wrapper = this.makeDeclarationGeneratorWrapper(generatorConfig);\r\n                    if (wrapper !== null) {\r\n                        generatorWrappers.push(wrapper);\r\n                    }\r\n                }\r\n                return generatorWrappers;\r\n            }\r\n            makeDeclarationGeneratorWrapper(config) {\r\n                const generator = this.createValueDescriptor(config);\r\n                return new DeclarationGeneratorWrapper(generator, this);\r\n            }\r\n            registerPreviewableSettingId(settingId) {\r\n                if (!this.settings.hasOwnProperty(settingId)) {\r\n                    this.settings[settingId] = ko.observable(inactiveSettingMarker);\r\n                }\r\n            }\r\n            get isBeforeFirstUpdate() {\r\n                return this._isBeforeFirstUpdate;\r\n            }\r\n        }\r\n        /**\r\n         * Preview manager for the style generator.\r\n         *\r\n         * This is a thin wrapper around the PreviewSession class. It initializes the session\r\n         * as needed and destroys it when the preview is cleared. This makes it simpler to manage\r\n         * active settings, style blocks, and CSS rule-sets: instead of having to carefully\r\n         * track dependencies and deactivate/reactivate them in the right order whenever the preview\r\n         * is disabled/enabled, we can just destroy the session and start over.\r\n         */\r\n        class StyleGeneratorPreview {\r\n            constructor(config) {\r\n                this.config = config;\r\n                this.currentSession = null;\r\n            }\r\n            getOrCreateSession() {\r\n                if (this.currentSession === null) {\r\n                    this.currentSession = new PreviewSession(this.config);\r\n                }\r\n                return this.currentSession;\r\n            }\r\n            getPreviewableSettingIDs() {\r\n                return this.getOrCreateSession().getPreviewableSettingIDs();\r\n            }\r\n            preview(settingId, value, otherSettingReader) {\r\n                const session = this.getOrCreateSession();\r\n                const shouldPreviewAll = (this.config.previewAllOnFirstUpdate && session.isBeforeFirstUpdate);\r\n                session.preview(settingId, value, otherSettingReader);\r\n                if (shouldPreviewAll) {\r\n                    //Preview all registered settings the first time the preview is updated.\r\n                    const notFound = {};\r\n                    for (const otherId of session.getPreviewableSettingIDs()) {\r\n                        const otherValue = otherSettingReader(otherId, notFound);\r\n                        if ((otherId !== settingId) && (otherValue !== notFound)) {\r\n                            session.preview(otherId, otherValue, otherSettingReader);\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n            clearPreview() {\r\n                if (this.currentSession !== null) {\r\n                    this.currentSession.dispose();\r\n                    this.currentSession = null;\r\n                }\r\n            }\r\n        }\r\n        Preview.StyleGeneratorPreview = StyleGeneratorPreview;\r\n        class DeclarationGeneratorWrapper {\r\n            constructor(generator, settingSource) {\r\n                this.generator = generator;\r\n                this.settingSource = settingSource;\r\n                //Introspect the generator and see which settings it uses.\r\n                //This will be useful to determine if the generator is active.\r\n                this.usedSettingIds = DeclarationGeneratorWrapper.findReferencedSettingIds(generator, settingSource);\r\n                this.cssDeclarations = ko.computed({\r\n                    read: () => this.getDeclarations(),\r\n                    deferEvaluation: true,\r\n                }).extend({ deferred: true });\r\n            }\r\n            /**\r\n             * Recursively find all settings used by a value descriptor (such as a function call).\r\n             *\r\n             * @param {ValueDescriptor} thing\r\n             * @param variableSource Needed to get variable definitions and not just the final values.\r\n             */\r\n            static findReferencedSettingIds(thing, variableSource) {\r\n                let settingIds = [];\r\n                if (thing instanceof SettingReference) {\r\n                    settingIds.push(thing.settingId);\r\n                }\r\n                else if (thing instanceof FunctionCall) {\r\n                    if (Array.isArray(thing.args)) {\r\n                        for (const arg of thing.args) {\r\n                            settingIds = settingIds.concat(DeclarationGeneratorWrapper.findReferencedSettingIds(arg, variableSource));\r\n                        }\r\n                    }\r\n                    else {\r\n                        for (const key in thing.args) {\r\n                            settingIds = settingIds.concat(DeclarationGeneratorWrapper.findReferencedSettingIds(thing.args[key], variableSource));\r\n                        }\r\n                    }\r\n                }\r\n                else if (thing instanceof VariableReference) {\r\n                    const value = variableSource.getVariableDefinition(thing.name);\r\n                    if (value !== null) {\r\n                        settingIds = settingIds.concat(DeclarationGeneratorWrapper.findReferencedSettingIds(value, variableSource));\r\n                    }\r\n                }\r\n                return settingIds;\r\n            }\r\n            isActive() {\r\n                //Check if any of the input settings are active.\r\n                let hasSettingLookups = false;\r\n                for (const settingId of this.usedSettingIds) {\r\n                    hasSettingLookups = true;\r\n                    if (this.settingSource.isSettingActive(settingId)) {\r\n                        return true;\r\n                    }\r\n                }\r\n                //If there are no input settings, the generator is always active: it just\r\n                //generates a fixed declaration.\r\n                return !hasSettingLookups;\r\n            }\r\n            getDeclarations() {\r\n                return this.generator.getValue();\r\n            }\r\n            dispose() {\r\n                this.cssDeclarations.dispose();\r\n            }\r\n        }\r\n        class CssStatement {\r\n            constructor() {\r\n                this.cssText = ko.computed({\r\n                    read: () => this.generateCss(),\r\n                    deferEvaluation: true,\r\n                }).extend({ deferred: true });\r\n            }\r\n            dispose() {\r\n                //Dispose the CSS text observable.\r\n                this.cssText.dispose();\r\n            }\r\n        }\r\n        class CssRuleSet extends CssStatement {\r\n            constructor(selectors, declarationSources, parent = null) {\r\n                super();\r\n                this.declarationSources = declarationSources;\r\n                this.nestedRuleSets = ko.observableArray([]);\r\n                if (parent === null) {\r\n                    this.effectiveSelectors = selectors;\r\n                }\r\n                else {\r\n                    this.effectiveSelectors = CssRuleSet.combineSelectors(selectors, parent.effectiveSelectors);\r\n                }\r\n                this.selectorText = this.effectiveSelectors.join(', ');\r\n            }\r\n            static combineSelectors(selectors, parentSelectors) {\r\n                const combinedSelectors = [];\r\n                for (const selector of selectors) {\r\n                    if (selector === '') {\r\n                        continue;\r\n                    }\r\n                    if (selector.includes('&')) {\r\n                        //Insert the parent selectors into the current selector at the position of the \"&\".\r\n                        for (const parentSelector of parentSelectors) {\r\n                            combinedSelectors.push(selector.replace('&', parentSelector.trim()));\r\n                        }\r\n                    }\r\n                    else {\r\n                        //Just append the current selector to the parent selectors.\r\n                        for (const parentSelector of parentSelectors) {\r\n                            combinedSelectors.push(`${parentSelector} ${selector}`);\r\n                        }\r\n                    }\r\n                }\r\n                return combinedSelectors;\r\n            }\r\n            setNestedRuleSets(ruleSets) {\r\n                //Dispose the old rule sets that are not part of the new list.\r\n                for (const oldRuleSet of this.nestedRuleSets()) {\r\n                    if (ruleSets.indexOf(oldRuleSet) === -1) {\r\n                        oldRuleSet.dispose();\r\n                    }\r\n                }\r\n                this.nestedRuleSets(ruleSets);\r\n            }\r\n            generateCss() {\r\n                const declarations = this.getDeclarations();\r\n                const nestedRuleSetParts = [];\r\n                for (const ruleSet of this.nestedRuleSets()) {\r\n                    if (ruleSet.isActive()) {\r\n                        nestedRuleSetParts.push(ruleSet.cssText());\r\n                    }\r\n                }\r\n                let css = '';\r\n                if (declarations.length > 0) {\r\n                    css += this.selectorText + ' {\\n\\t' + declarations.join('\\n\\t') + '\\n}\\n';\r\n                }\r\n                if (nestedRuleSetParts.length > 0) {\r\n                    css += nestedRuleSetParts.join('\\n');\r\n                }\r\n                return css;\r\n            }\r\n            isActive() {\r\n                for (const source of this.declarationSources) {\r\n                    if (source.isActive()) {\r\n                        return true;\r\n                    }\r\n                }\r\n                for (const ruleSet of this.nestedRuleSets()) {\r\n                    if (ruleSet.isActive()) {\r\n                        return true;\r\n                    }\r\n                }\r\n                return false;\r\n            }\r\n            getDeclarations() {\r\n                const declarations = [];\r\n                for (const source of this.declarationSources) {\r\n                    if (source.isActive()) {\r\n                        declarations.push(...source.cssDeclarations());\r\n                    }\r\n                }\r\n                return declarations;\r\n            }\r\n            dispose() {\r\n                //Dispose declaration sources.\r\n                for (const source of this.declarationSources) {\r\n                    source.dispose();\r\n                }\r\n                //Dispose nested rule sets.\r\n                for (const ruleSet of this.nestedRuleSets()) {\r\n                    ruleSet.dispose();\r\n                }\r\n                super.dispose();\r\n            }\r\n        }\r\n        class ConditionalAtRule extends CssStatement {\r\n            constructor(identifier, condition, nestedStatements) {\r\n                super();\r\n                this.identifier = identifier;\r\n                this.condition = condition;\r\n                this.nestedStatements = nestedStatements;\r\n            }\r\n            generateCss() {\r\n                const pieces = [];\r\n                for (const statement of this.nestedStatements) {\r\n                    const css = statement.cssText();\r\n                    if (css !== '') {\r\n                        pieces.push(css);\r\n                    }\r\n                }\r\n                if (pieces.length === 0) {\r\n                    return '';\r\n                }\r\n                return this.getAtRuleText() + ' {\\n\\t' + pieces.join('\\n\\t') + '\\n}';\r\n            }\r\n            getAtRuleText() {\r\n                return '@' + this.identifier + ' ' + this.condition;\r\n            }\r\n            isActive() {\r\n                for (const statement of this.nestedStatements) {\r\n                    if (statement.isActive()) {\r\n                        return true;\r\n                    }\r\n                }\r\n                return false;\r\n            }\r\n            dispose() {\r\n                //Dispose nested statements.\r\n                for (const statement of this.nestedStatements) {\r\n                    statement.dispose();\r\n                }\r\n                super.dispose();\r\n            }\r\n        }\r\n        class PreviewStyleBlock {\r\n            constructor(statements, condition = null) {\r\n                this.statements = statements;\r\n                this.condition = condition;\r\n                this.$styleElement = null;\r\n                this.cssText = ko.computed({\r\n                    read: () => {\r\n                        if ((condition !== null) && !condition()) {\r\n                            return '';\r\n                        }\r\n                        let pieces = [];\r\n                        for (const statement of this.statements) {\r\n                            if (statement.isActive()) {\r\n                                const css = statement.cssText();\r\n                                if (css !== '') {\r\n                                    pieces.push(css);\r\n                                }\r\n                            }\r\n                        }\r\n                        if (pieces.length === 0) {\r\n                            return '';\r\n                        }\r\n                        return pieces.join('\\n');\r\n                    },\r\n                    deferEvaluation: true,\r\n                }).extend({ deferred: true });\r\n                this.updateStyleElement(this.cssText());\r\n                this.cssChangeSubscription = this.cssText.subscribe((cssText) => {\r\n                    this.updateStyleElement(cssText);\r\n                });\r\n            }\r\n            updateStyleElement(cssText) {\r\n                if (cssText === '') {\r\n                    if (this.$styleElement) {\r\n                        this.$styleElement.remove();\r\n                        this.$styleElement = null;\r\n                    }\r\n                    return;\r\n                }\r\n                if (!this.$styleElement) {\r\n                    this.$styleElement = $('<style></style>').appendTo('head');\r\n                }\r\n                this.$styleElement.text(cssText);\r\n            }\r\n            clear() {\r\n                if (this.$styleElement) {\r\n                    this.$styleElement.remove();\r\n                    this.$styleElement = null;\r\n                }\r\n            }\r\n            dispose() {\r\n                //Stop listening for CSS changes.\r\n                this.cssChangeSubscription.dispose();\r\n                this.cssText.dispose();\r\n                //Dispose rule sets.\r\n                for (const ruleset of this.statements) {\r\n                    ruleset.dispose();\r\n                }\r\n                //Remove the style element.\r\n                this.clear();\r\n            }\r\n        }\r\n    })(Preview = AmeStyleGenerator.Preview || (AmeStyleGenerator.Preview = {}));\r\n})(AmeStyleGenerator || (AmeStyleGenerator = {}));\r\n//# sourceMappingURL=style-generator.js.map"], "names": [], "sourceRoot": ""}