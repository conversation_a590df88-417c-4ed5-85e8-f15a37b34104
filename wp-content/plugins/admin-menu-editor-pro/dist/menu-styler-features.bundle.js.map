{"version": 3, "file": "menu-styler-features.bundle.js", "mappings": ";;;;;;;;;;;;;AAAa;AAMN,IAAU,uBAAuB,CA4evC;AA5eD,WAAiB,uBAAuB;IAGvC,MAAM,CAAC,GAAG,MAAM,CAAC;IAcjB,MAAe,eAAe;QAM7B,YAAsB,MAAwB;YAC7C,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC,QAAQ,CAAC;YACvC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC;YAC3C,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC;YACpC,wFAAwF;YACxF,uFAAuF;QACxF,CAAC;QAED,YAAY;YACX,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACnC,CAAC;QAAA,CAAC;QAEF,OAAO,CAAC,SAAiB,EAAE,KAAU,EAAE,eAAmD;YACzF,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;YAC5C,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE;gBACpC,IAAI,OAAO,CAAC,IAAI,EAAE;oBACjB,OAAO,CAAC,IAAI,CACX,+BAA+B,GAAG,IAAI,CAAC,YAAY,EAAE;0BACnD,8BAA8B,GAAG,SAAS,GAAG,IAAI,CACnD,CAAC;iBACF;gBACD,OAAO;aACP;YAED,IAAI,WAAW,qBAAU,IAAI,CAAC,cAAc,CAAC,CAAC;YAC9C,WAAW,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAC;YAE9B,wDAAwD;YACxD,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,UAAU,EAAE;gBACxC,IAAI,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CAAC,EAAE;oBAC9C,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;oBAC5C,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE;wBAC1C,WAAW,CAAC,QAAQ,CAAC,GAAG,eAAe,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;qBACzD;iBACD;aACD;YAED,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;QAC1B,CAAC;QAES,MAAM,CAAC,QAAW;YAC3B,IAAI,CAAC,cAAc,GAAG,QAAQ,CAAC;QAChC,CAAC;QAED;;WAEG;QACH,wBAAwB;YACvB,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACrC,CAAC;KAGD;IAMD,MAAa,yBAA0B,SAAQ,eAA2C;QAGzF,YAAY,MAAiD;YAC5D,KAAK,CAAC,MAAM,CAAC,CAAC;YAHL,kBAAa,GAAkB,IAAI,CAAC;YAI7C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACnC,CAAC;QAES,MAAM,CAAC,QAAoC;YACpD,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAEvB,MAAM,MAAM,GAAG,CAAC,CAAC,oDAAoD,CAAC,CAAC;YACvE,IAAI,IAAI,CAAC,aAAa,KAAK,IAAI,EAAE;gBAChC,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;aACnC;YAED,IAAI,CAAC,OAAO,QAAQ,CAAC,OAAO,CAAC,KAAK,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,KAAK,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,KAAK,IAAI,CAAC,EAAE;gBACvG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;aAChC;iBAAM;gBACN,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;aAC5B;QACF,CAAC;QAED,YAAY;YACX,OAAO,2BAA2B,CAAC;QACpC,CAAC;KACD;IA1BY,iDAAyB,4BA0BrC;IAoBD,MAAa,eAAgB,SAAQ,eAAiC;QA4CrE,YAAY,MAAuC;YAClD,KAAK,CAAC,MAAM,CAAC,CAAC;YA5CL,eAAU,GAAkB,IAAI,CAAC;YACjC,UAAK,GAAkB,IAAI,CAAC;YAC5B,kBAAa,GAAkB,IAAI,CAAC;YAE7B,WAAM,GAAG,wBAAwB,CAAC;YAClC,WAAM,GAAG,uBAAuB,CAAC;YAElD;;;;;eAKG;YAEc,qBAAgB,GAAG;gBACnC,2BAA2B;gBAC3B,+BAA+B;gBAC/B,yBAAyB;gBACzB,iCAAiC;gBACjC,mBAAmB;gBACnB,qBAAqB;gBACrB,iBAAiB;gBACjB,0BAA0B;aAC1B,CAAC;YAEe,kBAAa,GAAG,eAAe,IAAI,CAAC,MAAM;;;;;;;;;gBAS7C,IAAI,CAAC,MAAM,uBAAuB,IAAI,CAAC,MAAM;;;;IAIzD,CAAC;YAEK,yBAAoB,GAAkB,IAAI,CAAC;YAC3C,4BAAuB,GAAkB,IAAI,CAAC;YAIrD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACnC,CAAC;QAES,MAAM,CAAC,MAAwB;YACxC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YACrB,IAAI,CAAC,wBAAwB,EAAE,CAAC;QACjC,CAAC;QAEO,wBAAwB;YAC/B,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,kEAAkE;YAEtG,MAAM,YAAY,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YACjE,MAAM,iBAAiB,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;YAC3E,IAAI,CAAC,YAAY,IAAI,CAAC,iBAAiB,EAAE;gBACxC,IAAI,CAAC,UAAU,EAAE,CAAC;gBAClB,OAAO;aACP;YAED,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,YAAY,EAAE,iBAAiB,EAAE,EAAE;gBAC/D,MAAM,CAAC,aAAa,EAAE,KAAK,CAAC,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBAC1D,IAAI,cAAc,GAAG,EAAE,CAAC;gBACxB,IAAI,mBAAmB,GAAG,EAAE,CAAC;gBAE7B,IAAI,WAAW,GAAG,KAAK,CAAC;gBACxB,IAAI,gBAAgB,GAAG,KAAK,CAAC;gBAE7B,IAAI,YAAY,EAAE;oBACjB,WAAW,GAAG,IAAI,CAAC;oBACnB,cAAc,CAAC,IAAI,CAAC,0BAA0B,YAAY,KAAK,CAAC,CAAC;oBAEjE,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;oBAC5E,cAAc,CAAC,IAAI,CAAC,WAAW,UAAU,KAAK,CAAC,CAAC;iBAChD;qBAAM;oBACN,cAAc,CAAC,IAAI,CAAC,gBAAgB,CAAC;iBACrC;gBACD,IAAI,iBAAiB,EAAE;oBACtB,gBAAgB,GAAG,IAAI,CAAC;oBACxB,mBAAmB,CAAC,IAAI,CAAC,0BAA0B,iBAAiB,KAAK,CAAC,CAAC;oBAC3E,mBAAmB,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;oBAE5C,MAAM,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;oBAC3F,mBAAmB,CAAC,IAAI,CAAC,WAAW,eAAe,KAAK,CAAC,CAAC;iBAC1D;qBAAM;oBACN,mBAAmB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;iBAC3C;gBAED,IAAI,MAAM,CAAC,eAAe,EAAE;oBAC3B,cAAc,CAAC,IAAI,CAAC,qBAAqB,MAAM,CAAC,eAAe,GAAG,CAAC,CAAC;iBACpE;gBAED,MAAM,OAAO,GAAG,CAAC,OAAO,MAAM,CAAC,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;gBAClF,IAAI,OAAO,EAAE;oBACZ,KAAK,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;iBACnC;qBAAM;oBACN,KAAK,CAAC,IAAI,EAAE,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;iBAChC;gBAED,cAAc,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBAEjD,MAAM,SAAS,GAAG,eAAe,IAAI,CAAC,MAAM,OAAO,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;gBACjF,MAAM,cAAc,GAAG,uBAAuB,IAAI,CAAC,MAAM,OAAO,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;gBAEnG,qEAAqE;gBACrE,qFAAqF;gBACrF,sDAAsD;gBACtD,MAAM,CAAC,EAAE,gBAAgB,CAAC,GAAG,IAAI,CAAC,6BAA6B,EAAE,CAAC;gBAClE,IAAI,UAAU,GAAG,iDAAiD,gBAAgB,OAAO,CAAC;gBAC1F,IAAI,WAAW,EAAE;oBAChB,UAAU,IAAI,mDAAmD,CAAC;oBAClE,UAAU,IAAI,uEAAuE,CAAC;iBACtF;gBACD,IAAI,gBAAgB,EAAE;oBACrB,UAAU,IAAI,yCAAyC,CAAC;oBACxD,UAAU,IAAI,6DAA6D,CAAC;iBAC5E;gBAED,aAAa,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,GAAG,cAAc,GAAG,IAAI,GAAG,IAAI,CAAC,aAAa,GAAG,IAAI,GAAG,UAAU,CAAC,CAAC;YACvG,CAAC,CAAC,CAAC;QACJ,CAAC;QAEO,oBAAoB,CAAC,OAAiC;YAC7D,IAAI,OAAO,KAAK,IAAI,EAAE;gBACrB,OAAO,KAAK,CAAC;aACb;YACD,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,YAAY,KAAK,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC,WAAW,CAAC,CAAC;QACnG,CAAC;QAEO,cAAc,CACrB,MAAwB,EACxB,QAAiF;YAEjF,IAAI,SAAS,GAAG;gBACf,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,SAAS,CAAC;gBAClC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,cAAc,CAAC;aACvC,CAAC;YAEF,gFAAgF;YAChF,+EAA+E;YAC/E,qEAAqE;YACrE,IAAI,CAAC,OAAO,SAAS,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC,IAAI,CAAC,OAAO,SAAS,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC,EAAE;gBAC7E,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrC,OAAO;aACP;YAED,4EAA4E;YAC5E,0EAA0E;YAC1E,OAAO,CAAC,GAAG,CACV,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE;gBACnB,4CAA4C;gBAC5C,IAAI,CAAC,OAAO,CAAC,KAAK,QAAQ,CAAC,IAAI,CAAC,CAAC,KAAK,IAAI,CAAC,EAAE;oBAC5C,OAAO,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;iBAC1B;gBAED,OAAO,CAAC,CAAC,IAAI,CACZ,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,EACrD,GAAG,EAAE,CAAC,IAAI,CAAC,yBAAyB;iBACJ,CAAC;YACnC,CAAC,CAAC,CACF,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE;gBAClB,4DAA4D;gBAC5D,8DAA8D;gBAC9D,IAAI,IAAI,CAAC,cAAc,KAAK,MAAM,EAAE;oBACnC,OAAO;iBACP;gBAED,MAAM,CAAC,YAAY,EAAE,iBAAiB,CAAC,GAAG,OAAO,CAAC;gBAClD,QAAQ,CAAC,YAAY,aAAZ,YAAY,cAAZ,YAAY,GAAI,IAAI,EAAE,iBAAiB,aAAjB,iBAAiB,cAAjB,iBAAiB,GAAI,IAAI,CAAC,CAAC;YAC3D,CAAC,CAAC,CAAC;QACJ,CAAC;QAEO,mBAAmB;YAC1B,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;gBACrB,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,WAAW,IAAI,CAAC,MAAM,SAAS,CAAC,CAAC;gBACrD,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,UAAU,IAAI,CAAC,MAAM,QAAQ,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBACxE,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;aACxC;YACD,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;gBACxB,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC,iBAAiB,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;aAC3D;YACD,OAAO,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,KAAM,CAAC,CAAC;QAC1C,CAAC;QAEO,WAAW,CAAC,YAAsC;YACzD,IAAI,YAAY,KAAK,IAAI,EAAE;gBAC1B,OAAO,IAAI,CAAC;aACZ;YAED,MAAM,WAAW,GAAG,CAAC,OAAO,YAAY,CAAC,WAAW,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;YAC1G,IAAI,WAAW,EAAE;gBAChB,OAAO,WAAW,CAAC;aACnB;YAED,MAAM,YAAY,GAAG,YAAY,CAAC,YAAY,IAAI,CAAC,CAAC;YACpD,8DAA8D;YAC9D,IAAI,YAAY,GAAG,CAAC,EAAE;gBACrB,4CAA4C;gBAC5C,IAAI,YAAY,CAAC,aAAa,EAAE;oBAC/B,OAAO,YAAY,CAAC,aAAa,CAAC;iBAClC;gBAED,0CAA0C;gBAC1C,IAAI,CAAC,OAAO,EAAE,KAAK,WAAW,CAAC,IAAI,EAAE,CAAC,KAAK,IAAI,EAAE,CAAC,KAAK,CAAC,UAAU,EAAE;oBACnE,4BAA4B;oBAC5B,IAAI,aAAa,GAAG,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;oBACjE,IAAI,aAAa,EAAE;wBAClB,OAAO,aAAa,CAAC;qBACrB;oBAED,MAAM,cAAc,GAAG,CAAC,CAAC,QAAQ,EAAU,CAAC;oBAC5C,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,KAAK,EAAE,CAAC,IAAI;oBAC7C,SAAS;oBACT,CAAC,UAAe,EAAE,EAAE;wBACnB,IAAI,UAAU,IAAI,UAAU,CAAC,GAAG,EAAE;4BACjC,cAAc,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;yBACvC;6BAAM;4BACN,cAAc,CAAC,MAAM,EAAE,CAAC;yBACxB;oBACF,CAAC;oBACD,OAAO;oBACP,GAAG,EAAE,CAAC,cAAc,CAAC,MAAM,EAAE,CAC7B,CAAC;oBACF,OAAO,cAAc,CAAC,OAAO,EAAE,CAAC;iBAChC;aACD;YAED,WAAW;YACX,OAAO,IAAI,CAAC;QACb,CAAC;QAEO,6BAA6B;YACpC,IAAI,CAAC,IAAI,CAAC,oBAAoB,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,uBAAuB,KAAK,IAAI,CAAC,EAAE;gBACpF,iFAAiF;gBACjF,gCAAgC;gBAChC,MAAM,UAAU,GAAG,CAAC,CAAC,YAAY,CAAC,CAAC;gBACnC,IAAI,CAAC,oBAAoB,GAAG,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,EAAE,CAAC,CAAC;gBACvE,IAAI,CAAC,uBAAuB,GAAG,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,eAAe,CAAC,EAAE,EAAE,CAAC,CAAC;gBAC7E,IAAI,KAAK,CAAC,IAAI,CAAC,oBAAoB,CAAC,EAAE;oBACrC,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC;iBAC9B;gBACD,IAAI,KAAK,CAAC,IAAI,CAAC,uBAAuB,CAAC,EAAE;oBACxC,IAAI,CAAC,uBAAuB,GAAG,CAAC,CAAC;iBACjC;aACD;YACD,OAAO,CAAC,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC;QAClE,CAAC;QAEO,UAAU;YACjB,IAAI,IAAI,CAAC,UAAU,EAAE;gBACpB,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;gBACzB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;gBACvB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;aAClB;YACD,IAAI,IAAI,CAAC,aAAa,EAAE;gBACvB,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;gBAC5B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;aAC1B;QACF,CAAC;QAED,YAAY;YACX,OAAO,iBAAiB,CAAC;QAC1B,CAAC;KACD;IA3QY,uCAAe,kBA2Q3B;IAOD,8DAA8D;IAC9D,wFAAwF;IAC7E,6CAAqB,GAAqC,IAAI,CAAC;IAC/D,uCAAe,GAA2B,IAAI,CAAC;IAC1D,MAAM,wBAAwB,GAAG,oCAAoC,CAAC;IACtE,MAAM,kBAAkB,GAAG,0BAA0B,CAAC;IAEtD,IAAI,aAAa,GAAG,KAAK,CAAC;IAE1B,SAAS,sBAAsB;QAC9B,IAAI,aAAa,EAAE;YAClB,OAAO;SACP;QACD,aAAa,GAAG,IAAI,CAAC;QAErB,2EAA2E;QAC3E,2EAA2E;QAC3E,mCAAmC;QACnC,uEAAuE;QACvE,sCAAsC;QACtC,IAAI,0BAA0B,CAAC,kBAAkB,EAAE;YAClD,IAAI,MAAM,CAAC,wBAAwB,CAAC,EAAE;gBACrC,6CAAqB,GAAG,MAAM,CAAC,wBAAwB,CAAC,CAAC;aACzD;iBAAM;gBACN,6CAAqB,GAAG,IAAI,yBAAyB,CAAC,0BAA0B,CAAC,kBAAkB,CAAC,CAAC;gBACrG,MAAM,CAAC,wBAAwB,CAAC,GAAG,6CAAqB,CAAC;aACzD;SACD;QACD,IAAI,0BAA0B,CAAC,QAAQ,EAAE;YACxC,IAAI,MAAM,CAAC,kBAAkB,CAAC,EAAE;gBAC/B,uCAAe,GAAG,MAAM,CAAC,kBAAkB,CAAC,CAAC;aAC7C;iBAAM;gBACN,uCAAe,GAAG,IAAI,eAAe,CAAC,0BAA0B,CAAC,QAAQ,CAAC,CAAC;gBAC3E,MAAM,CAAC,kBAAkB,CAAC,GAAG,uCAAe,CAAC;aAC7C;SACD;QAED;;;;WAIG;QACH,SAAS,sBAAsB,CAAC,cAAmB;YAClD,6EAA6E;YAC7E,IAAI,CAAC,6CAAqB,IAAI,CAAC,uCAAe,EAAE;gBAC/C,OAAO,CAAC,IAAI,CAAC,sEAAsE,CAAC,CAAC;gBACrF,OAAO;aACP;YACD,cAAc,CAAC,sBAAsB,CACpC,6CAAqB,CAAC,wBAAwB,EAAE,EAChD,6CAAqB,CACrB,CAAC;YACF,cAAc,CAAC,sBAAsB,CACpC,uCAAe,CAAC,wBAAwB,EAAE,EAC1C,uCAAe,CACf,CAAC;QACH,CAAC;QAED,IAAI,OAAO,MAAM,CAAC,0BAA0B,CAAC,KAAK,WAAW,EAAE;YAC9D,sBAAsB,CAAC,MAAM,CAAC,0BAA0B,CAAC,CAAC,CAAC;SAC3D;aAAM;YACN,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,gCAAgC,EAAE,CAAC,KAAK,EAAE,cAAc,EAAE,EAAE;gBAC1E,sBAAsB,CAAC,cAAc,CAAC,CAAC;YACxC,CAAC,CAAC,CAAC;SACH;IACF,CAAC;IAED,4FAA4F;IAC5F,8FAA8F;IAC9F,8DAA8D;IAE9D,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,8BAA8B,EAAE,sBAAsB,CAAC,CAAC;IACxE,CAAC,CAAC,sBAAsB,CAAC,CAAC;IAE1B,oDAAoD;IACpD,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,CACb,sCAAsC,EACtC,UAAU,OAAO,EAAE,EAAY;QAC9B,IAAI,CAAC,EAAE,EAAE;YACR,OAAO;SACP;QACD,sBAAsB,EAAE,CAAC;QAEzB,IAAI,6CAAqB,EAAE;YAC1B,EAAE,CAAC,sBAAsB,CAAC,6CAAqB,CAAC,wBAAwB,EAAE,EAAE,6CAAqB,CAAC,CAAC;SACnG;QACD,IAAI,uCAAe,EAAE;YACpB,EAAE,CAAC,sBAAsB,CAAC,uCAAe,CAAC,wBAAwB,EAAE,EAAE,uCAAe,CAAC,CAAC;SACvF;IACF,CAAC,CACD,CAAC;AACH,CAAC,EA5egB,uBAAuB,KAAvB,uBAAuB,QA4evC", "sources": ["webpack:///./extras/modules/menu-styler/menu-styler-features.ts"], "sourcesContent": ["'use strict';\n\nimport {AmeCustomizable, AmeCustomizableViewModel} from '../../pro-customizables/assets/customizable.js';\n\ndeclare const ameMenuStylerFeatureConfig: AmeMenuStylerJsFeatures.FeatureScriptConfig;\n\nexport namespace AmeMenuStylerJsFeatures {\n\timport PreviewUpdater = AmeCustomizable.PreviewUpdater;\n\timport SimpleVm = AmeCustomizableViewModel.SimpleVm;\n\tconst $ = jQuery;\n\n\tinterface FeatureConfig<T extends FeatureSettings> {\n\t\tsettings: T;\n\t\tsettingMap: SettingMap<T>;\n\t}\n\n\tinterface FeatureSettings {\n\t}\n\n\tinterface SettingMap<T extends FeatureSettings> {\n\t\t[settingId: string]: keyof T;\n\t}\n\n\tabstract class StylerJsFeature<S extends FeatureSettings> implements PreviewUpdater {\n\t\tprotected readonly initialSettings: S;\n\t\tprotected readonly settingMap: SettingMap<S>;\n\n\t\tprotected activeSettings: S;\n\n\t\tprotected constructor(config: FeatureConfig<S>) {\n\t\t\tthis.initialSettings = config.settings;\n\t\t\tthis.activeSettings = this.initialSettings;\n\t\t\tthis.settingMap = config.settingMap;\n\t\t\t//Note: Subclasses should call `this.update(this.initialSettings)` in their constructor.\n\t\t\t//It is not done here because the subclass constructor may need to do some setup first.\n\t\t}\n\n\t\tclearPreview(): void {\n\t\t\tthis.update(this.initialSettings);\n\t\t};\n\n\t\tpreview(settingId: string, value: any, getSettingValue: AmeCustomizable.SettingValueReader): void {\n\t\t\tconst localKey = this.settingMap[settingId];\n\t\t\tif (typeof localKey === 'undefined') {\n\t\t\t\tif (console.warn) {\n\t\t\t\t\tconsole.warn(\n\t\t\t\t\t\t'Preview failed: The feature \"' + this.getFeatureId()\n\t\t\t\t\t\t+ '\" does not use the setting \"' + settingId + '\".'\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tlet newSettings: S = {...this.activeSettings};\n\t\t\tnewSettings[localKey] = value;\n\n\t\t\t//Get any known but missing settings using the callback.\n\t\t\tfor (const settingId in this.settingMap) {\n\t\t\t\tif (this.settingMap.hasOwnProperty(settingId)) {\n\t\t\t\t\tconst localKey = this.settingMap[settingId];\n\t\t\t\t\tif (!newSettings.hasOwnProperty(localKey)) {\n\t\t\t\t\t\tnewSettings[localKey] = getSettingValue(settingId, null);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tthis.update(newSettings);\n\t\t}\n\n\t\tprotected update(settings: S): void {\n\t\t\tthis.activeSettings = settings;\n\t\t}\n\n\t\t/**\n\t\t * Get the setting IDs that this feature uses. The feature can preview any of these settings.\n\t\t */\n\t\tgetPreviewableSettingIds(): string[] {\n\t\t\treturn Object.keys(this.settingMap);\n\t\t}\n\n\t\tabstract getFeatureId(): string;\n\t}\n\n\tinterface CollapseButtonTextSettings extends FeatureSettings {\n\t\tlabel: string;\n\t}\n\n\texport class CollapseButtonTextFeature extends StylerJsFeature<CollapseButtonTextSettings> {\n\t\tprotected originalLabel: string | null = null;\n\n\t\tconstructor(config: FeatureConfig<CollapseButtonTextSettings>) {\n\t\t\tsuper(config);\n\t\t\tthis.update(this.initialSettings);\n\t\t}\n\n\t\tprotected update(settings: CollapseButtonTextSettings): void {\n\t\t\tsuper.update(settings);\n\n\t\t\tconst $label = $('#adminmenu #collapse-button .collapse-button-label');\n\t\t\tif (this.originalLabel === null) {\n\t\t\t\tthis.originalLabel = $label.text();\n\t\t\t}\n\n\t\t\tif ((typeof settings['label'] === 'undefined') || (settings.label === '') || (settings.label === null)) {\n\t\t\t\t$label.text(this.originalLabel);\n\t\t\t} else {\n\t\t\t\t$label.text(settings.label);\n\t\t\t}\n\t\t}\n\n\t\tgetFeatureId(): string {\n\t\t\treturn 'CollapseButtonTextFeature';\n\t\t}\n\t}\n\n\tinterface ImageSettingValue {\n\t\tattachmentId: number | null;\n\t\tattachmentSiteId: number | null;\n\t\tattachmentUrl: string;\n\t\texternalUrl: string | null;\n\t\twidth: number | null;\n\t\theight: number | null;\n\t}\n\n\tinterface MenuLogoSettings extends FeatureSettings {\n\t\tbaseImage: ImageSettingValue | null;\n\t\tcollapsedImage: ImageSettingValue | null;\n\t\tlinkUrl: string | null;\n\t\tbackgroundColor: string | null;\n\t\tbaseHeight: number | null;\n\t\tcollapsedHeight: number | null;\n\t}\n\n\texport class MenuLogoFeature extends StylerJsFeature<MenuLogoSettings> {\n\t\tprotected $container: JQuery | null = null;\n\t\tprotected $link: JQuery | null = null;\n\t\tprotected $styleElement: JQuery | null = null;\n\n\t\tprivate readonly logoId = 'ame_ms_admin_menu_logo';\n\t\tprivate readonly linkId = 'ame_ms_menu_logo_link';\n\n\t\t/*\n\t\t * Note: The logo container is set up so that the logo image is inside the content\n\t\t * box (i.e. it does not overlap the padding or the margin), but the logo link covers\n\t\t * the padding area. This way the user can control the clickable (padding) area and\n\t\t * the unclickable (margin) area separately.\n\t\t */\n\n\t\tprivate readonly staticLogoStyles = [\n\t\t\t'background-size: contain;',\n\t\t\t'background-repeat: no-repeat;',\n\t\t\t'background-position: 0;',\n\t\t\t'background-origin: content-box;',\n\t\t\t'min-height: 10px;',\n\t\t\t'position: relative;',\n\t\t\t'display: block;',\n\t\t\t'box-sizing: content-box;',\n\t\t];\n\n\t\tprivate readonly staticLinkCss = `#adminmenu #${this.linkId} {\n\t\t\tdisplay: block;\n\t\t\tposition: absolute;\n\t\t\ttop: 0; left: 0; right: 0; bottom: 0;\n\t\t\tmargin: 0;\n\t\t\tpadding: 0;\n\t\t\tbackground: transparent;\n\t\t\ttext-decoration: none;\n\t\t}\n\t\t#adminmenu #${this.linkId}:hover, #adminmenu #${this.linkId}:focus {\n\t\t\tbox-shadow: none;\n\t\t\ttransition: none;\n\t\t\tcolor: transparent;\n\t\t}`;\n\n\t\tprivate defaultMenuMarginTop: number | null = null;\n\t\tprivate defaultMenuMarginBottom: number | null = null;\n\n\t\tconstructor(config: FeatureConfig<MenuLogoSettings>) {\n\t\t\tsuper(config);\n\t\t\tthis.update(this.initialSettings);\n\t\t}\n\n\t\tprotected update(config: MenuLogoSettings) {\n\t\t\tsuper.update(config);\n\t\t\tthis.updateFromActiveSettings();\n\t\t}\n\n\t\tprivate updateFromActiveSettings(): void {\n\t\t\tconst config = this.activeSettings; //Local reference in case the config changes while loading images.\n\n\t\t\tconst hasBaseImage = this.settingContainsImage(config.baseImage);\n\t\t\tconst hasCollapsedImage = this.settingContainsImage(config.collapsedImage);\n\t\t\tif (!hasBaseImage && !hasCollapsedImage) {\n\t\t\t\tthis.removeLogo();\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tthis.withLogoImages(config, (baseImageUrl, collapsedImageUrl) => {\n\t\t\t\tconst [$styleElement, $link] = this.getOrCreateElements();\n\t\t\t\tlet baseLogoStyles = [];\n\t\t\t\tlet collapsedLogoStyles = [];\n\n\t\t\t\tlet hasBaseLogo = false;\n\t\t\t\tlet hasCollapsedLogo = false;\n\n\t\t\t\tif (baseImageUrl) {\n\t\t\t\t\thasBaseLogo = true;\n\t\t\t\t\tbaseLogoStyles.push(`background-image: url(\"${baseImageUrl}\");`);\n\n\t\t\t\t\tconst baseHeight = Math.max(config.baseHeight ? config.baseHeight : 10, 10);\n\t\t\t\t\tbaseLogoStyles.push(`height: ${baseHeight}px;`);\n\t\t\t\t} else {\n\t\t\t\t\tbaseLogoStyles.push('display: none;')\n\t\t\t\t}\n\t\t\t\tif (collapsedImageUrl) {\n\t\t\t\t\thasCollapsedLogo = true;\n\t\t\t\t\tcollapsedLogoStyles.push(`background-image: url(\"${collapsedImageUrl}\");`);\n\t\t\t\t\tcollapsedLogoStyles.push('display: block;');\n\n\t\t\t\t\tconst collapsedHeight = Math.max(config.collapsedHeight ? config.collapsedHeight : 10, 10);\n\t\t\t\t\tcollapsedLogoStyles.push(`height: ${collapsedHeight}px;`);\n\t\t\t\t} else {\n\t\t\t\t\tcollapsedLogoStyles.push('display: none;');\n\t\t\t\t}\n\n\t\t\t\tif (config.backgroundColor) {\n\t\t\t\t\tbaseLogoStyles.push(`background-color: ${config.backgroundColor};`);\n\t\t\t\t}\n\n\t\t\t\tconst linkUrl = (typeof config.linkUrl === 'string') ? config.linkUrl.trim() : '';\n\t\t\t\tif (linkUrl) {\n\t\t\t\t\t$link.show().attr('href', linkUrl);\n\t\t\t\t} else {\n\t\t\t\t\t$link.hide().removeAttr('href');\n\t\t\t\t}\n\n\t\t\t\tbaseLogoStyles.unshift(...this.staticLogoStyles);\n\n\t\t\t\tconst baseStyle = `#adminmenu #${this.logoId} {\\n${baseLogoStyles.join('\\n')} }`;\n\t\t\t\tconst collapsedStyle = `.folded #adminmenu #${this.logoId} {\\n${collapsedLogoStyles.join('\\n')} }`;\n\n\t\t\t\t//Remove the top margin from the admin menu when the logo is visible.\n\t\t\t\t//We also need to let other AME components know that the vertical margin has changed.\n\t\t\t\t//This affects the \"Collapse button position\" setting.\n\t\t\t\tconst [, menuMarginBottom] = this.getDefaultVerticalMenuMargins();\n\t\t\t\tlet wrapperCss = `#adminmenuwrap { --ame-ms-menu-margin-bottom: ${menuMarginBottom}px; }`;\n\t\t\t\tif (hasBaseLogo) {\n\t\t\t\t\twrapperCss += `body:not(.folded) #adminmenu { margin-top: 0; }\\n`;\n\t\t\t\t\twrapperCss += `body:not(.folded) #adminmenuwrap { --ame-ms-menu-margin-top: 0px; }\\n`;\n\t\t\t\t}\n\t\t\t\tif (hasCollapsedLogo) {\n\t\t\t\t\twrapperCss += `.folded #adminmenu { margin-top: 0; }\\n`;\n\t\t\t\t\twrapperCss += `.folded #adminmenuwrap { --ame-ms-menu-margin-top: 0px; }\\n`;\n\t\t\t\t}\n\n\t\t\t\t$styleElement.text(baseStyle + \"\\n\" + collapsedStyle + \"\\n\" + this.staticLinkCss + \"\\n\" + wrapperCss);\n\t\t\t});\n\t\t}\n\n\t\tprivate settingContainsImage(setting: ImageSettingValue | null): boolean {\n\t\t\tif (setting === null) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\treturn !!(((setting.attachmentId !== null) && (setting.attachmentId > 0)) || setting.externalUrl);\n\t\t}\n\n\t\tprivate withLogoImages(\n\t\t\tconfig: MenuLogoSettings,\n\t\t\tcallback: (baseImageUrl: string | null, collapsedImageUrl: string | null) => void\n\t\t): void {\n\t\t\tlet imageUrls = [\n\t\t\t\tthis.getImageUrl(config.baseImage),\n\t\t\t\tthis.getImageUrl(config.collapsedImage),\n\t\t\t];\n\n\t\t\t//Add the logo as quickly as possible to prevent the menu from visibly shifting.\n\t\t\t//Promises are usually asynchronous, so let's avoid them when possible and call\n\t\t\t//the callback immediately if both URLs are already known or invalid.\n\t\t\tif ((typeof imageUrls[0] === 'string') && (typeof imageUrls[1] === 'string')) {\n\t\t\t\tcallback(imageUrls[0], imageUrls[1]);\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t//Assume that ES2020 is not available, so we can't use Promise.allSettled().\n\t\t\t//However, we want to wait for all promises to resolve, even if some fail.\n\t\t\tPromise.all(\n\t\t\t\timageUrls.map((p) => {\n\t\t\t\t\t//Convert known values to resolved promises.\n\t\t\t\t\tif ((typeof p === 'string') || (p === null)) {\n\t\t\t\t\t\treturn Promise.resolve(p);\n\t\t\t\t\t}\n\n\t\t\t\t\treturn p.then(\n\t\t\t\t\t\t(value) => (typeof value === 'string') ? value : null,\n\t\t\t\t\t\t() => null //Convert errors to null.\n\t\t\t\t\t) as JQueryPromise<string | null>;\n\t\t\t\t})\n\t\t\t).then((results) => {\n\t\t\t\t//If the active config has changed, don't apply the results.\n\t\t\t\t//The URLs that we just loaded might not be relevant any more.\n\t\t\t\tif (this.activeSettings !== config) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tconst [baseImageUrl, collapsedImageUrl] = results;\n\t\t\t\tcallback(baseImageUrl ?? null, collapsedImageUrl ?? null);\n\t\t\t});\n\t\t}\n\n\t\tprivate getOrCreateElements(): [JQuery, JQuery] {\n\t\t\tif (!this.$container) {\n\t\t\t\tthis.$container = $(`<li id=\"${this.logoId}\"></li>`);\n\t\t\t\tthis.$link = $(`<a id=\"${this.linkId}\"></a>`).appendTo(this.$container);\n\t\t\t\tthis.$container.prependTo('#adminmenu');\n\t\t\t}\n\t\t\tif (!this.$styleElement) {\n\t\t\t\tthis.$styleElement = $('<style></style>').appendTo('head');\n\t\t\t}\n\t\t\treturn [this.$styleElement, this.$link!];\n\t\t}\n\n\t\tprivate getImageUrl(imageSetting: ImageSettingValue | null): JQueryPromise<string> | string | null {\n\t\t\tif (imageSetting === null) {\n\t\t\t\treturn null;\n\t\t\t}\n\n\t\t\tconst externalUrl = (typeof imageSetting.externalUrl === 'string') ? imageSetting.externalUrl.trim() : '';\n\t\t\tif (externalUrl) {\n\t\t\t\treturn externalUrl;\n\t\t\t}\n\n\t\t\tconst attachmentId = imageSetting.attachmentId || 0;\n\t\t\t//const attachmentSiteId = imageSetting.attachmentSiteId || 0;\n\t\t\tif (attachmentId > 0) {\n\t\t\t\t//Use the cached attachment URL if possible.\n\t\t\t\tif (imageSetting.attachmentUrl) {\n\t\t\t\t\treturn imageSetting.attachmentUrl;\n\t\t\t\t}\n\n\t\t\t\t//Load the attachment URL from the server.\n\t\t\t\tif ((typeof wp !== 'undefined') && wp.media && wp.media.attachment) {\n\t\t\t\t\t//Maybe it's already loaded?\n\t\t\t\t\tlet attachmentUrl = wp.media.attachment(attachmentId).get('url');\n\t\t\t\t\tif (attachmentUrl) {\n\t\t\t\t\t\treturn attachmentUrl;\n\t\t\t\t\t}\n\n\t\t\t\t\tconst deferredLoader = $.Deferred<string>();\n\t\t\t\t\twp.media.attachment(attachmentId).fetch().then(\n\t\t\t\t\t\t//Success\n\t\t\t\t\t\t(attachment: any) => {\n\t\t\t\t\t\t\tif (attachment && attachment.url) {\n\t\t\t\t\t\t\t\tdeferredLoader.resolve(attachment.url);\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tdeferredLoader.reject();\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t},\n\t\t\t\t\t\t//Error\n\t\t\t\t\t\t() => deferredLoader.reject()\n\t\t\t\t\t);\n\t\t\t\t\treturn deferredLoader.promise();\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t//No image.\n\t\t\treturn null;\n\t\t}\n\n\t\tprivate getDefaultVerticalMenuMargins(): [number, number] {\n\t\t\tif ((this.defaultMenuMarginTop === null) || (this.defaultMenuMarginBottom === null)) {\n\t\t\t\t//Get the vertical margins of the admin menu. The value includes the \"px\" suffix,\n\t\t\t\t//but parseInt() will ignore it.\n\t\t\t\tconst $adminmenu = $('#adminmenu');\n\t\t\t\tthis.defaultMenuMarginTop = parseInt($adminmenu.css('margin-top'), 10);\n\t\t\t\tthis.defaultMenuMarginBottom = parseInt($adminmenu.css('margin-bottom'), 10);\n\t\t\t\tif (isNaN(this.defaultMenuMarginTop)) {\n\t\t\t\t\tthis.defaultMenuMarginTop = 0;\n\t\t\t\t}\n\t\t\t\tif (isNaN(this.defaultMenuMarginBottom)) {\n\t\t\t\t\tthis.defaultMenuMarginBottom = 0;\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn [this.defaultMenuMarginTop, this.defaultMenuMarginBottom];\n\t\t}\n\n\t\tprivate removeLogo(): void {\n\t\t\tif (this.$container) {\n\t\t\t\tthis.$container.remove();\n\t\t\t\tthis.$container = null;\n\t\t\t\tthis.$link = null;\n\t\t\t}\n\t\t\tif (this.$styleElement) {\n\t\t\t\tthis.$styleElement.remove();\n\t\t\t\tthis.$styleElement = null;\n\t\t\t}\n\t\t}\n\n\t\tgetFeatureId(): string {\n\t\t\treturn 'MenuLogoFeature';\n\t\t}\n\t}\n\n\texport interface FeatureScriptConfig {\n\t\tcollapseButtonText?: FeatureConfig<CollapseButtonTextSettings>;\n\t\tmenuLogo?: FeatureConfig<MenuLogoSettings>;\n\t}\n\n\t//Always initialize the features if their config is available.\n\t//They work normally on most admin pages, and are used for preview on the settings page.\n\texport let collapseButtonFeature: CollapseButtonTextFeature | null = null;\n\texport let menuLogoFeature: MenuLogoFeature | null = null;\n\tconst collapseButtonFeatureKey = 'ameMenuStyler_collapseButtonTextFt';\n\tconst menuLogoFeatureKey = 'ameMenuStyler_menuLogoFt';\n\n\tlet isInitialized = false;\n\n\tfunction createFeatureInstances() {\n\t\tif (isInitialized) {\n\t\t\treturn;\n\t\t}\n\t\tisInitialized = true;\n\n\t\t//If the script is loaded multiple times, the features might already exist.\n\t\t//This can happen because the script is both enqueued normally and imported\n\t\t//as a module on the settings page.\n\t\t//We want each feature to be initialized only once, so we'll store them\n\t\t//in the window object and reuse them.\n\t\tif (ameMenuStylerFeatureConfig.collapseButtonText) {\n\t\t\tif (window[collapseButtonFeatureKey]) {\n\t\t\t\tcollapseButtonFeature = window[collapseButtonFeatureKey];\n\t\t\t} else {\n\t\t\t\tcollapseButtonFeature = new CollapseButtonTextFeature(ameMenuStylerFeatureConfig.collapseButtonText);\n\t\t\t\twindow[collapseButtonFeatureKey] = collapseButtonFeature;\n\t\t\t}\n\t\t}\n\t\tif (ameMenuStylerFeatureConfig.menuLogo) {\n\t\t\tif (window[menuLogoFeatureKey]) {\n\t\t\t\tmenuLogoFeature = window[menuLogoFeatureKey];\n\t\t\t} else {\n\t\t\t\tmenuLogoFeature = new MenuLogoFeature(ameMenuStylerFeatureConfig.menuLogo);\n\t\t\t\twindow[menuLogoFeatureKey] = menuLogoFeature;\n\t\t\t}\n\t\t}\n\n\t\t/**\n\t\t * Register the features with the Admin Customizer preview handler, if active.\n\t\t *\n\t\t * @param {AmeAdminCustomizerPreview.PreviewHandler} previewHandler\n\t\t */\n\t\tfunction registerFeaturePreview(previewHandler: any) {\n\t\t\t//Both features should exist in the AC preview, but let's check just in case.\n\t\t\tif (!collapseButtonFeature || !menuLogoFeature) {\n\t\t\t\tconsole.warn('Menu Styler: One or more features are not initialized in AC preview.');\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tpreviewHandler.registerPreviewUpdater(\n\t\t\t\tcollapseButtonFeature.getPreviewableSettingIds(),\n\t\t\t\tcollapseButtonFeature\n\t\t\t);\n\t\t\tpreviewHandler.registerPreviewUpdater(\n\t\t\t\tmenuLogoFeature.getPreviewableSettingIds(),\n\t\t\t\tmenuLogoFeature\n\t\t\t);\n\t\t}\n\n\t\tif (typeof window['wsAdminCustomizerPreview'] !== 'undefined') {\n\t\t\tregisterFeaturePreview(window['wsAdminCustomizerPreview']);\n\t\t} else {\n\t\t\t$(document).on('adminMenuEditor:acPreviewStart', (event, previewHandler) => {\n\t\t\t\tregisterFeaturePreview(previewHandler);\n\t\t\t});\n\t\t}\n\t}\n\n\t//The #adminmenu element must be available before initialization. The DOMContentLoaded event\n\t//works, but we can better avoid a visible change/FOUC by using a custom event that the plugin\n\t//triggers immediately after WordPress outputs the admin menu.\n\n\t$(document).one('adminMenuEditor:menuDomReady', createFeatureInstances);\n\t$(createFeatureInstances);\n\n\t//Register the features with the menu styler dialog.\n\t$(document).on(\n\t\t'adminMenuEditor:menuStylerUiRegister',\n\t\tfunction (_unused, vm: SimpleVm) {\n\t\t\tif (!vm) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tcreateFeatureInstances();\n\n\t\t\tif (collapseButtonFeature) {\n\t\t\t\tvm.registerPreviewUpdater(collapseButtonFeature.getPreviewableSettingIds(), collapseButtonFeature);\n\t\t\t}\n\t\t\tif (menuLogoFeature) {\n\t\t\t\tvm.registerPreviewUpdater(menuLogoFeature.getPreviewableSettingIds(), menuLogoFeature);\n\t\t\t}\n\t\t}\n\t);\n}\n\ndeclare global {\n\tinterface Window {\n\t\tameMenuStyler_menuLogoFt?: AmeMenuStylerJsFeatures.MenuLogoFeature;\n\t\tameMenuStyler_collapseButtonTextFt?: AmeMenuStylerJsFeatures.CollapseButtonTextFeature;\n\t}\n}"], "names": [], "sourceRoot": ""}