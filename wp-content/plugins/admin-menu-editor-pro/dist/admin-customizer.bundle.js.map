{"version": 3, "file": "admin-customizer.bundle.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yDAAyD,IAAI;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,oBAAoB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,gCAAgC;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qDAAqD;AACrD;AACA,0DAA0D;AAC1D,iCAAiC;AACjC;AACA;AACA;AACA;AACA;AACA;AACA,qDAAqD;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC,eAAe,aAAa,eAAe;AACjF;AACA;AACA;AACA,yDAAyD,2DAA2D;AACpH;AACA;AACA,wDAAwD,kCAAkC;AAC1F;AACA;AACA;AACA;AACA;AACA,+DAA+D,+BAA+B;AAC9F;AACA;AACA,sDAAsD,+BAA+B,cAAc,eAAe;AAClH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8DAA8D,0BAA0B;AACxF;AACA,qCAAqC,SAAS,oDAAoD,0BAA0B;AAC5H;AACA;AACA;AACA,iEAAiE,4BAA4B;AAC7F;AACA;AACA,+DAA+D,0BAA0B;AACzF;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC,iBAAiB;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gDAAgD,sEAAsE,EAAE,eAAe;AACvI;AACA,iDAAiD,iEAAiE,EAAE,eAAe;AACnI;AACA,4CAA4C;AAC5C;AACA;AACA;AACA,0CAA0C,EAAE,cAAc;AAC1D;AACA,0CAA0C;AAC1C;AACA;AACA;AACA,0CAA0C,EAAE,gCAAgC;AAC5E;AACA;AACA;AACA;AACA;AACA,gDAAgD,qEAAqE,EAAE,eAAe;AACtI;AACA,iDAAiD,iEAAiE,EAAE,eAAe;AACnI;AACA,4CAA4C;AAC5C;AACA;AACA;AACA,uCAAuC,EAAE,cAAc;AACvD;AACA,4CAA4C;AAC5C;AACA;AACA;AACA,uCAAuC,EAAE,cAAc;AACvD;AACA,0CAA0C;AAC1C;AACA;AACA;AACA,0CAA0C,EAAE,gCAAgC;AAC5E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sDAAsD,iBAAiB;AACvE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,YAAY,mCAAmC;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wCAAwC,kCAAkC;AAC1E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,aAAa;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,CAAC;AACD,4BAA4B,wBAAwB;AACpD,yBAAyB,wBAAwB;AACjD;AACA;AACA;AACA;;AAEA;AACA;AACA,sEAAsE,UAAU;AAChF;AACA,CAAC,8BAA8B;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,4DAA4D;AACxE;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,qBAAqB;AACrB;AACA,qBAAqB;AACrB;AACA,iBAAiB;AACjB;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC,mCAAmC;AAC5E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,+CAA+C,mCAAmC;AAClF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,sBAAsB,gCAAgC;AACtD,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,8BAA8B;AACpD,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,GAAG;AAC/B;AACA,sCAAsC,GAAG;AACzC,8BAA8B,EAAE,UAAU,EAAE,eAAe,EAAE,UAAU,EAAE,UAAU,GAAG;AACtF;AACA;AACA,4DAA4D,GAAG,mFAAmF,GAAG;AACrJ;AACA,sCAAsC,sBAAsB,sCAAsC,uBAAuB,OAAO,GAAG,cAAc;AACjJ;AACA,qCAAqC,yBAAyB,4DAA4D,EAAE,SAAS,IAAI,MAAM,EAAE,iCAAiC,EAAE,SAAS,IAAI,yBAAyB,IAAI,GAAG,EAAE,aAAa,IAAI,GAAG,IAAI,WAAW,IAAI,GAAG,EAAE,WAAW,IAAI,GAAG,IAAI,WAAW,IAAI,GAAG,EAAE,WAAW,IAAI,GAAG,IAAI,WAAW,IAAI,GAAG,EAAE,WAAW,IAAI,GAAG,IAAI,WAAW,IAAI,GAAG,EAAE,WAAW,IAAI,GAAG,IAAI,WAAW,IAAI,GAAG,EAAE,WAAW,IAAI,GAAG,IAAI,WAAW,IAAI,mCAAmC,EAAE,SAAS,IAAI,MAAM,EAAE,iCAAiC,EAAE,SAAS,IAAI,0DAA0D,GAAG;AACnoB;AACA,yBAAyB,sBAAsB,IAAI,gBAAgB;AACnE,sDAAsD,EAAE,SAAS,IAAI,MAAM,EAAE,iCAAiC,EAAE,SAAS,IAAI;AAC7H,+BAA+B,IAAI,GAAG,EAAE,aAAa,IAAI,GAAG,IAAI,WAAW,IAAI,GAAG,EAAE,WAAW,IAAI,GAAG,IAAI,WAAW,IAAI,GAAG,EAAE,WAAW,IAAI,GAAG,IAAI,WAAW,IAAI,GAAG,EAAE,WAAW,IAAI,GAAG,IAAI,WAAW,IAAI,GAAG,EAAE,WAAW,IAAI,GAAG,IAAI,WAAW,IAAI,GAAG,EAAE,WAAW,IAAI,GAAG,IAAI,WAAW,IAAI,mCAAmC,EAAE,SAAS,IAAI,MAAM,EAAE,iCAAiC,EAAE,SAAS,IAAI;AAC3Y;AACA;AACA;AACA;AACA,oCAAoC,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,gBAAgB,UAAU,EAAE,OAAO,EAAE;AACvH;AACA;AACA,oCAAoC,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,gBAAgB;AAClG;AACA;AACA;AACA;AACA,oCAAoC,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,OAAO,EAAE;AAC9F;AACA;AACA,oCAAoC,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE;AACzE;AACA;AACA;AACA;AACA,oCAAoC,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,oBAAoB,EAAE,OAAO,EAAE;AACxG;AACA;AACA,oCAAoC,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE;AACzE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,4CAA4C,cAAc;AAC1D,SAAS;AACT;AACA;AACA,4CAA4C,qBAAqB;AACjE,SAAS;AACT;AACA;AACA,4CAA4C,qBAAqB;AACjE,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC,iDAAiD;AACvF;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC,yBAAyB;AAC/D;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC,uBAAuB;AAC7D;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,gCAAgC,+CAA+C;AAC/E;AACA;AACA,gCAAgC,6CAA6C;AAC7E;AACA;AACA,gCAAgC,+CAA+C;AAC/E;AACA;AACA,gCAAgC,8CAA8C;AAC9E;AACA;AACA,gCAAgC,8CAA8C;AAC9E;AACA;AACA,gCAAgC,+CAA+C;AAC/E;AACA;AACA,gCAAgC,8CAA8C;AAC9E;AACA;AACA,gCAAgC,4CAA4C;AAC5E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,gBAAgB,cAAc;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA,aAAa;AACb;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,wDAAwD;AACjF,SAAS;AACT;AACA;AACA;AACA;AACA,yBAAyB,wDAAwD;AACjF,SAAS;AACT;AACA;AACA;AACA;AACA,2BAA2B,kDAAkD;AAC7E,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,aAAa;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,gBAAgB,cAAc;AAC9B,gBAAgB,yBAAyB;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,6BAA6B;AACpD;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,6BAA6B;AAC5D,iCAAiC,uCAAuC;AACxE,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B,6BAA6B;AACxD;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA,oBAAoB;AACpB,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA,8BAA8B,eAAe;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,gBAAgB,MAAM;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,MAAM;AACtB;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mEAAmE,cAAc;AACjF;AACA;AACA;AACA,8DAA8D,uBAAuB,sBAAsB,cAAc;AACzH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA,4BAA4B,kBAAkB;AAC9C;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,gBAAgB,cAAc;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,gBAAgB,cAAc;AAC9B;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,iCAAiC;AACjC;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,cAAc;AAC9B;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,gBAAgB,cAAc;AAC9B;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,gBAAgB,cAAc;AAC9B;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,sDAAsD;AAC7E,SAAS;AACT;AACA;AACA;AACA;AACA,uBAAuB,sDAAsD;AAC7E,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,MAAM;AACtB;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,aAAa;AACb;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,MAAM;AACtB;AACA,mCAAmC,6CAA6C;AAChF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,MAAM;AACtB;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,cAAc;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA,mCAAmC,6CAA6C;AAChF;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC;AACjC,qBAAqB;AACrB,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA,mCAAmC,6CAA6C;AAChF;AACA;AACA;AACA,uGAAuG,qCAAqC;AAC5I,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,kBAAkB,2CAA2C;AAC7D;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,gBAAgB,MAAM;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,gBAAgB,MAAM;AACtB;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA,yBAAyB;AACzB;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,gBAAgB,MAAM;AACtB;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,cAAc;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,kCAAkC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA,gDAAgD,cAAc;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B;AAC5B;AACA;AACA,qDAAqD,aAAa;AAClE,+BAA+B,sCAAsC;AACrE;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,sDAAsD;AACvD;AACA;AACA;AACA,sCAAsC,SAAS;AAC/C,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC,sBAAsB;AAC/D,yCAAyC,sBAAsB;AAC/D;AACA;AACA;AACA,KAAK;AACL,yCAAyC,sBAAsB;AAC/D,qCAAqC,sBAAsB;AAC3D;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,cAAc;AAChC,wBAAwB,oBAAoB;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC,+BAA+B;AAClE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAE+pD;;;;;;;;;;;;;;;;;;;;;;;;;;;ACh3HnpD;AAEb,gDAAgD;AAChD,uCAAuC;AACvC,+CAA+C;AAE0D;AACV;AAC9B;AACJ;AACS;AACT;AACW;AACI;AACV;AACD;AACe;AACpB;AAKrD,IAAU,kBAAkB,CAgvElC;AAhvED,WAAiB,kBAAkB;IAElC,IAAO,iBAAiB,GAAG,wGAAiC,CAAC;IAG7D,IAAO,oBAAoB,GAAG,2GAAoC,CAAC;IACnE,IAAO,kBAAkB,GAAG,yGAAkC,CAAC;IAI/D,MAAM,CAAC,GAAG,MAAM,CAAC;IACjB,MAAM,CAAC,GAAG,WAAW,CAAC;IAEtB,0GAAsB,EAAE,CAAC;IACzB,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,kBAAkB,EAAE,0EAAc,CAAC,CAAC;IAC3D,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,gBAAgB,EAAE,wEAAY,CAAC,CAAC;IACvD,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,qBAAqB,EAAE,6EAAgB,CAAC,CAAC;IAChE,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,wBAAwB,EAAE,gFAAmB,CAAC,CAAC;IACtE,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,sBAAsB,EAAE,8EAAiB,CAAC,CAAC;IAClE,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,gBAAgB,EAAE,wEAAY,CAAC,CAAC;IACvD,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,kBAAkB,EAAE,0EAAc,CAAC,CAAC;IAC3D,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,0BAA0B,EAAE,mFAAqB,CAAC,CAAC;IAyD1E,MAAM,kBAAkB,GAAG,MAAM,CAAC,UAAU,CAAC,kCAAkC,CAAC,CAAC;IACjF,IAAI,oBAAoB,GAAG,kBAAkB,IAAI,kBAAkB,CAAC,OAAO,CAAC;IAC5E,kBAAkB,CAAC,gBAAgB,CAAC,QAAQ,EAAE,GAAG,EAAE;QAClD,oBAAoB,GAAG,kBAAkB,CAAC,OAAO,CAAC;IACnD,CAAC,CAAC,CAAC;IAEH,MAAM,4BAA6B,SAAQ,iBAAiB;QAuB3D,YACiB,OAAe,EACf,kBAA0B,EAC1B,mBAA2B,EAC3C,aAAqB,EACrB,qBAA6B,CAAC,EAC9B,kBAAiC,IAAI;YAErC,KAAK,EAAE,CAAC;YAPQ,YAAO,GAAP,OAAO,CAAQ;YACf,uBAAkB,GAAlB,kBAAkB,CAAQ;YAC1B,wBAAmB,GAAnB,mBAAmB,CAAQ;YAzB5C;;eAEG;YACK,oBAAe,GAA4B,EAAE,CAAC;YACtD;;;eAGG;YACK,iBAAY,GAA4B,EAAE,CAAC;YAC3C,4BAAuB,GAAqB,IAAI,CAAC;YACjD,yBAAoB,GAAyC,IAAI,CAAC;YAMzD,uBAAkB,GAAkD,EAAE,CAAC,UAAU,CAA4B,IAAI,CAAC,CAAC;YACnH,uBAAkB,GAAgC,EAAE,CAAC,UAAU,CAAU,KAAK,CAAC,CAAC;YAGhF,uBAAkB,GAAgC,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;YAWvF,MAAM,IAAI,GAAG,IAAI,CAAC;YAElB,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC,UAAU,CACpC,IAAI,SAAS,CAAC,aAAa,EAAE,kBAAkB,EAAE,eAAe,CAAC,CACjE,CAAC;YACF,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC,YAAY,CAAC,GAAG,EAAE;;gBACzC,OAAO,CAAC,UAAI,CAAC,gBAAgB,EAAE,0CAAE,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;YAChD,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,kBAAkB,GAAG,EAAE,CAAC,QAAQ,CAAC;gBACrC,IAAI,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,kBAAkB,EAAE;gBACrC,KAAK,EAAE,CAAC,QAAQ,EAAE,EAAE;oBACnB,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,CAAC;oBAChD,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE;wBACnC,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;wBAClC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;qBAC9B;gBACF,CAAC;aACD,CAAC,CAAC;YAEH,4DAA4D;YAC5D,MAAM,gBAAgB,GAAG,EAAE,CAAC,YAAY,CAAC,GAAG,EAAE;gBAC7C,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBAC1C,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,qBAAqB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5D,CAAC,CAAC,CAAC;YACH,MAAM,oBAAoB,GAAG,CAAC,CAAC,QAAQ,CACtC,GAAG,EAAE;gBACJ,uEAAuE;gBACvE,kEAAkE;gBAClE,sEAAsE;gBACtE,EAAE;gBACF,kEAAkE;gBAClE,oEAAoE;gBACpE,IACC,CAAC,gBAAgB,EAAE,GAAG,CAAC,CAAC;uBACrB,CAAC,IAAI,CAAC,kBAAkB,EAAE,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC,UAAU,EAAE,CAAC,EACrE;oBACD,IAAI,CAAC,oBAAoB,EAAE;iBAC3B;YACF,CAAC,EACD,IAAI,EACJ,EAAC,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAC,CAC/B;YACD,gBAAgB,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,EAAE;gBACtC,IAAI,OAAO,GAAG,CAAC,EAAE;oBAChB,oBAAoB,EAAE,CAAC;iBACvB;YACF,CAAC,CAAC,CAAC;YACH,wCAAwC;YACxC,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC,UAAU,EAAE,EAAE;gBAChD,IAAI,UAAU,EAAE;oBACf,oBAAoB,EAAE,CAAC;iBACvB;YACF,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,8BAA8B,GAAG,EAAE,CAAC,YAAY,CAAC,GAAG,EAAE;gBAC1D,OAAO,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAClC,CAAC,CAAC,CAAC;YAEH,+CAA+C;YAC/C,IAAI,CAAC,iBAAiB,CAAC,CAAC,OAAgB,EAAE,EAAE;gBAC3C,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC;gBAE3C,IAAI,SAAS,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACxC,+DAA+D;gBAC/D,6BAA6B;gBAC7B,IAAI,CAAC,UAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,aAAa,EAAE,GAAE;oBAChC,SAAS,GAAG,IAAI,SAAS,EAAE,CAAC;oBAC5B,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;iBACjC;gBACD,qDAAqD;gBACrD,SAAS,CAAC,qBAAqB,CAAC,SAAS,CAAC,qBAAqB,EAAE,GAAG,CAAC,CAAC,CAAC;YACxE,CAAC,CAAC,CAAC;QACJ,CAAC;QAED,oBAAoB,CAAC,QAAgB,CAAC;YACrC,IAAI,KAAK,GAAG,CAAC,EAAE;gBACd,IAAI,IAAI,CAAC,oBAAoB,KAAK,IAAI,EAAE;oBACvC,8CAA8C;oBAC9C,YAAY,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;iBACxC;gBACD,IAAI,CAAC,oBAAoB,GAAG,UAAU,CAAC,GAAG,EAAE;oBAC3C,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;oBACjC,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC;gBAC9B,CAAC,EAAE,KAAK,CAAC,CAAC;gBACV,OAAO;aACP;YAED,IAAI,IAAI,CAAC,oBAAoB,KAAK,IAAI,EAAE;gBACvC,OAAO,CAAC,qCAAqC;aAC7C;YAED,IAAI,IAAI,CAAC,uBAAuB,KAAK,IAAI,EAAE;gBAC1C,0DAA0D;gBAC1D,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,GAAG,EAAE;oBACxC,2CAA2C;oBAC3C,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;gBACjC,CAAC,CAAC,CAAC;gBACH,OAAO;aACP;YAED,IAAI,CAAC,aAAa,EAAE,CAAC;QACtB,CAAC;QAEO,aAAa,CAAC,SAAwB,IAAI;;YACjD,qCAAqC;YACrC,IAAI,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE;gBACvF,OAAO,CAAC,CAAC,QAAQ,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;aACjF;YAED,IAAI,IAAI,CAAC,8BAA8B,EAAE,EAAE;gBAC1C,OAAO,CAAC,CAAC,QAAQ,EAAE,CAAC,MAAM,CACzB,IAAI,KAAK,CAAC,uDAAuD,CAAC,CAClE,CAAC,OAAO,EAAE,CAAC;aACZ;YAED,IAAI,kBAAkB,GAAG,CAAC,MAAM,KAAK,SAAS,CAAC,IAAI,CAAC,MAAM,KAAK,OAAO,CAAC,CAAC;YACxE,IAAI,kBAAkB,EAAE;gBACvB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;aAC9B;YAED,MAAM,cAAc,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAE/C,sEAAsE;YACtE,oEAAoE;YACpE,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC;YAC5C,IAAI,CAAC,YAAY,GAAG,cAAc,CAAC;YACnC,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;YAE1B,MAAM,gBAAgB,GAAG,CAAC,CAAC,SAAS,CAAC,cAAc,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC;YACjF,MAAM,WAAW,GAAwB;gBACxC,MAAM,EAAE,0BAA0B;gBAClC,WAAW,EAAE,IAAI,CAAC,kBAAkB;gBACpC,SAAS,EAAE,OAAC,cAAc,aAAd,cAAc,uBAAd,cAAc,CAAE,IAAI,EAAE,CAAC,mCAAI,EAAE;gBACzC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC;aAC1C,CAAC;YACF,IAAI,MAAM,KAAK,IAAI,EAAE;gBACpB,WAAW,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;aAC/B;YACD,kDAAkD;YAClD,IAAI,CAAC,eAAc,aAAd,cAAc,uBAAd,cAAc,CAAE,OAAO,EAAE,GAAE;gBAC/B,WAAW,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;aAC7B;YAED,2CAA2C;YAC3C,MAAM,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACrD,IAAI,kBAAkB,EAAE;gBACvB,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC3C,WAAW,CAAC,oBAAoB,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;aAC7D;YACD,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;YAE/B,MAAM,OAAO,GAAG,CAAC,CAAC,IAAI,CAAC;gBACtB,GAAG,EAAE,IAAI,CAAC,OAAO;gBACjB,MAAM,EAAE,MAAM;gBACd,IAAI,EAAE,WAAW;gBACjB,QAAQ,EAAE,MAAM;gBAChB,OAAO,EAAE,KAAK;aACd,CAAC,CAAC;YACH,IAAI,CAAC,uBAAuB,GAAG,OAAO,CAAC;YASvC,MAAM,IAAI,GAAG,IAAI,CAAC;YAElB,SAAS,0BAA0B,CAAC,cAAmB;gBACtD,MAAM,OAAO,GAA4B,CAAC,CAAC,GAAG,CAC7C,cAAc,EACd,CAAC,MAAM,EAAE,mBAAmB,CAAC,CAC7B,CAAC;gBACF,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;oBAChC,OAAO;iBACP;gBAED,KAAK,MAAM,SAAS,IAAI,OAAO,EAAE;oBAChC,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;oBACpC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE;wBACzB,SAAS;qBACT;oBAED,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,SAAS,CAAC,EAAE;wBAChD,SAAS;qBACT;oBACD,MAAM,SAAS,GAAG,gBAAgB,CAAC,SAAS,CAAC,CAAC;oBAE9C,MAAM,KAAK,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;oBACjC,IAAI,KAAK,CAAC,OAAO,EAAE;wBAClB,OAAO,CAAC,GAAG,EAAE,CAAC,6BAA6B,CAAC,SAAS,CAAC,CAAC;qBACvD;yBAAM;wBACN,sEAAsE;wBACtE,mBAAmB;wBACnB,qCAAqC;wBACrC,OAAO,CAAC,GAAG,EAAE,CAAC,2BAA2B,CACxC,SAAS,EACT,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,KAAK,CAAC,OAAO,KAAK,QAAQ,CAAC,CAAC,CACpE,CAAC;qBACF;iBACD;YACF,CAAC;YAED,SAAS,yBAAyB,CAAC,cAAmB;gBACrD,IAAI,CAAC,cAAc,EAAE;oBACpB,OAAO;iBACP;gBAED,uEAAuE;gBACvE,yDAAyD;gBACzD,MAAM,OAAO,GAAG,CAAC,CAAC,GAAG,CAAC,cAAc,EAAE,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC,CAAC;gBAC7D,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,cAAc,CAAC,IAAI,EAAE,CAAC,EAAE;oBACrE,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;wBAChC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;qBAC7B;iBACD;gBACD,6BAA6B;gBAC7B,MAAM,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,cAAc,EAAE,CAAC,MAAM,EAAE,iBAAiB,CAAC,CAAC,CAAC;gBACrE,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE;oBAClC,cAAc,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;iBACjC;gBAED,+CAA+C;gBAC/C,MAAM,cAAc,GAAG,CAAC,CAAC,GAAG,CAAC,cAAc,EAAE,CAAC,MAAM,EAAE,oBAAoB,CAAC,CAAC,CAAC;gBAC7E,IAAI,OAAO,cAAc,KAAK,QAAQ,EAAE;oBACvC,cAAc,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;iBAC9C;gBAED,qEAAqE;gBACrE,4EAA4E;gBAC5E,6DAA6D;gBAC7D,MAAM,YAAY,GAAG,CAAC,CAAC,GAAG,CAAC,cAAc,EAAE,CAAC,MAAM,EAAE,uBAAuB,CAAC,EAAE,IAAI,CAAC,CAAC;gBACpF,IAAI,YAAY,EAAE;oBACjB,cAAc,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;iBAC1C;YACF,CAAC;YAED,OAAO,CAAC,IAAI,CAAC,UAAU,QAAQ;gBAC9B,yBAAyB,CAAC,QAAQ,CAAC,CAAC;gBACpC,0BAA0B,CAAC,QAAQ,CAAC,CAAC;gBAErC,2DAA2D;gBAC3D,kBAAkB;gBAClB,MAAM,WAAW,GAChB,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,SAAS,CAAC;uBACpC,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,QAAQ,CAAC;uBACtC,CAAC,cAAc,CAAC,YAAY,EAAE,CAAC,CAAC;gBACpC,IAAI,WAAW,EAAE;oBAChB,cAAc,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;iBACxC;gBAED,6DAA6D;gBAC7D,8DAA8D;gBAC9D,iCAAiC;gBACjC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC,aAAa,EAAE,EAAE;oBAC7C,MAAM,aAAa,GAAG,CAAC,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC,CAAC;oBACjE,IAAI,CAAC,OAAO,aAAa,KAAK,QAAQ,CAAC,IAAI,CAAC,aAAa,KAAK,EAAE,CAAC,EAAE;wBAClE,IAAI,CAAC,gBAAgB,CAAC,IAAI,SAAS,CAAC,aAAa,CAAC,CAAC,CAAC;qBACpD;iBACD;YACF,CAAC,CAAC,CAAC;YAEH,OAAO,CAAC,IAAI,CAAC,CAAC,aAAwB,EAAE,EAAE;gBACzC,IAAI,OAAO,aAAa,CAAC,YAAY,KAAK,QAAQ,EAAE;oBACnD,0BAA0B,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;oBACvD,yBAAyB,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;iBACtD;gBAED,oDAAoD;gBACpD,KAAK,MAAM,EAAE,IAAI,cAAc,EAAE;oBAChC,sCAAsC;oBACtC,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,SAAS,EAAE,EAAE;wBAC7B,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC,GAAG,cAAc,CAAC,EAAE,CAAC,CAAC;qBAC9C;iBACD;gBAED,qEAAqE;gBACrE,+DAA+D;YAChE,CAAC,CAAC,CAAC;YAEH,OAAO,CAAC,MAAM,CAAC,GAAG,EAAE;gBACnB,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;gBACpC,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;gBACvB,IAAI,kBAAkB,EAAE;oBACvB,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;iBAC/B;YACF,CAAC,CAAC,CAAC;YAEH,OAAO,OAAO,CAAC;QAChB,CAAC;QAEM,mBAAmB,CAAC,UAAkB,EAAE;YAC9C,IAAI,IAAI,CAAC,8BAA8B,EAAE,EAAE;gBAC1C,6CAA6C;gBAC7C,MAAM,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC;gBAC9B,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,qBAAqB,EAAE,CAAC,CAAC;gBAEjE,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBAC7B,MAAM,KAAK,GAAG,WAAW,CAAC,GAAG,EAAE;oBAC9B,IAAI,CAAC,IAAI,CAAC,8BAA8B,EAAE,EAAE;wBAC3C,aAAa,CAAC,KAAK,CAAC,CAAC;wBACrB,QAAQ,CAAC,OAAO,EAAE,CAAC;qBACnB;yBAAM,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,GAAG,OAAO,EAAE;wBAC9C,aAAa,CAAC,KAAK,CAAC,CAAC;wBACrB,QAAQ,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC,CAAC;qBAC7D;gBACF,CAAC,EAAE,GAAG,CAAC,CAAC;gBAER,OAAO,MAAM,CAAC;aACd;YAED,OAAO,IAAI,CAAC,qBAAqB,EAAE,CAAC;QACrC,CAAC;QAEO,qBAAqB;YAC5B,wDAAwD;YACxD,IAAI,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE;gBACpC,OAAO,CAAC,CAAC,QAAQ,EAAE,CAAC,OAAO,EAAE,CAAC,OAAO,EAAE,CAAC;aACxC;YACD,OAAO,IAAI,CAAC,aAAa,EAAE,CAAC;QAC7B,CAAC;QAEM,mBAAmB;YACzB,OAAO,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAChC,CAAC;QAED;;;;WAIG;QACH,IAAW,cAAc;YACxB,gEAAgE;YAChE,yBAAyB;YACzB,IAAI,eAAe,GAA4B,EAAE,CAAC;YAClD,CAAC,CAAC,QAAQ,CAAC,eAAe,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;YAErE,OAAO,CAAC,CAAC,SAAS,CAAC,eAAe,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC;QACjE,CAAC;QAEM,gBAAgB;YACtB,IAAI,IAAI,CAAC,8BAA8B,EAAE,EAAE;gBAC1C,OAAO,CAAC,CAAC,QAAQ,EAAE;qBACjB,MAAM,CAAC,IAAI,KAAK,CAAC,+DAA+D,CAAC,CAAC;qBAClF,OAAO,EAAE,CAAC;aACZ;YACD,OAAO,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;QACtC,CAAC;QAEM,cAAc;YACpB,IAAI,IAAI,CAAC,8BAA8B,EAAE,EAAE;gBAC1C,OAAO,CAAC,CAAC,QAAQ,EAAE;qBACjB,MAAM,CAAC,IAAI,KAAK,CAAC,+DAA+D,CAAC,CAAC;qBAClF,OAAO,EAAE,CAAC;aACZ;YAED,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC1C,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,EAAE;gBACzB,yEAAyE;gBACzE,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;gBAC1B,SAAS,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;gBAEnC,0BAA0B;gBAC1B,OAAO,CAAC,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,CAAC;aAC5C;YAED,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;YAE9B,MAAM,WAAW,GAAwB;gBACxC,MAAM,EAAE,2BAA2B;gBACnC,WAAW,EAAE,IAAI,CAAC,mBAAmB;gBACrC,SAAS,EAAE,SAAS,CAAC,IAAI;aACzB,CAAC;YAEF,MAAM,OAAO,GAAG,CAAC,CAAC,IAAI,CAAC;gBACtB,GAAG,EAAE,IAAI,CAAC,OAAO;gBACjB,MAAM,EAAE,MAAM;gBACd,IAAI,EAAE,WAAW;gBACjB,QAAQ,EAAE,MAAM;gBAChB,OAAO,EAAE,KAAK;aACd,CAAC,CAAC;YACH,IAAI,CAAC,uBAAuB,GAAG,OAAO,CAAC;YAEvC,OAAO,CAAC,IAAI,CAAC;gBACZ,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;gBAC1B,SAAS,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;YACpC,CAAC,CAAC,CAAC;YAEH,mEAAmE;YACnE,oEAAoE;YACpE,sDAAsD;YAEtD,OAAO,CAAC,MAAM,CAAC,GAAG,EAAE;gBACnB,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;gBACpC,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC;YAEH,OAAO,OAAO,CAAC;QAChB,CAAC;QAEM,uBAAuB,CAAC,QAAmC;YACjE,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;YAClC,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;QAChC,CAAC;KACD;IAED,MAAM,SAAS;QAwBd,YAAY,OAAe,EAAE,EAAE,iBAAyB,CAAC,EAAE,gBAA+B,EAAE;YAnB5F;;;;;;;eAOG;YACa,0BAAqB,GAA+B,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAErF;;;eAGG;YACc,yBAAoB,GACpC,EAAC,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAC,CAAC;YAElC,iBAAY,GAAgC,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;YAGhF,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YAEhC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,EAAE;gBAC/B,4EAA4E;gBAC5E,0EAA0E;gBAC1E,0EAA0E;gBAC1E,8CAA8C;gBAC9C,IAAI,CAAC,OAAQ,OAAmB,KAAK,QAAQ,CAAC,EAAE;oBAC/C,MAAM,IAAI,KAAK,CAAC,gDAAgD,GAAG,CAAC,OAAO,OAAO,CAAC,CAAC,CAAC;iBACrF;YACF,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC;YACpD,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,UAAU,CAAC,aAAa,aAAb,aAAa,cAAb,aAAa,GAAI,EAAE,CAAC,CAAC;QAClD,CAAC;QAEM,OAAO;YACb,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;YACzB,OAAO,CAAC,IAAI,KAAK,EAAE,CAAC,CAAC;QACtB,CAAC;QAEM,aAAa;YACnB,OAAO,CAAC,IAAI,CAAC,oBAAoB,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;QACjE,CAAC;QAEM,UAAU;YAChB,OAAO,CAAC,IAAI,CAAC,qBAAqB,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,GAAG,CAAC,CAAC;QACzE,CAAC;KACD;IAED,MAAM,sBAAsB,GAAG,cAAc,CAAC,CAAC,yCAAyC;IAExF,oBAAoB;IACpB,MAAM,UAAU,GAAG,gEAAO,CAAC;QAC1B,iEAAQ,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC;QAC1B,kEAAS,CAAC,EAAE,CAAC;KACb,CAAC,CAAC;IAEH,MAAM,kBAAkB,GAAG,iEAAQ,CAAC;QACnC,UAAU,EAAE,iEAAQ,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC;QAC/B,gBAAgB,EAAE,iEAAQ,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC;QAErC,UAAU,EAAE,iEAAQ,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC;aACtD,MAAM,CACN,UAAU,KAAa;YACtB,8DAA8D;YAC9D,0BAA0B;YAC1B,OAAO,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACpC,CAAC,EACD,EAAC,OAAO,EAAE,4EAA4E,EAAC,CACvF;QACF,gBAAgB,EAAE,iEAAQ,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE;QAE/C,aAAa,EAAE,iEAAQ,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE;QACnD,SAAS,EAAE,UAAU,CAAC,QAAQ,EAAE;QAChC,UAAU,EAAE,iEAAQ,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE;QAC1C,SAAS,EAAE,UAAU,CAAC,QAAQ,EAAE;QAChC,iBAAiB,EAAE,iEAAQ,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE;QAC/D,eAAe,EAAE,iEAAQ,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE;QAC9C,gBAAgB,EAAE,kEAAS,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE;KACvD,CAAC,CAAC;IAIH,MAAM,kBAAkB,GAAG,iEAAQ;IAClC,UAAU;IACV,iEAAQ,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;IACjB,YAAY;IACZ,8DAAK,EAAE,CACP,CAAC;IAEF,MAAM,sBAAsB;QAS3B,YACiB,QAAgB,EAChB,QAA4B;YAD5B,aAAQ,GAAR,QAAQ,CAAQ;YAChB,aAAQ,GAAR,QAAQ,CAAoB;YAVtC,kBAAa,GAAW,CAAC,CAAC;YAC1B,qBAAgB,GAAW,CAAC,CAAC;YAC7B,oBAAe,GAAW,CAAC,CAAC;YAC5B,oBAAe,GAAW,CAAC,CAAC;YAC5B,8BAAyB,GAAW,CAAC,CAAC;YAQ5C,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,UAAU,IAAI,WAAW,CAAC;QACtD,CAAC;KACD;IAQD,SAAS,2BAA2B,CACnC,KAAkB,EAClB,MAAS;QAET,MAAM,oBAAoB,GAAG,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QAElD,MAAM,UAAU,GAA8B,EAAE,CAAC,YAAY,CAAC;YAC7D,IAAI,EAAE,oBAAoB;YAC1B,KAAK,EAAE,CAAC,QAAW,EAAE,EAAE;gBACtB,MAAM,gBAAgB,GAAG,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;gBACpD,IAAI,gBAAgB,CAAC,OAAO,EAAE;oBAC7B,oBAAoB,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;oBAC5C,UAAU,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;oBACvC,UAAU,CAAC,mBAAmB,CAAC,EAAE,CAAC,CAAC;iBACnC;qBAAM;oBACN,UAAU,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;oBACzD,mDAAmD;oBACnD,UAAU,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;wBACxE,OAAO;4BACN,IAAI,EAAE,KAAK,CAAC,IAAI;4BAChB,OAAO,EAAE,KAAK,CAAC,OAAO;yBACc,CAAC;oBACvC,CAAC,CAAC,CAAC,CAAC;iBACJ;YACF,CAAC;SACD,CAA8B,CAAC;QAEhC,UAAU,CAAC,qBAAqB,GAAG,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QACvD,UAAU,CAAC,mBAAmB,GAAG,EAAE,CAAC,UAAU,CAAC,EAAiC,CAAC,CAAC;QAClF,UAAU,CAAC,UAAU,GAAG,EAAE,CAAC,YAAY,CAAC,GAAG,EAAE;YAC5C,MAAM,MAAM,GAAG,UAAU,CAAC,mBAAmB,EAAE,CAAC;YAChD,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;QAEH,OAAO,UAAU,CAAC;IACnB,CAAC;IAED,MAAM,uBAAuB;QAa5B,YAAY,QAA4B;;YACvC,IAAI,CAAC,UAAU,GAAG,2BAA2B,CAC5C,QAAQ,CAAC,UAAU,EACnB,kBAAkB,CAAC,KAAK,CAAC,UAAU,CACnC,CAAC;YACF,IAAI,CAAC,gBAAgB,GAAG,2BAA2B,CAClD,QAAQ,CAAC,gBAAgB,EACzB,kBAAkB,CAAC,KAAK,CAAC,gBAAgB,CACzC,CAAC;YAEF,IAAI,CAAC,UAAU,GAAG,2BAA2B,CAC5C,cAAQ,CAAC,UAAU,mCAAI,EAAE,EACzB,kBAAkB,CAAC,KAAK,CAAC,UAAU,CACnC,CAAC;YACF,IAAI,CAAC,gBAAgB,GAAG,2BAA2B,CAClD,cAAQ,CAAC,gBAAgB,mCAAI,EAAE,EAC/B,kBAAkB,CAAC,KAAK,CAAC,gBAAgB,CACzC,CAAC;YAEF,IAAI,CAAC,aAAa,GAAG,2BAA2B,CAC/C,cAAQ,CAAC,aAAa,mCAAI,EAAE,EAC5B,kBAAkB,CAAC,KAAK,CAAC,aAAa,CACtC,CAAC;YACF,IAAI,CAAC,SAAS,GAAG,2BAA2B,CAC3C,cAAQ,CAAC,SAAS,mCAAI,EAAE,EACxB,kBAAkB,CAAC,KAAK,CAAC,SAAS,CAClC,CAAC;YACF,IAAI,CAAC,UAAU,GAAG,2BAA2B,CAC5C,cAAQ,CAAC,UAAU,mCAAI,EAAE,EACzB,kBAAkB,CAAC,KAAK,CAAC,UAAU,CACnC,CAAC;YACF,IAAI,CAAC,SAAS,GAAG,2BAA2B,CAC3C,cAAQ,CAAC,SAAS,mCAAI,EAAE,EACxB,kBAAkB,CAAC,KAAK,CAAC,SAAS,CAClC,CAAC;YACF,IAAI,CAAC,iBAAiB,GAAG,2BAA2B,CACnD,cAAQ,CAAC,iBAAiB,mCAAI,EAAE,EAChC,kBAAkB,CAAC,KAAK,CAAC,iBAAiB,CAC1C,CAAC;YACF,IAAI,CAAC,eAAe,GAAG,2BAA2B,CACjD,cAAQ,CAAC,eAAe,mCAAI,EAAE,EAC9B,kBAAkB,CAAC,KAAK,CAAC,eAAe,CACxC,CAAC;YAEF,IAAI,CAAC,gBAAgB,GAAG,2BAA2B,CAClD,cAAQ,CAAC,gBAAgB,mCAAI,KAAK,EAClC,kBAAkB,CAAC,KAAK,CAAC,gBAAgB,CACzC,CAAC;QACH,CAAC;QAEM,QAAQ;YACd,OAAO;gBACN,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE;gBAC7B,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,EAAE;gBACzC,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE;gBAC7B,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,EAAE;gBACzC,aAAa,EAAE,IAAI,CAAC,aAAa,EAAE;gBACnC,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE;gBAC3B,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE;gBAC7B,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE;gBAC3B,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,EAAE;gBAC3C,eAAe,EAAE,IAAI,CAAC,eAAe,EAAE;gBACvC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,EAAE;aACzC,CAAC;QACH,CAAC;QAED,OAAO;YACN,0EAA0E;YAC1E,OAAO,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE;mBAC/B,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE;mBAClC,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE;mBAC5B,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE;mBAClC,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE;mBAC/B,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE;mBAC3B,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE;mBAC5B,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE;mBAC3B,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE;mBACnC,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE;mBACjC,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,CAAC;QACxC,CAAC;KACD;IAED,IAAK,kBAGJ;IAHD,WAAK,kBAAkB;QACtB,mEAAQ;QACR,2DAAI;IACL,CAAC,EAHI,kBAAkB,KAAlB,kBAAkB,QAGtB;IAED,MAAM,mBAAoB,SAAQ,qBAAqB;QA2BtD,YACkB,gBAA8B,EAC9B,wBAAkD,EAClD,kBAAiE,EAClF,qBAAsC;YAEtC,KAAK,EAAE,CAAC;YALS,qBAAgB,GAAhB,gBAAgB,CAAc;YAC9B,6BAAwB,GAAxB,wBAAwB,CAA0B;YAClD,uBAAkB,GAAlB,kBAAkB,CAA+C;YA3BnE,0BAAqB,GAAgC,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;YAE1E,SAAI,GAA2C,EAAE,CAAC,UAAU,CAAqB,kBAAkB,CAAC,QAAQ,CAAC,CAAC;YAE9H,qBAAgB,GAAY,IAAI,CAAC;YAIjC,2BAAsB,GAAgC,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;YAG3E,gBAAW,GAAgC,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;YAIhE,kBAAa,GAA+B,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;YAC9D,iBAAY,GAA+B,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;YAC7D,uBAAkB,GAA+B,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;YAI3D,2BAAsB,GAAe,GAAG,EAAE;YAClD,CAAC,CAAC;YASD,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,GAAG,CAAC;YAE5B,IAAI,CAAC,eAAe,iBAChB;gBACF,qBAAqB,EAAE,mDAAmD;gBAC1E,oBAAoB,EAAE,mFAAmF;aACzG,EACE,qBAAqB,CACxB;YAED,IAAI,eAAe,GAAG,kBAAkB,EAAE,CAAC;YAC3C,IAAI,eAAe,KAAK,IAAI,EAAE;gBAC7B,eAAe,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;aAC3C;YAED,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC,UAAU,CAAC,IAAI,uBAAuB,CAAC,eAAe,CAAC,CAAC,CAAC;YAExE,IAAI,CAAC,kBAAkB,GAAG,EAAE,CAAC,QAAQ,CAAC,GAAG,EAAE;gBAC1C,IAAI,IAAI,CAAC,IAAI,EAAE,KAAK,kBAAkB,CAAC,QAAQ,EAAE;oBAChD,OAAO,sBAAsB,CAAC;iBAC9B;gBACD,OAAO,IAAI,CAAC;YACb,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,sBAAsB,GAAG,EAAE,CAAC,QAAQ,CAAC,GAAG,EAAE;gBAC9C,IAAI,IAAI,CAAC,qBAAqB,EAAE,EAAE;oBACjC,OAAO,KAAK,CAAC;iBACb;gBAED,IAAI,gBAAgB,EAAE,KAAK,EAAE,EAAE;oBAC9B,yEAAyE;oBACzE,OAAO,KAAK,CAAC;iBACb;gBACD,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC;YAC9B,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC,QAAQ,CAAC,GAAG,EAAE;gBACzC,OAAO,CAAC,IAAI,CAAC,qBAAqB,EAAE,CAAC;YACtC,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,0BAA0B,GAAG,EAAE,CAAC,YAAY,CAAC,GAAW,EAAE;gBAC9D,OAAO,IAAI,CAAC,sBAAsB,EAAE,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,cAAc,CAAC;YACzE,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC,YAAY,CAAC,GAAW,EAAE;gBACnD,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,cAAc,CAAC;YAC1D,CAAC,CAAC,CAAC;YAEH,2CAA2C;YAC3C,IAAI,CAAC,oBAAoB,GAAG,EAAE,CAAC,YAAY,CAAC,GAAY,EAAE;gBACzD,OAAO,IAAI,CAAC,IAAI,EAAE,KAAK,kBAAkB,CAAC,QAAQ,CAAC;YACpD,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,OAA2B,EAAE,EAAE;gBACnD,IAAI,OAAO,KAAK,kBAAkB,CAAC,QAAQ,EAAE;oBAC5C,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC;iBACnC;qBAAM,IAAI,OAAO,KAAK,kBAAkB,CAAC,IAAI,EAAE;oBAC/C,IAAI,CAAC,KAAK,CAAC,6BAA6B,CAAC,CAAC;iBAC1C;YACF,CAAC,CAAC,CAAC;QACJ,CAAC;QAEO,iBAAiB;YACxB,OAAO,kBAAkB,CAAC,KAAK,CAAC;gBAC/B,UAAU,EAAE,oBAAoB;gBAChC,gBAAgB,EAAE,uBAAuB,GAAG,IAAI,CAAC,eAAe,CAAC,qBAAqB;gBACtF,aAAa,EAAE,KAAK;aACpB,CAAC,CAAC;QACJ,CAAC;QAED,MAAM,CAAC,KAAwB,EAAE,EAAO;YACvC,IAAI,cAAc,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC/C,IAAI,cAAc,KAAK,IAAI,EAAE;gBAC5B,cAAc,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;aAC1C;YACD,IAAI,CAAC,IAAI,CAAC,IAAI,uBAAuB,CAAC,cAAc,CAAC,CAAC,CAAC;QACxD,CAAC;QAED,qBAAqB;YACpB,IAAI,CAAC,sBAAsB,CAAC,CAAC,IAAI,CAAC,sBAAsB,EAAE,CAAC,CAAC;QAC7D,CAAC;QAED,UAAU;YACT,IAAI,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;QACvC,CAAC;QAED,SAAS,CAAC,KAAwB;YACjC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,OAAO,EAAE,EAAE;gBAC3B,iEAAiE;gBACjE,+BAA+B;gBAC/B,KAAK,CAAC,+CAA+C,CAAC,CAAC;gBACvD,OAAO;aACP;YAED,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE,CAAC;YACxC,QAAQ,CAAC,gBAAgB,GAAG,IAAI,CAAC;YACjC,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;YAElC,IAAI,IAAI,CAAC,IAAI,EAAE,KAAK,kBAAkB,CAAC,IAAI,EAAE;gBAC5C,wCAAwC;gBACxC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBACnB,OAAO;aACP;YAED,IAAI,CAAC,iCAAiC,CAAC,QAAQ,CAAC,CAAC;QAClD,CAAC;QAEM,iCAAiC,CAAC,QAA4B;YACpE,IAAI,IAAI,CAAC,qBAAqB,EAAE,EAAE;gBACjC,KAAK,CAAC,kDAAkD,CAAC,CAAC;gBAC1D,OAAO;aACP;YAED,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC;iBAC5B,IAAI,CAAC,CAAC,KAAa,EAAE,EAAE;gBACvB,IAAI,KAAK,KAAK,EAAE,EAAE;oBACjB,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,CAAC;iBACzB;YACF,CAAC,CAAC,CAAC;QACL,CAAC;QAEO,eAAe,CAAC,QAA4B;YACnD,MAAM,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC;YAE9B,gBAAgB;YAChB,iDAAiD;YACjD,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC9C,IAAI,aAAa,KAAK,EAAE,EAAE;gBACzB,OAAO,QAAQ,CAAC,MAAM,CAAC,uDAAuD,CAAC,CAAC,OAAO,EAAE,CAAC;aAC1F;YAED,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;YAEjC,MAAM,KAAK,GAAG,CAAC,CAAC,qCAAqC,CAAC,CAAC;YACvD,MAAM,MAAM,GAAG,CAAC,CAAC,8BAA8B,CAAC,CAAC;YAEjD,2EAA2E;YAC3E,IAAI,iBAAiB,GAAY,KAAK,CAAC;YACvC,MAAM,gBAAgB,GAAG,KAAK,CAAC;YAC/B,MAAM,gBAAgB,GAAG,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC;YAChD,IAAI,mBAAmB,GAAyC,IAAI,CAAC;YAErE,MAAM,OAAO,GAAG,IAAI,CAAC,sBAAsB,GAAG,GAAG,EAAE;gBAClD,iBAAiB,GAAG,IAAI,CAAC;gBAEzB,MAAM,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;gBAC3C,IAAI,YAAY,EAAE;oBACjB,YAAY,CAAC,YAAY,CAAC,CAAC;iBAC3B;gBACD,IAAI,mBAAmB,EAAE;oBACxB,aAAa,CAAC,mBAAmB,CAAC,CAAC;iBACnC;gBACD,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC;gBAElC,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;gBAElC,IAAI,IAAI,CAAC,sBAAsB,KAAK,OAAO,EAAE;oBAC5C,IAAI,CAAC,sBAAsB,GAAG,GAAG,EAAE;oBACnC,CAAC,CAAC;iBACF;YACF,CAAC;YAED,MAAM,YAAY,GAAG,UAAU,CAAC,GAAG,EAAE;gBACpC,QAAQ,CAAC,MAAM,CAAC,mCAAmC,CAAC,CAAC;gBACrD,OAAO,EAAE,CAAC;YACX,CAAC,EAAE,gBAAgB,CAAC,CAAC;YAErB,IAAI,CAAC,wBAAwB,EAAE,CAAC,IAAI,CACnC,GAAG,EAAE;gBACJ,IAAI,iBAAiB,EAAE;oBACtB,OAAO;iBACP;gBAED,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;gBAClC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC;gBAE5C,kEAAkE;gBAClE,iEAAiE;gBACjE,gCAAgC;gBAChC,MAAM,UAAU,GAAG,CAAC,oBAAoB;sBACrC,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE;sBACpB,GAAG;sBACH,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,KAAK,CAAC,CAAC,wCAAwC;iBAC5E,CAAC;gBACF,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;gBAEpC,8DAA8D;gBAC9D,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC;gBAClC,IAAI;oBACH,MAAM,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;iBACxC;gBAAC,OAAO,CAAC,EAAE;oBACX,0EAA0E;oBAC1E,IAAI,OAAO,IAAI,OAAO,CAAC,KAAK,EAAE;wBAC7B,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;qBACjB;iBACD;gBAED,mBAAmB,GAAG,WAAW,CAAC,GAAG,EAAE;oBACtC,MAAM,WAAW,GAAG,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;oBACzC,IAAI,WAAW,EAAE;wBAChB,OAAO,EAAE,CAAC;wBACV,CAAC,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;wBAE3B,4CAA4C;wBAC5C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;wBACnB,QAAQ,CAAC,OAAO,EAAE,CAAC;wBACnB,OAAO;qBACP;oBAED,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC,OAAO,EAAE,GAAG,gBAAgB,GAAG,gBAAgB,EAAE;wBACjE,OAAO,EAAE,CAAC;wBACV,QAAQ,CAAC,MAAM,CAAC,mCAAmC,CAAC,CAAC;qBACrD;gBACF,CAAC,EAAE,IAAI,CAAC,CAAC;gBAET,MAAM,CAAC,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;oBAC9C,uEAAuE;oBACvE,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;oBAC7D,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;oBAE1C,OAAO,EAAE,CAAC;oBAEV,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,IAAI,CAAC,OAAO,QAAQ,KAAK,QAAQ,CAAC,EAAE;wBAC1D,QAAQ,CAAC,MAAM,CAAC,+CAA+C,CAAC,CAAC;qBACjE;yBAAM;wBACN,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;4BACtB,IAAI,YAAY,CAAC;4BACjB,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE;gCAC1B,YAAY,GAAG,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC;6BACrC;iCAAM;gCACN,YAAY,GAAG,0CAA0C,CAAC;6BAC1D;4BACD,QAAQ,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;yBAC9B;6BAAM;4BACN,uCAAuC;4BACvC,QAAQ,CAAC,MAAM,CAAC,kDAAkD,CAAC,CAAC;yBACpE;qBACD;gBACF,CAAC,CAAC,CAAC;gBAEH,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YACzB,CAAC,EACD,GAAG,EAAE;gBACJ,IAAI,iBAAiB,EAAE;oBACtB,IAAI,QAAQ,CAAC,KAAK,EAAE,KAAK,SAAS,EAAE;wBACnC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,8DAA8D;qBACnF;oBACD,OAAO;iBACP;gBAED,OAAO,EAAE,CAAC;gBACV,QAAQ,CAAC,MAAM,CAAC,qEAAqE,CAAC,CAAC;YACxF,CAAC,CACD,CAAC;YAEF,OAAO,QAAQ,CAAC,OAAO,EAAE,CAAC;QAC3B,CAAC;QAED,OAAO,CAAC,KAAwB,EAAE,EAAO;YACxC,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAC/B,CAAC;KACD;IAED,WAAW;IAEX,MAAM,iBAAiB;QAMtB;YALQ,oBAAe,GAAoC,EAAE,CAAC,eAAe,CAAC,EAAc,CAAC,CAAC;YAM7F,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,8BAA8B,CAAC,CAAC;YAEtD,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,OAAO,EAAE,sBAAsB,EAAE,CAAC,KAAK,EAAE,EAAE;gBAC/D,KAAK,CAAC,cAAc,EAAE;gBAEtB,IAAI,KAAK,CAAC,aAAa,KAAK,IAAI,EAAE;oBACjC,OAAO,CAAC,mEAAmE;iBAC3E;gBAED,MAAM,QAAQ,GAAG,CAAC,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBAC1D,IAAI,QAAQ,EAAE;oBACb,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;iBACjC;YACF,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,OAAO,EAAE,6BAA6B,EAAE,CAAC,KAAK,EAAE,EAAE;gBACtE,KAAK,CAAC,cAAc,EAAE;gBACtB,IAAI,CAAC,YAAY,EAAE,CAAC;YACrB,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC,YAAY,CAAC,GAAG,EAAE;gBACvC,OAAO,IAAI,CAAC,eAAe,EAAE;qBAC3B,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,SAAS,CAAC,CAAC;qBACtC,MAAM,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;qBACzC,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE;oBACjB,OAAO;wBACN,KAAK,EAAE,QAAQ,CAAC,IAAI,CAAC,iDAAiD,CAAC;6BACrE,KAAK,EAAE,CAAC,IAAI,EAAE;qBAChB;gBACF,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACJ,CAAC;QAED,iBAAiB,CAAC,gBAAwB;YACzC,MAAM,QAAQ,GAAG,CAAC,CAAC,GAAG,GAAG,gBAAgB,CAAC,CAAC;YAC3C,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC1B,OAAO;aACP;YAED,IAAI,QAAQ,CAAC,QAAQ,CAAC,wBAAwB,CAAC,EAAE;gBAChD,OAAO,CAAC,0BAA0B;aAClC;YAED,oEAAoE;YACpE,sDAAsD;YACtD,MAAM,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;YAClE,IAAI,UAAU,KAAK,CAAC,CAAC,EAAE;gBACtB,OAAO,IAAI,CAAC,eAAe,EAAE,CAAC,MAAM,GAAG,UAAU,EAAE;oBAClD,IAAI,CAAC,YAAY,EAAE,CAAC;iBACpB;gBACD,OAAO;aACP;YAED,MAAM,gBAAgB,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;YAC3E,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE;gBAChC,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,iBAAiB,CAAC,CAAC;gBAC3D,gBAAgB;qBACd,WAAW,CAAC,wBAAwB,CAAC;qBACrC,QAAQ,CAAC,yBAAyB,CAAC,CAAC;gBACtC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;gBAEvD,gBAAgB,CAAC,OAAO,CAAC,8BAA8B,CAAC,CAAC;aACzD;YAED,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,iBAAiB,CAAC,CAAC;YACnD,QAAQ,CAAC,QAAQ,CAAC,wBAAwB,CAAC,CAAC;YAE5C,QAAQ,CAAC,OAAO,CAAC,8BAA8B,CAAC,CAAC;QAClD,CAAC;QAED,YAAY;YACX,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC,MAAM,GAAG,CAAC,EAAE;gBACtC,OAAO;aACP;YACD,MAAM,kBAAkB,GAAG,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,CAAC,CAAC;YAC/D,IAAI,kBAAkB,CAAC,MAAM,KAAK,CAAC,EAAE;gBACpC,OAAO;aACP;YAED,MAAM,kBAAkB,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;YAC7E,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,iBAAiB,CAAC,CAAC;YAC7D,kBAAkB,CAAC,WAAW,CAAC,gDAAgD,CAAC,CAAC;YACjF,kBAAkB,CAAC,OAAO,CAAC,8BAA8B,CAAC,CAAC;YAE3D,MAAM,mBAAmB,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;YAC/E,mBAAmB,CAAC,WAAW,CAAC,yBAAyB,CAAC,CAAC;YAE3D,+BAA+B;YAC/B,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,iBAAiB,CAAC,CAAC;YAC7D,kBAAkB,CAAC,QAAQ,CAAC,wBAAwB,CAAC,CAAC;YACtD,kBAAkB,CAAC,OAAO,CAAC,8BAA8B,CAAC,CAAC;YAE3D,6DAA6D;YAC7D,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC,MAAM,GAAG,CAAC,EAAE;gBACtC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;qBACrF,QAAQ,CAAC,yBAAyB,CAAC,CAAC;aACtC;QACF,CAAC;QAED,0EAA0E;QAC1E,uEAAuE;QACvE,4CAA4C;QAC5C,gBAAgB,CAAC,QAAgB,EAAE,gBAAwB;YAC1D,IAAI,oBAAoB,EAAE;gBACzB,OAAO;aACP;YAED,IAAI,QAAQ,CAAC,IAAI,CAAC,wBAAwB,CAAC,EAAE;gBAC5C,OAAO,CAAC,iCAAiC;aACzC;YAED,MAAM,gBAAgB,GAAG,gCAAgC,CAAC;YAE1D,QAAQ,CAAC,QAAQ,CAAC,sBAAsB,CAAC,CAAC;YAE1C,SAAS,qBAAqB,CAAC,KAAwB;gBACtD,gDAAgD;gBAChD,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,gBAAgB,CAAC,EAAE;oBAC1C,OAAO;iBACP;gBAED,QAAQ;qBACN,GAAG,CAAC,gBAAgB,EAAE,qBAAqB,CAAC;qBAC5C,IAAI,CAAC,wBAAwB,EAAE,IAAI,CAAC;qBACpC,WAAW,CAAC,sBAAsB,CAAC,CAAC;YACvC,CAAC;YAED,QAAQ,CAAC,IAAI,CAAC,wBAAwB,EAAE,IAAI,CAAC,CAAC;YAC9C,QAAQ,CAAC,EAAE,CAAC,gBAAgB,EAAE,qBAAqB,CAAC,CAAC;QACtD,CAAC;KACD;IAMD;;OAEG;IACH,IAAK,cAUJ;IAVD,WAAK,cAAc;QAClB;;WAEG;QACH,uEAAkB;QAElB;;WAEG;QACH,+EAAsB;IACvB,CAAC,EAVI,cAAc,KAAd,cAAc,QAUlB;IAED,MAAa,eAAgB,SAAQ,iGAA0C;QA+C9E,YAAY,UAAsB;YACjC,KAAK,CAAC,UAAU,CAAC,CAAC;YA/CF,sBAAiB,GAAG,mEAAmE,CAAC;YACzG,+DAA+D;YAC9C,sBAAiB,GAAG,GAAG,GAAG,IAAI,CAAC;YAQhD;;eAEG;YACK,sBAAiB,GAAkB,IAAI,CAAC;YAKxC,sBAAiB,GAA+D,IAAI,CAAC;YAMrF,qBAAgB,GAAkB,IAAI,CAAC;YACvC,uBAAkB,GAAkB,IAAI,CAAC;YAEzC,qBAAgB,GAAkB,IAAI,CAAC;YACvC,gBAAW,GAAgC,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;YAChE,qBAAgB,GAAsD,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YAC1F,0BAAqB,GAAgC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YAEzE,wBAAmB,GAAgC,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;YAmiBxE,oBAAe,GAAY,KAAK,CAAC;YACjC,0BAAqB,GAAkB,IAAI,CAAC;YAC5C,6BAAwB,GAAS,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;YAE7C,wBAAmB,GAAkB,IAAI,CAAC;YAC1C,4BAAuB,GAAY,KAAK,CAAC;YAvhBhD,IAAI,CAAC,QAAQ,GAAG,IAAI,4BAA4B,CAC/C,UAAU,CAAC,OAAO,EAClB,UAAU,CAAC,kBAAkB,EAC7B,UAAU,CAAC,mBAAmB,EAC9B,UAAU,CAAC,aAAa,EACxB,UAAU,CAAC,kBAAkB,EAC7B,UAAU,CAAC,eAAe,CAC1B,CAAC;YACF,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE;gBAC1C,IAAI,OAAO,EAAE,KAAK,QAAQ,EAAE;oBAC3B,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,kBAAkB,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC;iBAChD;YACF,CAAC,CAAC,CAAC;YACH,IAAI,UAAU,CAAC,sBAAsB,EAAE;gBACtC,IAAI,CAAC,QAAQ,CAAC,uBAAuB,CAAC,UAAU,CAAC,sBAAsB,CAAC,CAAC;aACzE;YAED,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC,cAAc,IAAI,IAAI,CAAC;YACxD,IAAI,CAAC,qBAAqB,GAAG,UAAU,CAAC,gBAAgB,IAAI,KAAK,CAAC;YAClE,IAAI,CAAC,iCAAiC,GAAG,UAAU,CAAC,iCAAiC,IAAI,KAAK,CAAC;YAE/F,IAAI,CAAC,OAAO,UAAU,CAAC,cAAc,KAAK,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,IAAI,cAAc,CAAC,EAAE;gBACrG,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC,cAAc,CAAC;aAChD;iBAAM;gBACN,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC,kBAAkB,CAAC;aACxD;YAED,IAAI,gBAAgB,GAAG,CAAC,CAAC;YAEzB,IAAI,CAAC,kBAAkB,GAAG,oBAAoB,CAC7C,UAAU,CAAC,kBAAkB,EAC7B,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,EACrC,CAAC,IAA4B,EAAE,EAAE;gBAChC,QAAQ,IAAI,CAAC,CAAC,EAAE;oBACf,KAAK,SAAS;wBACb,IAAI,CAAC,SAAS,GAAG,gBAAgB,CAAC;wBAClC,2DAA2D;wBAC3D,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE;4BACb,IAAI,CAAC,EAAE,GAAG,SAAS,GAAG,CAAC,EAAE,gBAAgB,CAAC,CAAC;yBAC3C;wBACD,MAAM;oBACP,KAAK,eAAe;wBACnB,IAAI,CAAC,SAAS,GAAG,sBAAsB,CAAC;wBACxC,MAAM;oBACP,KAAK,SAAS;wBACb,4DAA4D;wBAC5D,uCAAuC;wBACvC,IACC,CAAC,IAAI,CAAC,SAAS,KAAK,kBAAkB,CAAC;+BACpC,CAAC,IAAI,CAAC,SAAS,KAAK,oBAAoB,CAAC,EAC3C;4BACD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC;4BAChC,IAAI,CAAC,MAAM,CAAC,iBAAiB,GAAG,yBAAyB,CAAC;yBAC1D;wBACD,mDAAmD;wBACnD,IAAI,IAAI,CAAC,SAAS,KAAK,0BAA0B,EAAE;4BAClD,IAAI,CAAC,SAAS,GAAG,kBAAkB,CAAC;yBACpC;iBACF;YACF,CAAC,CACD,CAAC;YAEF,oEAAoE;YACpE,8CAA8C;YAC9C,MAAM,UAAU,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YACjD,IAAI,UAAU,CAAC,YAAY,CAAC,GAAG,CAAC,gBAAgB,CAAC,KAAK,IAAI,EAAE;gBAC3D,UAAU,CAAC,YAAY,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;gBACjD,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC;aACrD;YAED,oDAAoD;YACpD,IAAI,UAAU,CAAC,YAAY,CAAC,GAAG,CAAC,uBAAuB,CAAC,KAAK,IAAI,EAAE;gBAClE,UAAU,CAAC,YAAY,CAAC,MAAM,CAAC,uBAAuB,CAAC,CAAC;gBACxD,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC;aACrD;YAED,MAAM,qBAAqB,GAAkB,UAAU,CAAC,qBAAqB,CAAC;YAC9E,MAAM,oBAAoB,GAAG,aAAa,CAAC;YAE3C,SAAS,iBAAiB,CAAC,UAAkB,EAAE,aAAqB;gBACnE,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,UAAU,CAAC,CAAC;gBAChC,IAAI,qBAAqB,EAAE;oBAC1B,GAAG,CAAC,QAAQ,GAAG,qBAAqB,CAAC,OAAO,CAAC,oBAAoB,EAAE,aAAa,CAAC,CAAC;oBAClF,gEAAgE;oBAChE,qDAAqD;oBACrD,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;oBAChC,uEAAuE;oBACvE,wEAAwE;oBACxE,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC;iBAC5C;qBAAM;oBACN,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,kBAAkB,EAAE,aAAa,CAAC,CAAC;iBACxD;gBACD,OAAO,GAAG,CAAC;YACZ,CAAC;YAED,SAAS,mBAAmB,CAAC,GAAW;;gBACvC,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;gBAC/B,IAAI,qBAAqB,EAAE;oBAC1B,SAAS,YAAY,CAAC,KAAa;wBAClC,OAAO,KAAK,CAAC,OAAO,CAAC,qBAAqB,EAAE,MAAM,CAAC,CAAC;oBACrD,CAAC;oBAED,MAAM,gBAAgB,GAAG,qBAAqB,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC;oBAC7E,MAAM,cAAc,GAAG,gBAAgB,GAAG,oBAAoB,CAAC,MAAM,CAAC;oBAEtE,MAAM,0BAA0B,GAAG,IAAI,MAAM,CAC5C,GAAG,GAAG,YAAY,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC;0BAClE,iBAAiB;0BACjB,YAAY,CAAC,qBAAqB,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,CAC3D,CAAC;oBAEF,MAAM,KAAK,GAAG,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAC;oBACnE,OAAO,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;iBAC7B;qBAAM;oBACN,OAAO,eAAS,CAAC,YAAY,CAAC,GAAG,CAAC,kBAAkB,CAAC,mCAAI,EAAE,CAAC;iBAC5D;YACF,CAAC;YAED,6DAA6D;YAC7D,IAAI,mBAAmB,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE,EAAE;gBAChF,MAAM,MAAM,GAAG,iBAAiB,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE,CAAC,CAAC;gBACtF,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC;aACjD;YAED,uDAAuD;YACvD,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,aAAa,EAAE,EAAE;gBACvD,MAAM,GAAG,GAAG,iBAAiB,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;gBACnE,IAAI,UAAU,CAAC,yBAAyB,EAAE;oBACzC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;iBAC3C;qBAAM;oBACN,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;iBAC9C;YACF,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,uBAAuB,CAAC,CAAC;YAE9C,yCAAyC;YACzC,yDAAyD;YACzD,4BAA4B;YAC5B,0EAA0E;YAC1E,gFAAgF;YAChF,MAAM,mBAAmB,GAAG,EAAE,CAAC,YAAY,CAAC,GAAG,EAAE;gBAChD,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,mBAAmB,EAAE,CAAC;gBACtD,OAAO,CACN,SAAS,CAAC,UAAU,EAAE;uBACnB,SAAS,CAAC,aAAa,EAAE;uBACzB,CAAC,IAAI,CAAC,QAAQ,CAAC,8BAA8B,EAAE;uBAC/C,CAAC,IAAI,CAAC,QAAQ,CAAC,mBAAmB,EAAE,CACvC,CAAC;YACH,CAAC,CAAC,CAAC;YACH,gDAAgD;YAChD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,mBAAmB,EAAE,CAAC,CAAC;YAC1D,sBAAsB;YACtB,mBAAmB,CAAC,SAAS,CAAC,CAAC,SAAS,EAAE,EAAE;;gBAC3C,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,SAAS,CAAC,CAAC;gBAC9C,iEAAiE;gBACjE,IAAI,SAAS,EAAE;oBACd,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,UAAI,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,mCAAI,cAAc,CAAC,CAAC;iBAC9E;YACF,CAAC,CAAC,CAAC;YAEH,mCAAmC;YACnC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;gBACjC,mBAAmB;gBACnB,MAAM,QAAQ,GAAG,CAAC,CAAC,kCAAkC,CAAC,CAAC;gBACvD,QAAQ,CAAC,GAAG,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC,IAAI,EAAE,CAAC;gBAE7C,MAAM,mBAAmB,GAAG,8BAA8B,CAAC;gBAC3D,iDAAiD;gBACjD,CAAC,CAAC,GAAG,GAAG,mBAAmB,CAAC,CAAC,MAAM,EAAE,CAAC;gBAEtC,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,gBAAgB,EAAE,CAAC;gBAEjD,OAAO,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;oBACtB,wCAAwC;oBACxC,IAAI,OAAO,GAAG,oDAAoD,CAAC;oBACnE,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;wBAC9B,OAAO,GAAG,KAAK,CAAC;qBAChB;yBAAM,IAAI,KAAK,YAAY,KAAK,EAAE;wBAClC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;qBACxB;yBAAM,IAAI,OAAO,KAAK,CAAC,YAAY,KAAK,QAAQ,EAAE;wBAClD,OAAO,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC,MAAM,EAAE,SAAS,CAAC,EAAE,OAAO,CAAC,CAAC;qBAClE;oBAED,MAAM,OAAO,GAAG,CAAC,CAAC,OAAO,CAAC;yBACxB,IAAI,CAAC,IAAI,EAAE,mBAAmB,CAAC;yBAC/B,QAAQ,CAAC,oCAAoC,CAAC;yBAC9C,IAAI,CAAC,OAAO,CAAC,CAAC;oBAEhB,uEAAuE;oBACvE,kDAAkD;oBAClD,OAAO,CAAC,MAAM,CACb,CAAC,CAAC,wDAAwD,CAAC;yBACzD,MAAM,CAAC,6DAA6D,CAAC;yBACrE,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;wBACtB,KAAK,CAAC,cAAc,EAAE,CAAC;wBACvB,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,6BAA6B;oBAChD,CAAC,CAAC,CACH,CAAC;oBAEF,MAAM,UAAU,GAAG,CAAC,CAAC,kCAAkC,CAAC,CAAC;oBACzD,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;gBAC5B,CAAC,CAAC;gBAEF,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE;;oBACjB,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,UAAI,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,mCAAI,OAAO,CAAC,CAAC;oBAEzE,kEAAkE;oBAClE,oEAAoE;oBACpE,mEAAmE;oBACnE,8DAA8D;oBAC9D,IAAI,CAAC,uBAAuB,EAAE,CAAC;gBAChC,CAAC,CAAC,CAAC;gBAEH,OAAO,CAAC,MAAM,CAAC,GAAG,EAAE;oBACnB,QAAQ,CAAC,GAAG,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;gBACtC,CAAC,CAAC,CAAC;YACJ,CAAC,CAAC,CAAC;YAEH,wFAAwF;YACxF,IAAI,CAAC,QAAQ,CAAC,8BAA8B,CAAC,SAAS,CAAC,CAAC,YAAY,EAAE,EAAE;gBACvE,CAAC,CAAC,iCAAiC,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;YAC3D,CAAC,CAAC,CAAC;YAEH,8EAA8E;YAC9E,IAAI,CAAC,uBAAuB,GAAG,EAAE,CAAC,YAAY,CAAC,GAAG,EAAE;gBACnD,OAAO,IAAI,CAAC,WAAW,EAAE,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACzD,CAAC,CAAC,CAAC;YAEH,+CAA+C;YAC/C,IAAI,CAAC,mBAAmB,GAAG,IAAI,mBAAmB,CACjD,GAAG,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,mBAAmB,EAAE,CAAC,IAAI,EAAE,EAChD,GAAG,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,mBAAmB,EAAE,EACzC,IAAI,CAAC,QAAQ,CAAC,kBAAkB,EAChC,UAAU,CACV,CAAC;YAEF,2DAA2D;YAC3D,IAAI,CAAC,mBAAmB,GAAG,EAAE,CAAC,YAAY,CAAC,GAAG,EAAE;gBAC/C,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,mBAAmB,EAAE,CAAC;gBACtD,OAAO,SAAS,IAAI,SAAS,CAAC,aAAa,EAAE;uBACzC,CAAC,IAAI,CAAC,QAAQ,CAAC,8BAA8B,EAAE,CAAC;YACrD,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC,SAAS,EAAE,EAAE;gBAChD,IAAI,IAAI,CAAC,gBAAgB,EAAE;oBAC1B,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,6BAA6B,CAAC;yBACvD,WAAW,CAAC,mBAAmB,EAAE,CAAC,SAAS,CAAC,CAAC;iBAC/C;YACF,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,2BAA2B,GAAG,EAAE,CAAC,YAAY,CAAC,GAAG,EAAE;gBACvD,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,mBAAmB,EAAE,CAAC;gBACtD,OAAO,SAAS,IAAI,SAAS,CAAC,UAAU,EAAE,IAAI,SAAS,CAAC,aAAa,EAAE;uBACnE,CAAC,IAAI,CAAC,QAAQ,CAAC,8BAA8B,EAAE;YACpD,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,2BAA2B,CAAC,SAAS,CAAC,CAAC,SAAS,EAAE,EAAE;gBACxD,IAAI,IAAI,CAAC,gBAAgB,EAAE;oBAC1B,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,gCAAgC,CAAC;yBAC1D,WAAW,CAAC,mBAAmB,EAAE,CAAC,SAAS,CAAC,CAAC;iBAC/C;YACF,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,0BAA0B,GAAG,EAAE,CAAC,YAAY,CAAC,GAAG,EAAE;gBACtD,OAAO,CACN,CAAC,IAAI,CAAC,QAAQ,CAAC,8BAA8B,EAAE;uBAC5C,CAAC,IAAI,CAAC,mBAAmB,CAAC,qBAAqB,EAAE;oBACpD,+DAA+D;oBAC/D,oCAAoC;uBACjC,CAAC,IAAI,CAAC,QAAQ,CAAC,mBAAmB,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC;uBACnD;oBACF,qEAAqE;oBACrE,CAAC,IAAI,CAAC,iCAAiC;2BACpC,IAAI,CAAC,QAAQ,CAAC,mBAAmB,EAAE,CAAC,UAAU,EAAE,CACnD,CACD,CAAC;YACH,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,0BAA0B,CAAC,SAAS,CAAC,CAAC,SAAS,EAAE,EAAE;gBACvD,IAAI,IAAI,CAAC,gBAAgB,EAAE;oBAC1B,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,+BAA+B,CAAC;yBACzD,WAAW,CAAC,mBAAmB,EAAE,CAAC,SAAS,CAAC,CAAC;iBAC/C;YACF,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,iBAAiB,GAAG,IAAI,iBAAiB,EAAE,CAAC;YAEjD,2BAA2B;YAC3B,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC,uBAAuB,CAAC,CAAC;YAEhD,IAAI,CAAC,iBAAiB,GAAG,UAAU,CAAC,iBAAiB,CAAC;YACtD,IAAI,CAAC,mBAAmB,GAAG,UAAU,CAAC,mBAAmB,CAAC;YAE1D,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,MAAM,EAAE,GAAG,EAAE;;gBAClC,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;gBAE5B,+DAA+D;gBAC/D,oDAAoD;gBACpD,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;gBAE9B,4CAA4C;gBAC5C,IAAI,IAAI,CAAC,iBAAiB,EAAE;oBAC3B,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,CAAC;oBACpC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;iBAC9B;gBAED,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAsB,CAAC;gBAC7D,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC,KAAK,YAAY,iBAAiB,CAAC,EAAE;oBACpD,OAAO;iBACP;gBAED,6CAA6C;gBAC7C,IAAI;oBACH,MAAM,GAAG,GAAG,WAAK,CAAC,aAAa,0CAAE,QAAQ,CAAC,IAAI,CAAC;oBAC/C,IAAI,GAAG,EAAE;wBACR,IAAI,CAAC,iBAAiB,GAAG,GAAG,CAAC;qBAC7B;iBACD;gBAAC,OAAO,CAAC,EAAE;oBACX,6EAA6E;iBAC7E;gBAED,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC,cAAc,CACxD,KAAK,EACL;oBACC,eAAe,EAAE,CAAC,GAAW,EAAE,EAAE;wBAChC,IAAI,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,EAAE;4BAC/B,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC;yBACtB;oBACF,CAAC;oBACD,yBAAyB,EAAE,CAAC,GAAW,EAAE,EAAE;wBAC1C,IAAI,CAAC,iBAAiB,GAAG,GAAG,CAAC;oBAC9B,CAAC;iBACD,EACD,IAAI,CAAC,kBAAkB,EACvB,UAAU,CAAC,gBAAgB,CAC3B,CAAC;gBAEF,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,EAAE;oBAClD,IAAI,OAAO,UAAU,KAAK,WAAW,EAAE;wBACtC,mEAAmE;wBACnE,MAAM,IAAI,KAAK,CAAC,2FAA2F,CAAC,CAAC;qBAC7G;oBAED,UAAU,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE;wBAChD,IAAI,GAAG,IAAI,CAAC,OAAO,GAAG,KAAK,QAAQ,CAAC,EAAE;4BACrC,IAAI,CAAC,iBAAiB,GAAG,GAAG,CAAC;yBAC7B;oBACF,CAAC,CAAC,CAAC;oBAEH,2DAA2D;oBAC3D,8CAA8C;oBAC9C,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,0CAA0C,CAAC,CAAC;gBAC/D,CAAC,CAAC,CAAC;YACJ,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC;YAEzC,0EAA0E;YAC1E,CAAC,CAAC,0BAA0B,CAAC,CAAC,OAAO,CAAC,4BAA4B,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;YAE5E,MAAM,sBAAsB,GAAG,CAAC,CAAC,QAAQ,CACxC,GAAG,EAAE;gBACJ,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAChC,CAAC,EACD,IAAI,EAAE,4EAA4E;YAClF,EAAC,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAC,CAC/B,CAAC;YAEF,+CAA+C;YAC/C,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAE;gBACrD,IACC,OAAO,CAAC,mBAAmB;uBACxB,IAAI,CAAC,iBAAiB;uBACtB,IAAI,CAAC,iBAAiB,CAAC,WAAW,EACpC;oBACD,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,gBAAgB,EAAE,OAAO,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;iBACvE;qBAAM;oBACN,IAAI,MAAM,GAAW,SAAS,CAAC;oBAC/B,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE;wBACjC,MAAM,GAAG,WAAW,GAAG,OAAO,CAAC,EAAE,GAAG,gCAAgC,CAAC;qBACrE;yBAAM,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;wBACnC,MAAM,GAAG,uBAAuB,CAAC;qBACjC;yBAAM,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE;wBAC/C,MAAM,GAAG,qCAAqC,CAAC;qBAC/C;oBACD,IAAI,CAAC,GAAG,CAAC,uCAAuC,GAAG,MAAM,CAAC,CAAC;oBAE3D,sBAAsB,EAAE,CAAC;iBACzB;YACF,CAAC,CAAC,CAAC;YAEH,MAAM,oBAAoB,GAAG,GAAG,EAAE;gBACjC,8EAA8E;gBAC9E,kCAAkC;gBAClC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,kCAAkC,EAAE,CAAC,KAAK,EAAE,EAAE;oBAC1D,IAAI,IAAI,CAAC,kBAAkB,EAAE,EAAE;wBAC9B,KAAK,CAAC,cAAc,EAAE,CAAC;wBACvB,kEAAkE;wBAClE,wDAAwD;wBAExD,gFAAgF;wBAChF,0EAA0E;wBAC1E,0EAA0E;wBAC1E,gFAAgF;wBAChF,OAAO,IAAI,CAAC,iBAAiB,CAAC;qBAC9B;gBACF,CAAC,CAAC,CAAC;YACJ,CAAC;YAED;;;;eAIG;YACH,IAAI,IAAI,CAAC,QAAQ,CAAC,mBAAmB,EAAE,CAAC,UAAU,EAAE,EAAE;gBACrD,oBAAoB,EAAE,CAAC;aACvB;iBAAM;gBACN,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,GAAG,EAAE;oBACvD,uDAAuD;oBACvD,IAAI,CAAC,QAAQ,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;oBAC/C,oBAAoB,EAAE,CAAC;gBACxB,CAAC,CAAC,CAAC;aACH;QACF,CAAC;QAED,oBAAoB,CAAC,SAAiB,EAAE,YAAiB;YACxD,yEAAyE;YACzE,6BAA6B;YAC7B,OAAO,IAAI,CAAC,QAAQ;iBAClB,GAAG,CAAC,SAAS,CAAC;iBACd,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;iBAC7B,SAAS,CAAC,EAAE,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC;QAC1C,CAAC;QAED,mBAAmB;YAClB,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;QAC5C,CAAC;QAED,IAAI,UAAU;YACb,OAAO,IAAI,CAAC,iBAAiB,CAAC;QAC/B,CAAC;QAED,IAAI,UAAU,CAAC,GAAkB;YAChC,IAAI,GAAG,KAAK,IAAI,CAAC,iBAAiB,EAAE;gBACnC,OAAO;aACP;YACD,sEAAsE;YACtE,oCAAoC;YACpC,IAAI,GAAG,KAAK,IAAI,EAAE;gBACjB,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;aAC3D;YAED,IAAI,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,EAAE;gBAC/B,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;aAC/B;QACF,CAAC;QAEO,oBAAoB,CAAC,MAAqB,IAAI,EAAE,cAAuB,KAAK;YACnF,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC;YAC/B,IAAI,GAAG,KAAK,IAAI,EAAE;gBACjB,GAAG,GAAG,MAAM,aAAN,MAAM,cAAN,MAAM,GAAI,IAAI,CAAC,iBAAiB,CAAC;aACvC;YAED,MAAM,SAAS,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,CAAC;YACnC,IAAI,SAAS,IAAI,CAAC,WAAW,EAAE;gBAC9B,OAAO;aACP;YAED,mFAAmF;YACnF,mFAAmF;YACnF,wDAAwD;YACxD,MAAM,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC;YACpD,MAAM,sBAAsB,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;YAE1D,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;YAE/B,6DAA6D;YAC7D,gCAAgC;YAChC,MAAM,YAAY,GAAG,yBAAyB,CAAC;YAC/C,IAAI,SAAS,IAAI,CAAC,sBAAsB,EAAE;gBACzC,SAAS,CAAC,YAAY,CAAC,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;aAC3E;iBAAM;gBACN,kDAAkD;gBAClD,SAAS,CAAC,YAAY,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;aAC5C;YAED,mEAAmE;YACnE,wEAAwE;YACxE,qDAAqD;YACrD,IAAI,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE,CAAC;YAClD,IAAI,aAAa,KAAK,EAAE,EAAE;gBACzB,6DAA6D;gBAC7D,aAAa,GAAG,sBAAsB,CAAC;aACvC;YACD,SAAS,CAAC,YAAY,CAAC,GAAG,CAAC,kBAAkB,EAAE,aAAa,CAAC,CAAC;YAC9D,SAAS,CAAC,YAAY,CAAC,GAAG,CAAC,gBAAgB,EAAE,GAAG,CAAC,CAAC;YAElD,IAAI,CAAC,uBAAuB,GAAG,KAAK,CAAC,CAAC,sCAAsC;YAC5E,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;YAE3B,wEAAwE;YACxE,IAAI,sBAAsB,EAAE;gBAC3B,MAAM,QAAQ,GAAG;oBAChB,MAAM,EAAE,iCAAiC;oBACzC,kBAAkB,EAAE,aAAa;oBACjC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC;oBACxC,KAAK,EAAE,IAAI,CAAC,mBAAmB;iBAC/B;gBAED,MAAM,KAAK,GAAG,CAAC,CAAC,QAAQ,CAAC;qBACvB,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC;qBACtB,IAAI,CAAC,QAAQ,EAAE,SAAS,CAAC,IAAI,CAAC;qBAC9B,IAAI,CAAC,QAAQ,EAAE,sBAAsB,CAAC;qBACtC,QAAQ,CAAC,MAAM,CAAC,CAAC;gBAEnB,IAAI,GAA0B,CAAC;gBAC/B,KAAK,GAAG,IAAI,QAAQ,EAAE;oBACrB,MAAM,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC;oBAC5B,CAAC,CAAC,SAAS,CAAC;yBACV,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC;yBACtB,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC;yBACjB,GAAG,CAAC,KAAK,CAAC;yBACV,QAAQ,CAAC,KAAK,CAAC,CAAC;iBAClB;gBAED,IAAI,CAAC,iBAAiB,GAAG,SAAS,CAAC,IAAI,CAAC;gBACxC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;gBACxB,KAAK,CAAC,MAAM,EAAE,CAAC;aACf;iBAAM;gBACN,IAAI,CAAC,iBAAiB,GAAG,SAAS,CAAC,IAAI,CAAC;gBACxC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;aACvD;QACF,CAAC;QASD,IAAY,cAAc,CAAC,SAAkB;YAC5C,MAAM,gBAAgB,GAAG,IAAI,CAAC,eAAe,CAAC;YAC9C,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,KAAK,gBAAgB,CAAC,EAAE;gBACnD,OAAO;aACP;YACD,8DAA8D;YAC9D,iEAAiE;YACjE,yBAAyB;YAEzB,CAAC,CAAC,mCAAmC,CAAC,CAAC,WAAW,CAAC,uBAAuB,EAAE,SAAS,CAAC,CAAC;YACvF,IAAI,IAAI,CAAC,qBAAqB,EAAE;gBAC/B,YAAY,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;gBACzC,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;aAClC;YAED,IAAI,SAAS,EAAE;gBACd,8EAA8E;gBAC9E,qCAAqC;gBACrC,IAAI,CAAC,qBAAqB,GAAG,MAAM,CAAC,UAAU,CAAC,GAAG,EAAE;oBACnD,IAAI,IAAI,CAAC,cAAc,EAAE;wBACxB,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;qBAC5B;gBACF,CAAC,EAAE,KAAK,CAAC,CAAC;aACV;YACD,IAAI,CAAC,eAAe,GAAG,SAAS,CAAC;YAEjC,IAAI,gBAAgB,IAAI,CAAC,SAAS,EAAE;gBACnC,IAAI,CAAC,wBAAwB,GAAG,IAAI,IAAI,EAAE,CAAC;aAC3C;YAED,uDAAuD;YACvD,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,uBAAuB,EAAE;gBAC/C,IAAI,CAAC,uBAAuB,GAAG,KAAK,CAAC;gBACrC,IAAI,CAAC,uBAAuB,EAAE,CAAC;aAC/B;QACF,CAAC;QAED,IAAW,cAAc;YACxB,OAAO,IAAI,CAAC,eAAe,CAAC;QAC7B,CAAC;QAEO,uBAAuB;YAC9B,IAAI,IAAI,CAAC,mBAAmB,EAAE;gBAC7B,OAAO,CAAC,6BAA6B;aACrC;YAED,IAAI,IAAI,CAAC,cAAc,EAAE;gBACxB,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;gBACpC,OAAO;aACP;YAED,kEAAkE;YAClE,4CAA4C;YAC5C,MAAM,cAAc,GAAG,IAAI,CAAC;YAC5B,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,MAAM,iBAAiB,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,wBAAwB,CAAC,OAAO,EAAE,CAAC;YAClF,IAAI,iBAAiB,GAAG,cAAc,EAAE;gBACvC,IAAI,CAAC,mBAAmB,GAAG,MAAM,CAAC,UAAU,CAAC,GAAG,EAAE;oBACjD,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;oBAChC,IAAI,CAAC,uBAAuB,EAAE,CAAC;gBAChC,CAAC,EAAE,cAAc,GAAG,iBAAiB,CAAC,CAAC;gBACvC,OAAO;aACP;YAED,4BAA4B;YAC5B,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACvC,CAAC;QAED,iBAAiB,CAAC,WAAwB;YACzC,+EAA+E;YAC/E,wEAAwE;YACxE,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAE7B,6BAA6B;YAC7B,IAAI,CAAC,kBAAkB,GAAG,MAAM,CAAC,+BAA+B,EAAE,WAAW,CAAC,CAAC;YAC/E,IAAI,CAAC,gBAAgB,GAAG,MAAM,CAAC,4BAA4B,EAAE,WAAW,CAAC,CAAC,IAAI,EAAE,CAAC;YAEjF,qBAAqB;YACrB,IAAI,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC,CAAC;YACvE,IAAI,CAAC,2BAA2B,CAAC,iBAAiB,CAAC,IAAI,CAAC,2BAA2B,EAAE,CAAC,CAAC;YACvF,IAAI,CAAC,0BAA0B,CAAC,iBAAiB,CAAC,IAAI,CAAC,0BAA0B,EAAE,CAAC,CAAC;YAErF,sBAAsB;YACtB,IAAI,CAAC,gBAAgB,GAAG,MAAM,CAAC,iCAAiC,EAAE,WAAW,CAAC,CAAC;QAChF,CAAC;QAED,qBAAqB;YACpB,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,+BAA+B,CAAC,CAAC;QAC3E,CAAC;QAED,qEAAqE;QACrE;;;;;WAKG;QACH,gBAAgB,CAAC,UAAkB,EAAE,GAAG,IAAS;YAChD,IAAI,CAAC,IAAI,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE;gBACnE,OAAO,CAAC,CAAC,QAAQ,EAAE,CAAC,MAAM,CAAC,qCAAqC,CAAC,CAAC,OAAO,EAAE,CAAC;aAC5E;YACD,OAAO,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,UAAU,EAAE,GAAG,IAAI,CAAC,CAAC;QAC5D,CAAC;QAED,WAAW;YACV,IAAI,IAAI,CAAC,kBAAkB,EAAE,EAAE;gBAC9B,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,EAAE;oBAC3C,oEAAoE;oBACpE,oCAAoC;oBACpC,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;oBAClD,OAAO,IAAI,CAAC;iBACZ;gBACD,OAAO,KAAK,CAAC;aACb;YACD,OAAO,IAAI,CAAC;QACb,CAAC;QAEO,kBAAkB;YACzB,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,mBAAmB,EAAE,CAAC;YAEtD,qDAAqD;YACrD,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,EAAE;gBAC5B,OAAO,KAAK,CAAC;aACb;YAED,QAAQ,IAAI,CAAC,cAAc,EAAE;gBAC5B,KAAK,cAAc,CAAC,kBAAkB;oBACrC,OAAO,CACN,CAAC,SAAS,CAAC,YAAY,EAAE;2BACtB,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,OAAO,CAAC,CAAC,oCAAoC;qBACxE,CAAC;gBACH,KAAK,cAAc,CAAC,cAAc;oBACjC,MAAM,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC;oBACpD,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;gBACnC;oBACC,OAAO,KAAK,CAAC;aACd;QAEF,CAAC;QAED,uEAAuE;QACvE,qBAAqB;YACpB,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;gBAC3B,OAAO;aACP;YACD,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC;YAE/B,IAAI,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,UAAU,CAAC,EAAE;gBACzC,qCAAqC;gBACrC,MAAM,OAAO,GAAG,CAAC,CAAC,+BAA+B,CAAC,CAAC;gBACnD,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC;oBAC9B,EAAE,EAAE,WAAW;oBACf,EAAE,EAAE,cAAc;oBAClB,EAAE,EAAE,OAAO;oBACX,SAAS,EAAE,SAAS;iBACpB,CAAC,CAAC;gBAEH,oEAAoE;gBACpE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,8BAA8B,EAAE,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;aAC7F;iBAAM;gBACN,iDAAiD;gBACjD,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;aAChD;QACF,CAAC;QAED,4BAA4B,CAAC,KAAwB;YACpD,IACC,CAAC,IAAI,CAAC,gBAAgB;mBACnB,CAAC,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,UAAU,CAAC;mBACrC,CAAC,IAAI,CAAC,kBAAkB,EAC1B;gBACD,qEAAqE;gBACrE,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;gBAChD,OAAO;aACP;YAED,MAAM,WAAW,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YACjD,MAAM,aAAa,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YACrD,MAAM,kBAAkB,GAAG,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YAC/D,MAAM,oBAAoB,GAAG,CAAC,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YAEnE,IAAI,kBAAkB,IAAI,oBAAoB,EAAE;gBAC/C,IAAI,CAAC,mBAAmB,EAAE,CAAC;aAC3B;QACF,CAAC;QAEO,mBAAmB;YAC1B,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;gBAC3B,OAAO;aACP;YAED,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,CAAC;YAC7B,6CAA6C;YAC7C,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;QACjD,CAAC;QAEO,kBAAkB,CAAC,IAAwB;YAClD,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpC,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YACtC,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;YAClC,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC5B,CAAC;QAED,wBAAwB;YACvB,IAAI,CAAC,IAAI,CAAC,0BAA0B,EAAE,EAAE;gBACvC,KAAK,CAAC,8DAA8D,CAAC,CAAC;gBACtE,OAAO;aACP;YACD,IAAI,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;QACtD,CAAC;QAED,gEAAgE;QAChE,yBAAyB;YACxB,IAAI,CAAC,IAAI,CAAC,0BAA0B,EAAE,EAAE;gBACvC,OAAO;aACP;YAED,uEAAuE;YACvE,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,kBAAkB,EAAE,CAAC;YACpD,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,gBAAgB,EAAE;gBACtD,IAAI,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;aACrD;iBAAM;gBACN,IAAI,CAAC,mBAAmB,CAAC,iCAAiC,CAAC,QAAQ,CAAC,CAAC;aACrE;QACF,CAAC;QAED,gEAAgE;QAChE,wBAAwB;YACvB,IAAI,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;QAClD,CAAC;QAED,sBAAsB;YACrB,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,EAAE;gBAChC,yEAAyE;gBACzE,wEAAwE;gBACxE,OAAO,KAAK,CAAC;aACb;YACD,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAE3B,uEAAuE;YACvE,OAAO,IAAI,CAAC;QACb,CAAC;QAED,oBAAoB;YACnB,IAAI,CAAC,IAAI,CAAC,2BAA2B,EAAE,EAAE;gBACxC,OAAO;aACP;YACD,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAE3B,IAAI,IAAI,CAAC,QAAQ,CAAC,8BAA8B,EAAE,EAAE;gBACnD,KAAK,CAAC,6FAA6F,CAAC,CAAC;gBACrG,OAAO;aACP;YAED,IAAI,CAAC,OAAO,CAAC,wDAAwD,CAAC,EAAE;gBACvE,OAAO;aACP;YAED,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;YAClC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;YAE/B,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE;iBAC5B,IAAI,CAAC,GAAG,EAAE;gBACV,6CAA6C;gBAC7C,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;gBAC1C,IAAI,IAAI,CAAC,cAAc,EAAE;oBACxB,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC;oBACnC,GAAG,CAAC,MAAM,GAAG,EAAE,CAAC;iBAChB;qBAAM;oBACN,uDAAuD;oBACvD,gDAAgD;oBAChD,MAAM,IAAI,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;oBAC1C,GAAG,CAAC,MAAM,GAAG,EAAE,CAAC;oBAChB,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,IAAI,sBAAsB,CAAC,CAAC;iBAC7D;gBACD,gEAAgE;gBAChE,4BAA4B;gBAC5B,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,uBAAuB,EAAE,GAAG,CAAC,CAAC;gBAEnD,sBAAsB;gBACtB,GAAG,CAAC,IAAI,GAAG,EAAE,CAAC;gBAEd,2CAA2C;gBAC3C,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;gBAEhF,0BAA0B;gBAC1B,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,GAAG,CAAC,QAAQ,EAAE,CAAC;gBAEtC,2DAA2D;gBAC3D,mEAAmE;YACpE,CAAC,CAAC;iBACD,IAAI,CAAC,CAAC,aAAa,EAAE,EAAE;gBACvB,IAAI,OAAO,GAAW,aAAa,CAAC,UAAU,IAAI,gBAAgB,CAAC;gBAEnE,IAAI,OAAO,aAAa,CAAC,YAAY,KAAK,QAAQ,EAAE;oBACnD,MAAM,aAAa,GAAG,CAAC,CAAC,GAAG,CAAC,aAAa,CAAC,YAAY,EAAE,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC,CAAC;oBAC7E,IAAI,OAAO,aAAa,KAAK,QAAQ,EAAE;wBACtC,OAAO,GAAG,aAAa,CAAC;qBACxB;iBACD;gBAED,KAAK,CAAC,SAAS,GAAG,OAAO,CAAC,CAAC;gBAC3B,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;YACjC,CAAC,CAAC,CAAC;QACL,CAAC;QAED,yBAAyB;YACxB,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;gBAC3B,OAAO;aACP;YACD,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAqB,CAAC;YACnE,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE;gBACnE,OAAO;aACP;YAED,yDAAyD;YACzD,MAAM,YAAY,GAAG,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC7C,IAAI,CAAC,YAAY,EAAE;gBAClB,OAAO;aACP;YAED,sBAAsB;YACtB,IAAI,YAAY,CAAC,IAAI,GAAG,IAAI,CAAC,iBAAiB,EAAE;gBAC/C,KAAK,CACJ,kEAAkE;sBAChE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,GAAG,MAAM,CACpD,CAAC;gBACF,uBAAuB;gBACvB,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;gBAC9B,OAAO;aACP;YAED,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YACvB,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;YAE5B,KAAK,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,IAAI,CACjC,CAAC,GAAG,EAAE,EAAE;gBACP,MAAM,iBAAiB,GAAG,6CAA6C,CAAC;gBACxE,MAAM,kBAAkB,GAAG,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;gBACvD,IAAI,CAAC,kBAAkB,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE;oBAC3D,MAAM,IAAI,KAAK,CAAC,iEAAiE,CAAC,CAAC;iBACnF;gBACD,MAAM,YAAY,GAAG,kBAAkB,CAAC,CAAC,CAAC,CAAC;gBAE3C,mEAAmE;gBACnE,gEAAgE;gBAChE,MAAM,OAAO,GAAG,iBAAiB,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;gBAC1D,IAAI,eAAuB,CAAC;gBAC5B,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE;oBACrC,MAAM,IAAI,KAAK,CAAC,6DAA6D,CAAC,CAAC;iBAC/E;qBAAM;oBACN,eAAe,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;iBAC7B;gBAED,MAAM,YAAY,GAAG,GAAG,CAAC,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC,CAAC;gBACjE,IAAI,CAAC,YAAY,EAAE;oBAClB,MAAM,IAAI,KAAK,CAAC,wDAAwD,CAAC,CAAC;iBAC1E;gBAED,kBAAkB;gBAClB,OAAO,OAAO,CAAC,GAAG,CAAC;oBAClB,YAAY,CAAC,KAAK,CAAC,QAAQ,CAAC;oBAC5B,YAAY,CAAC,KAAK,CAAC,QAAQ,CAAC;iBAC5B,CAAC,CAAC;YACJ,CAAC,EACD,CAAC,KAAK,EAAE,EAAE;gBACT,MAAM,YAAY,GAAG,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC;gBAC5C,MAAM,IAAI,KAAK,CAAC,iBAAiB,GAAG,YAAY,CAAC,IAAI,GAAG,KAAK,GAAG,YAAY,CAAC,CAAC;YAC/E,CAAC,CACD,CAAC,IAAI,CAAC,CAAC,YAAY,EAAE,EAAE;gBACvB,IAAI,CAAC,YAAY,EAAE;oBAClB,MAAM,IAAI,KAAK,CAAC,yDAAyD,CAAC,CAAC;iBAC3E;gBAED,MAAM,QAAQ,GAAG,IAAI,CAAC,2BAA2B,CAChD,YAAY,CAAC,CAAC,CAAC,EACf,eAAe,EACf,kBAAkB,CAClB,CAAC;gBACF,MAAM,QAAQ,GAAG,IAAI,CAAC,2BAA2B,CAChD,YAAY,CAAC,CAAC,CAAC,EACf,eAAe,EACf,kBAAkB,CAClB,CAAC;gBACF,MAAM,MAAM,GAAG,IAAI,sBAAsB,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;gBAEvE,kBAAkB;gBAClB,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,uBAAuB,CAAC,QAAQ,CAAC,CAAC,CAAC;gBAErE,kBAAkB;gBAClB,KAAK,MAAM,CAAC,SAAS,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;oBAC1D,MAAM,CAAC,aAAa,EAAE,CAAC;oBAEvB,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;oBAClD,YAAY,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;wBAChC,MAAM,QAAQ,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC;wBACjC,MAAM,MAAM,GAAG,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;wBACxC,IAAI,MAAM,IAAI,MAAM,CAAC,MAAM,EAAE;4BAC5B,MAAM,CAAC,eAAe,EAAE,CAAC;yBACzB;6BAAM;4BACN,MAAM,CAAC,gBAAgB,EAAE,CAAC;4BAC1B,IAAI,QAAQ,IAAI,KAAK,EAAE;gCACtB,MAAM,CAAC,yBAAyB,EAAE,CAAC;6BACnC;yBACD;oBACF,CAAC,CAAC,CAAC;oBAEH,IAAI,YAAY,CAAC,OAAO,EAAE,EAAE;wBAC3B,MAAM,CAAC,eAAe,EAAE,CAAC;qBACzB;iBACD;gBAED,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;gBAC9B,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;YAElC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;gBAClB,qDAAqD;gBACrD,IAAI,YAAoB,CAAC;gBACzB,IAAI,KAAK,YAAY,KAAK,EAAE;oBAC3B,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC;iBAC7B;qBAAM;oBACN,YAAY,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;iBAC7B;gBACD,KAAK,CAAC,SAAS,GAAG,YAAY,CAAC,CAAC;YACjC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE;;gBACf,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;gBACxB,UAAI,CAAC,gBAAgB,0CAAE,GAAG,CAAC,EAAE,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC;QACJ,CAAC;QAEO,2BAA2B,CAClC,OAAe,EACf,IAAY,EACZ,MAAS;YAET,IAAI;gBACH,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;gBACvC,OAAO,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;aAChC;YAAC,OAAO,KAAK,EAAE;gBACf,IAAI,YAAoB,CAAC;gBACzB,IAAI,KAAK,YAAY,wDAAQ,EAAE;oBAC9B,+CAA+C;oBAC/C,YAAY,GAAG,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;wBACzC,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC;oBACpD,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;iBACd;qBAAM,IAAI,KAAK,YAAY,KAAK,EAAE;oBAClC,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC;iBAC7B;qBAAM;oBACN,YAAY,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;iBAC7B;gBACD,yCAAyC;gBACzC,MAAM,IAAI,KAAK,CAAC,gBAAgB,GAAG,IAAI,GAAG,KAAK,GAAG,YAAY,CAAC,CAAC;aAChE;QACF,CAAC;QAED,mBAAmB;YAClB,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;QACnC,CAAC;QAED,GAAG,CAAC,OAAY;YACf,IAAI,IAAI,CAAC,qBAAqB,IAAI,OAAO,IAAI,OAAO,CAAC,GAAG,EAAE;gBACzD,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;aACrB;QACF,CAAC;KACD;IA3hCY,kCAAe,kBA2hC3B;AACF,CAAC,EAhvEgB,kBAAkB,KAAlB,kBAAkB,QAgvElC;AAQD,MAAM,CAAC;IACN,sDAAsD;IACtD,4DAA4D;IAC5D,UAAU,CAAC,GAAG,EAAE;QACf,MAAM,CAAC,iBAAiB,GAAG,IAAI,kBAAkB,CAAC,eAAe,CAAC,wBAAwB,CAAC,CAAC;QAC5F,MAAM,WAAW,GAAG,QAAQ,CAAC,cAAc,CAAC,yBAAyB,CAAC,CAAC;QACvE,IAAI,WAAW,KAAK,IAAI,EAAE;YACzB,MAAM,IAAI,KAAK,CAAC,0DAA0D,CAAC,CAAC;SAC5E;QAED,EAAE,CAAC,aAAa,CAAC,MAAM,CAAC,iBAAiB,EAAE,WAAW,CAAC,CAAC;QAExD,4EAA4E;QAC5E,uEAAuE;QACvE,UAAU,CAAC,GAAG,EAAE;YACf,MAAM,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;QACzD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,yCAAyC;IACjD,CAAC,EAAE,EAAE,CAAC,CAAC;AACR,CAAC,CAAC,CAAC;;;;;;;;;;;;;;;AChyEU;AACN;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,wDAAwD;AACzD;;;;;;;;;;;;;;;;ACvCmD;AAC8C;AACjG,kCAAkC,4DAAY;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,iEAAe,uGAAqB;AACpC;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,EAAC;AACH;;;;;;;;;;;;;;;;ACtCgJ;AAC5D;AACpF,mBAAmB,mGAA4B;AAC/C,gCAAgC,kGAAoB;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,iHAAmC;AACtD;AACA;AACA;AACA;AACA,iEAAe,uGAAqB;AACpC;AACA;AACA;AACA;AACA,mCAAmC,cAAc;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,EAAC;AACH;;;;;;;;;;;;;;;;AC9CqH;AACjC;AACpF,cAAc,8FAAuB;AACrC;AACA;AACA,iCAAiC,gBAAgB,KAAK,aAAa;AACnE;AACA;AACA;AACA,2BAA2B,gGAAkB;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iEAAe,uGAAqB;AACpC;AACA;AACA;AACA,sEAAsE,gBAAgB;AACtF;AACA;AACA;AACA;AACA;AACA,sBAAsB,mEAAmE;AACzF;AACA,EAAE,EAAC;AACH;;;;;;;;;;;;;;;;;ACvCuH;AACnC;AACpF,cAAc,8FAAuB;AACc;AACnD,+BAA+B,kGAAoB;AACnD;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,gFAAgC;AAC/D,yBAAyB,oFAAoC;AAC7D;AACA;AACA;AACA;AACA;AACA,iEAAe,uGAAqB;AACpC,mDAAmD,oDAAoD;AACvG;AACA;AACA,EAAE,EAAC;AACH;;;;;;;;;;;;;;;;;ACvBgJ;AAC5D;AACpF,cAAc,8FAAuB;AACrC,cAAc,8FAAuB;AACrC,mBAAmB,mGAA4B;AACxC,2BAA2B,kGAAoB;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,iHAAmC;AAC1D;AACA;AACA,uBAAuB,iHAAmC;AAC1D;AACA;AACA;AACA,mBAAmB,iHAAmC;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,iHAAmC;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iEAAe,uGAAqB;AACpC,8CAA8C,cAAc;AAC5D;AACA;AACA;AACA;AACA;AACA,sDAAsD,oDAAoD;AAC1G;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,EAAC;AACH;;;;;;;;;;;;;;;ACtHsH;AACtH,6BAA6B,iGAAmB;AAChD;AACA;AACA;AACA;AACA,iEAAe,uGAAqB;AACpC;AACA,EAAE,EAAC;AACH;;;;;;;;;;;;;;;;ACT8H;AAC1C;AACpF,cAAc,8FAAuB;AACrC,6BAA6B,iGAAmB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iEAAe,+GAA6B;AAC5C;AACA,sBAAsB,4DAA4D;AAClF;AACA,EAAE,EAAC;AACH;;;;;;;;;;;;;;;ACxCsH;AACtH,oCAAoC,iGAAmB;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iEAAe,uGAAqB;AACpC;AACA;AACA,6EAA6E,qBAAqB;AAClG;AACA;AACA,EAAE,EAAC;AACH;;;;;;;;;;;;;;;;AC5Ba;AAC0E;AACd;AACzE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,iEAAmB;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4DAA4D,cAAc;AAC1E;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA,aAAa,WAAW,yBAAyB;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6CAA6C;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA,aAAa,WAAW,yBAAyB;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,iFAAsB;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iEAAe,8EAA4B;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B,0CAA0C;AACvE;AACA;AACA;AACA;AACA,yEAAyE,mBAAmB;AAC5F;AACA;AACA;AACA,2DAA2D,uBAAuB;AAClF;AACA;AACA,EAAE,EAAC;AACH;;;;;;;;;;;;;;;;AC1SyD;AAClD;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,+BAA+B,iEAAmB;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;ACnBa;AAC0E;AACvF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,iEAAmB;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iEAAe,8EAA4B;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,0CAA0C,2BAA2B;AAC3F;AACA,EAAE,EAAC;AACH;;;;;;;;;;;;;;;AChCuF;AACvF;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B,iEAAmB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA,wBAAwB;AACxB;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iEAAe,8EAA4B;AAC3C;AACA;AACA,EAAE,EAAC;AACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3CA;AACA;AACA;AACA;AAC0E;AACN;AACc;AACR;AACN;AACkB;AACP;AACd;AACH;AAC2B;AACxB;AACH;AACe;AACN;AACZ;AACiC;AAC3B;AACjE;AACA;AACA;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA,iDAAiD,iFAAgB;AACjE,+CAA+C,6EAAc;AAC7D,oDAAoD,uFAAkB;AACtE,iDAAiD,iFAAgB;AACjE,+CAA+C,6EAAc;AAC7D,qDAAqD,yFAAoB;AACzE,mDAAmD,qFAAiB;AACpE,8CAA8C,2EAAa;AAC3D,6CAA6C,yEAAY;AACzD,sDAAsD,2FAAqB;AAC3E,8CAA8C,4EAAa;AAC3D,6CAA6C,0EAAY;AACzD,kDAAkD,oFAAiB;AACnE,gDAAgD,gFAAe;AAC/D,4CAA4C,wEAAW;AACvD,uDAAuD,8FAAsB;AAC7E,8CAA8C,4EAAa;AAC3D;AACA;AACA;;;;;;;;;;;;;;;AClD0D;AAC1D;AACA;AACA;AACO,sCAAsC,kEAAoB;AACjE;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;ACVuF;AACvF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B,SAAS;AACT;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B,SAAS,IAAI,OAAO;AAC9C;AACA,mDAAmD,YAAY;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,KAAK;AACL;AACA,iCAAiC,iEAAmB;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iEAAe,8EAA4B;AAC3C;AACA;AACA;AACA,oEAAoE,oBAAoB;AACxF;AACA;AACA;AACA;AACA,mCAAmC,wDAAwD;AAC3F;AACA;AACA;AACA,EAAE,EAAC;AACH;;;;;;;;;;;;;;;ACnKuF;AACvF,qCAAqC,iEAAmB;AACxD;AACA;AACA;AACA;AACA,iEAAe,8EAA4B;AAC3C;AACA,EAAE,EAAC;AACH;;;;;;;;;;;;;;;ACTa;AAC0E;AACvF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,iEAAmB;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,iEAAe,8EAA4B;AAC3C;AACA,8DAA8D,wBAAwB;AACtF;AACA;AACA,EAAE,EAAC;AACH;;;;;;;;;;;;;;;;AC/C2D;AACqB;AAChF;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC,wFAAuB;AAC1D;AACA,iEAAe,uEAAqB;AACpC;AACA,EAAE,EAAC;AACH;;;;;;;;;;;;;;;;;ACbA;AACsF;AACvB;AAC/D,cAAc,4EAAuB;AAC9B,6BAA6B,gEAAkB;AACtD;AACA;AACA;AACA;AACA,qDAAqD,yBAAyB;AAC9E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iEAAe,8EAA4B;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2EAA2E,qBAAqB;AAChG;AACA;AACA;AACA;AACA;AACA,yCAAyC,gDAAgD;AACzF;AACA;AACA;AACA,EAAE,EAAC;AACH;;;;;;;;;;;;;;;;ACnHa;AACqD;AACa;AAC/E,gCAAgC,uFAAgB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iEAAe,8EAA4B;AAC3C;AACA;AACA,2BAA2B,mBAAmB;AAC9C;AACA;AACA;AACA,8DAA8D,2BAA2B;AACzF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,EAAC;AACH;;;;;;;;;;;;;;;;AC9Ba;AACqD;AACa;AAC/E;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,8BAA8B;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,uFAAgB;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,wEAAwE;AAChF;AACA;AACA,UAAU,sCAAsC;AAChD;AACA;AACA,uBAAuB,yCAAyC,2BAA2B;AAC3F;AACA;AACA;AACA,iEAAe,8EAA4B;AAC3C;AACA;AACA;AACA,MAAM,gBAAgB;AACtB;AACA;AACA,SAAS,eAAe;AACxB;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,2BAA2B,8CAA8C;AACzE;AACA;AACA;AACA,EAAE,EAAC;AACH;;;;;;;;;;;;;;;;AClG+E;AACb;AAClE,2BAA2B,uFAAgB;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iEAAe,8EAA4B;AAC3C;AACA;AACA;AACA,sBAAsB,0CAA0C,2BAA2B;AAC3F;AACA,EAAE,EAAC;AACH;;;;;;;;;;;;;;;;ACjB2D;AACqB;AAChF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC,wFAAuB;AAC3D;AACA,iEAAe,uEAAqB;AACpC;AACA,EAAE,EAAC;AACH;;;;;;;;;;;;;;;ACfuF;AACvF,4BAA4B,iEAAmB;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iEAAe,8EAA4B;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,EAAC;AACH;;;;;;;;;;;;;;;;ACrBsF;AAC/E,2BAA2B,gEAAkB;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,wBAAwB;AACvD;AACA;AACA,iEAAe,8EAA4B;AAC3C;AACA;AACA,sBAAsB,0CAA0C,2BAA2B;AAC3F;AACA,EAAE,EAAC;AACH;;;;;;;;;;;;;;;AC1BuF;AACvF,gCAAgC,iEAAmB;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iEAAe,8EAA4B;AAC3C;AACA;AACA;AACA;AACA;AACA,uBAAuB,yCAAyC,2BAA2B;AAC3F;AACA;AACA,EAAE,EAAC;AACH;;;;;;;;;;;;;;;;ACtCuF;AAChF,8BAA8B,iEAAmB;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iEAAe,8EAA4B;AAC3C;AACA;AACA,SAAS,aAAa;AACtB,EAAE,EAAC;AACH;;;;;;;;;;;;;;;ACjBa;AAC0E;AACvF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B,iEAAmB;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,4BAA4B;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,UAAU,+BAA+B;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iEAAe,8EAA4B;AAC3C;AACA,EAAE,EAAC;AACH;;;;;;;;;;;;;;;;;;;;;;;;ACpK4D;AAC5D,cAAc,4EAAuB;AACrC,yBAAyB,uFAAkC;AAC3D,cAAc,4EAAuB;AAC9B;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uFAAuF,WAAW;AAClG;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5RA;AACA;AACA;AACA;AACA;AACO;AACP,iIAAiI;AACjI;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "sources": ["webpack:///./extras/zod/lib/index.js", "webpack:///./extras/modules/admin-customizer/admin-customizer.ts", "webpack:///./extras/modules/admin-customizer/admin-customizer-base.js", "webpack:///./extras/modules/admin-customizer/ko-components/ame-ac-content-section.js", "webpack:///./extras/modules/admin-customizer/ko-components/ame-ac-control-group.js", "webpack:///./extras/modules/admin-customizer/ko-components/ame-ac-control.js", "webpack:///./extras/modules/admin-customizer/ko-components/ame-ac-section-link.js", "webpack:///./extras/modules/admin-customizer/ko-components/ame-ac-section.js", "webpack:///./extras/modules/admin-customizer/ko-components/ame-ac-separator.js", "webpack:///./extras/modules/admin-customizer/ko-components/ame-ac-structure.js", "webpack:///./extras/modules/admin-customizer/ko-components/ame-ac-validation-errors.js", "webpack:///./extras/pro-customizables/ko-components/ame-box-dimensions/ame-box-dimensions.js", "webpack:///./extras/pro-customizables/ko-components/ame-choice-control/ame-choice-control.js", "webpack:///./extras/pro-customizables/ko-components/ame-code-editor/ame-code-editor.js", "webpack:///./extras/pro-customizables/ko-components/ame-color-picker/ame-color-picker.js", "webpack:///./extras/pro-customizables/ko-components/ame-components.js", "webpack:///./extras/pro-customizables/ko-components/ame-description/ame-description.js", "webpack:///./extras/pro-customizables/ko-components/ame-font-style-picker/ame-font-style-picker.js", "webpack:///./extras/pro-customizables/ko-components/ame-horizontal-separator/ame-horizontal-separator.js", "webpack:///./extras/pro-customizables/ko-components/ame-image-selector/ame-image-selector.js", "webpack:///./extras/pro-customizables/ko-components/ame-nested-description/ame-nested-description.js", "webpack:///./extras/pro-customizables/ko-components/ame-number-input/ame-number-input.js", "webpack:///./extras/pro-customizables/ko-components/ame-radio-button-bar/ame-radio-button-bar.js", "webpack:///./extras/pro-customizables/ko-components/ame-radio-group/ame-radio-group.js", "webpack:///./extras/pro-customizables/ko-components/ame-select-box/ame-select-box.js", "webpack:///./extras/pro-customizables/ko-components/ame-sibling-description/ame-sibling-description.js", "webpack:///./extras/pro-customizables/ko-components/ame-static-html/ame-static-html.js", "webpack:///./extras/pro-customizables/ko-components/ame-text-input/ame-text-input.js", "webpack:///./extras/pro-customizables/ko-components/ame-toggle-checkbox/ame-toggle-checkbox.js", "webpack:///./extras/pro-customizables/ko-components/ame-unit-dropdown/ame-unit-dropdown.js", "webpack:///./extras/pro-customizables/ko-components/ame-wp-editor/ame-wp-editor.js", "webpack:///./extras/pro-customizables/ko-components/control-base.js", "webpack:///./extras/pro-customizables/ko-components/lazy-popup-slider-adapter.js"], "sourcesContent": ["var util;\n(function (util) {\n    util.assertEqual = (val) => val;\n    function assertIs(_arg) { }\n    util.assertIs = assertIs;\n    function assertNever(_x) {\n        throw new Error();\n    }\n    util.assertNever = assertNever;\n    util.arrayToEnum = (items) => {\n        const obj = {};\n        for (const item of items) {\n            obj[item] = item;\n        }\n        return obj;\n    };\n    util.getValidEnumValues = (obj) => {\n        const validKeys = util.objectKeys(obj).filter((k) => typeof obj[obj[k]] !== \"number\");\n        const filtered = {};\n        for (const k of validKeys) {\n            filtered[k] = obj[k];\n        }\n        return util.objectValues(filtered);\n    };\n    util.objectValues = (obj) => {\n        return util.objectKeys(obj).map(function (e) {\n            return obj[e];\n        });\n    };\n    util.objectKeys = typeof Object.keys === \"function\" // eslint-disable-line ban/ban\n        ? (obj) => Object.keys(obj) // eslint-disable-line ban/ban\n        : (object) => {\n            const keys = [];\n            for (const key in object) {\n                if (Object.prototype.hasOwnProperty.call(object, key)) {\n                    keys.push(key);\n                }\n            }\n            return keys;\n        };\n    util.find = (arr, checker) => {\n        for (const item of arr) {\n            if (checker(item))\n                return item;\n        }\n        return undefined;\n    };\n    util.isInteger = typeof Number.isInteger === \"function\"\n        ? (val) => Number.isInteger(val) // eslint-disable-line ban/ban\n        : (val) => typeof val === \"number\" && isFinite(val) && Math.floor(val) === val;\n    function joinValues(array, separator = \" | \") {\n        return array\n            .map((val) => (typeof val === \"string\" ? `'${val}'` : val))\n            .join(separator);\n    }\n    util.joinValues = joinValues;\n    util.jsonStringifyReplacer = (_, value) => {\n        if (typeof value === \"bigint\") {\n            return value.toString();\n        }\n        return value;\n    };\n})(util || (util = {}));\nvar objectUtil;\n(function (objectUtil) {\n    objectUtil.mergeShapes = (first, second) => {\n        return {\n            ...first,\n            ...second, // second overwrites first\n        };\n    };\n})(objectUtil || (objectUtil = {}));\nconst ZodParsedType = util.arrayToEnum([\n    \"string\",\n    \"nan\",\n    \"number\",\n    \"integer\",\n    \"float\",\n    \"boolean\",\n    \"date\",\n    \"bigint\",\n    \"symbol\",\n    \"function\",\n    \"undefined\",\n    \"null\",\n    \"array\",\n    \"object\",\n    \"unknown\",\n    \"promise\",\n    \"void\",\n    \"never\",\n    \"map\",\n    \"set\",\n]);\nconst getParsedType = (data) => {\n    const t = typeof data;\n    switch (t) {\n        case \"undefined\":\n            return ZodParsedType.undefined;\n        case \"string\":\n            return ZodParsedType.string;\n        case \"number\":\n            return isNaN(data) ? ZodParsedType.nan : ZodParsedType.number;\n        case \"boolean\":\n            return ZodParsedType.boolean;\n        case \"function\":\n            return ZodParsedType.function;\n        case \"bigint\":\n            return ZodParsedType.bigint;\n        case \"symbol\":\n            return ZodParsedType.symbol;\n        case \"object\":\n            if (Array.isArray(data)) {\n                return ZodParsedType.array;\n            }\n            if (data === null) {\n                return ZodParsedType.null;\n            }\n            if (data.then &&\n                typeof data.then === \"function\" &&\n                data.catch &&\n                typeof data.catch === \"function\") {\n                return ZodParsedType.promise;\n            }\n            if (typeof Map !== \"undefined\" && data instanceof Map) {\n                return ZodParsedType.map;\n            }\n            if (typeof Set !== \"undefined\" && data instanceof Set) {\n                return ZodParsedType.set;\n            }\n            if (typeof Date !== \"undefined\" && data instanceof Date) {\n                return ZodParsedType.date;\n            }\n            return ZodParsedType.object;\n        default:\n            return ZodParsedType.unknown;\n    }\n};\n\nconst ZodIssueCode = util.arrayToEnum([\n    \"invalid_type\",\n    \"invalid_literal\",\n    \"custom\",\n    \"invalid_union\",\n    \"invalid_union_discriminator\",\n    \"invalid_enum_value\",\n    \"unrecognized_keys\",\n    \"invalid_arguments\",\n    \"invalid_return_type\",\n    \"invalid_date\",\n    \"invalid_string\",\n    \"too_small\",\n    \"too_big\",\n    \"invalid_intersection_types\",\n    \"not_multiple_of\",\n    \"not_finite\",\n]);\nconst quotelessJson = (obj) => {\n    const json = JSON.stringify(obj, null, 2);\n    return json.replace(/\"([^\"]+)\":/g, \"$1:\");\n};\nclass ZodError extends Error {\n    constructor(issues) {\n        super();\n        this.issues = [];\n        this.addIssue = (sub) => {\n            this.issues = [...this.issues, sub];\n        };\n        this.addIssues = (subs = []) => {\n            this.issues = [...this.issues, ...subs];\n        };\n        const actualProto = new.target.prototype;\n        if (Object.setPrototypeOf) {\n            // eslint-disable-next-line ban/ban\n            Object.setPrototypeOf(this, actualProto);\n        }\n        else {\n            this.__proto__ = actualProto;\n        }\n        this.name = \"ZodError\";\n        this.issues = issues;\n    }\n    get errors() {\n        return this.issues;\n    }\n    format(_mapper) {\n        const mapper = _mapper ||\n            function (issue) {\n                return issue.message;\n            };\n        const fieldErrors = { _errors: [] };\n        const processError = (error) => {\n            for (const issue of error.issues) {\n                if (issue.code === \"invalid_union\") {\n                    issue.unionErrors.map(processError);\n                }\n                else if (issue.code === \"invalid_return_type\") {\n                    processError(issue.returnTypeError);\n                }\n                else if (issue.code === \"invalid_arguments\") {\n                    processError(issue.argumentsError);\n                }\n                else if (issue.path.length === 0) {\n                    fieldErrors._errors.push(mapper(issue));\n                }\n                else {\n                    let curr = fieldErrors;\n                    let i = 0;\n                    while (i < issue.path.length) {\n                        const el = issue.path[i];\n                        const terminal = i === issue.path.length - 1;\n                        if (!terminal) {\n                            curr[el] = curr[el] || { _errors: [] };\n                            // if (typeof el === \"string\") {\n                            //   curr[el] = curr[el] || { _errors: [] };\n                            // } else if (typeof el === \"number\") {\n                            //   const errorArray: any = [];\n                            //   errorArray._errors = [];\n                            //   curr[el] = curr[el] || errorArray;\n                            // }\n                        }\n                        else {\n                            curr[el] = curr[el] || { _errors: [] };\n                            curr[el]._errors.push(mapper(issue));\n                        }\n                        curr = curr[el];\n                        i++;\n                    }\n                }\n            }\n        };\n        processError(this);\n        return fieldErrors;\n    }\n    toString() {\n        return this.message;\n    }\n    get message() {\n        return JSON.stringify(this.issues, util.jsonStringifyReplacer, 2);\n    }\n    get isEmpty() {\n        return this.issues.length === 0;\n    }\n    flatten(mapper = (issue) => issue.message) {\n        const fieldErrors = {};\n        const formErrors = [];\n        for (const sub of this.issues) {\n            if (sub.path.length > 0) {\n                fieldErrors[sub.path[0]] = fieldErrors[sub.path[0]] || [];\n                fieldErrors[sub.path[0]].push(mapper(sub));\n            }\n            else {\n                formErrors.push(mapper(sub));\n            }\n        }\n        return { formErrors, fieldErrors };\n    }\n    get formErrors() {\n        return this.flatten();\n    }\n}\nZodError.create = (issues) => {\n    const error = new ZodError(issues);\n    return error;\n};\n\nconst errorMap = (issue, _ctx) => {\n    let message;\n    switch (issue.code) {\n        case ZodIssueCode.invalid_type:\n            if (issue.received === ZodParsedType.undefined) {\n                message = \"Required\";\n            }\n            else {\n                message = `Expected ${issue.expected}, received ${issue.received}`;\n            }\n            break;\n        case ZodIssueCode.invalid_literal:\n            message = `Invalid literal value, expected ${JSON.stringify(issue.expected, util.jsonStringifyReplacer)}`;\n            break;\n        case ZodIssueCode.unrecognized_keys:\n            message = `Unrecognized key(s) in object: ${util.joinValues(issue.keys, \", \")}`;\n            break;\n        case ZodIssueCode.invalid_union:\n            message = `Invalid input`;\n            break;\n        case ZodIssueCode.invalid_union_discriminator:\n            message = `Invalid discriminator value. Expected ${util.joinValues(issue.options)}`;\n            break;\n        case ZodIssueCode.invalid_enum_value:\n            message = `Invalid enum value. Expected ${util.joinValues(issue.options)}, received '${issue.received}'`;\n            break;\n        case ZodIssueCode.invalid_arguments:\n            message = `Invalid function arguments`;\n            break;\n        case ZodIssueCode.invalid_return_type:\n            message = `Invalid function return type`;\n            break;\n        case ZodIssueCode.invalid_date:\n            message = `Invalid date`;\n            break;\n        case ZodIssueCode.invalid_string:\n            if (typeof issue.validation === \"object\") {\n                if (\"includes\" in issue.validation) {\n                    message = `Invalid input: must include \"${issue.validation.includes}\"`;\n                    if (typeof issue.validation.position === \"number\") {\n                        message = `${message} at one or more positions greater than or equal to ${issue.validation.position}`;\n                    }\n                }\n                else if (\"startsWith\" in issue.validation) {\n                    message = `Invalid input: must start with \"${issue.validation.startsWith}\"`;\n                }\n                else if (\"endsWith\" in issue.validation) {\n                    message = `Invalid input: must end with \"${issue.validation.endsWith}\"`;\n                }\n                else {\n                    util.assertNever(issue.validation);\n                }\n            }\n            else if (issue.validation !== \"regex\") {\n                message = `Invalid ${issue.validation}`;\n            }\n            else {\n                message = \"Invalid\";\n            }\n            break;\n        case ZodIssueCode.too_small:\n            if (issue.type === \"array\")\n                message = `Array must contain ${issue.exact ? \"exactly\" : issue.inclusive ? `at least` : `more than`} ${issue.minimum} element(s)`;\n            else if (issue.type === \"string\")\n                message = `String must contain ${issue.exact ? \"exactly\" : issue.inclusive ? `at least` : `over`} ${issue.minimum} character(s)`;\n            else if (issue.type === \"number\")\n                message = `Number must be ${issue.exact\n                    ? `exactly equal to `\n                    : issue.inclusive\n                        ? `greater than or equal to `\n                        : `greater than `}${issue.minimum}`;\n            else if (issue.type === \"date\")\n                message = `Date must be ${issue.exact\n                    ? `exactly equal to `\n                    : issue.inclusive\n                        ? `greater than or equal to `\n                        : `greater than `}${new Date(Number(issue.minimum))}`;\n            else\n                message = \"Invalid input\";\n            break;\n        case ZodIssueCode.too_big:\n            if (issue.type === \"array\")\n                message = `Array must contain ${issue.exact ? `exactly` : issue.inclusive ? `at most` : `less than`} ${issue.maximum} element(s)`;\n            else if (issue.type === \"string\")\n                message = `String must contain ${issue.exact ? `exactly` : issue.inclusive ? `at most` : `under`} ${issue.maximum} character(s)`;\n            else if (issue.type === \"number\")\n                message = `Number must be ${issue.exact\n                    ? `exactly`\n                    : issue.inclusive\n                        ? `less than or equal to`\n                        : `less than`} ${issue.maximum}`;\n            else if (issue.type === \"bigint\")\n                message = `BigInt must be ${issue.exact\n                    ? `exactly`\n                    : issue.inclusive\n                        ? `less than or equal to`\n                        : `less than`} ${issue.maximum}`;\n            else if (issue.type === \"date\")\n                message = `Date must be ${issue.exact\n                    ? `exactly`\n                    : issue.inclusive\n                        ? `smaller than or equal to`\n                        : `smaller than`} ${new Date(Number(issue.maximum))}`;\n            else\n                message = \"Invalid input\";\n            break;\n        case ZodIssueCode.custom:\n            message = `Invalid input`;\n            break;\n        case ZodIssueCode.invalid_intersection_types:\n            message = `Intersection results could not be merged`;\n            break;\n        case ZodIssueCode.not_multiple_of:\n            message = `Number must be a multiple of ${issue.multipleOf}`;\n            break;\n        case ZodIssueCode.not_finite:\n            message = \"Number must be finite\";\n            break;\n        default:\n            message = _ctx.defaultError;\n            util.assertNever(issue);\n    }\n    return { message };\n};\n\nlet overrideErrorMap = errorMap;\nfunction setErrorMap(map) {\n    overrideErrorMap = map;\n}\nfunction getErrorMap() {\n    return overrideErrorMap;\n}\n\nconst makeIssue = (params) => {\n    const { data, path, errorMaps, issueData } = params;\n    const fullPath = [...path, ...(issueData.path || [])];\n    const fullIssue = {\n        ...issueData,\n        path: fullPath,\n    };\n    let errorMessage = \"\";\n    const maps = errorMaps\n        .filter((m) => !!m)\n        .slice()\n        .reverse();\n    for (const map of maps) {\n        errorMessage = map(fullIssue, { data, defaultError: errorMessage }).message;\n    }\n    return {\n        ...issueData,\n        path: fullPath,\n        message: issueData.message || errorMessage,\n    };\n};\nconst EMPTY_PATH = [];\nfunction addIssueToContext(ctx, issueData) {\n    const issue = makeIssue({\n        issueData: issueData,\n        data: ctx.data,\n        path: ctx.path,\n        errorMaps: [\n            ctx.common.contextualErrorMap,\n            ctx.schemaErrorMap,\n            getErrorMap(),\n            errorMap, // then global default map\n        ].filter((x) => !!x),\n    });\n    ctx.common.issues.push(issue);\n}\nclass ParseStatus {\n    constructor() {\n        this.value = \"valid\";\n    }\n    dirty() {\n        if (this.value === \"valid\")\n            this.value = \"dirty\";\n    }\n    abort() {\n        if (this.value !== \"aborted\")\n            this.value = \"aborted\";\n    }\n    static mergeArray(status, results) {\n        const arrayValue = [];\n        for (const s of results) {\n            if (s.status === \"aborted\")\n                return INVALID;\n            if (s.status === \"dirty\")\n                status.dirty();\n            arrayValue.push(s.value);\n        }\n        return { status: status.value, value: arrayValue };\n    }\n    static async mergeObjectAsync(status, pairs) {\n        const syncPairs = [];\n        for (const pair of pairs) {\n            syncPairs.push({\n                key: await pair.key,\n                value: await pair.value,\n            });\n        }\n        return ParseStatus.mergeObjectSync(status, syncPairs);\n    }\n    static mergeObjectSync(status, pairs) {\n        const finalObject = {};\n        for (const pair of pairs) {\n            const { key, value } = pair;\n            if (key.status === \"aborted\")\n                return INVALID;\n            if (value.status === \"aborted\")\n                return INVALID;\n            if (key.status === \"dirty\")\n                status.dirty();\n            if (value.status === \"dirty\")\n                status.dirty();\n            if (typeof value.value !== \"undefined\" || pair.alwaysSet) {\n                finalObject[key.value] = value.value;\n            }\n        }\n        return { status: status.value, value: finalObject };\n    }\n}\nconst INVALID = Object.freeze({\n    status: \"aborted\",\n});\nconst DIRTY = (value) => ({ status: \"dirty\", value });\nconst OK = (value) => ({ status: \"valid\", value });\nconst isAborted = (x) => x.status === \"aborted\";\nconst isDirty = (x) => x.status === \"dirty\";\nconst isValid = (x) => x.status === \"valid\";\nconst isAsync = (x) => typeof Promise !== \"undefined\" && x instanceof Promise;\n\nvar errorUtil;\n(function (errorUtil) {\n    errorUtil.errToObj = (message) => typeof message === \"string\" ? { message } : message || {};\n    errorUtil.toString = (message) => typeof message === \"string\" ? message : message === null || message === void 0 ? void 0 : message.message;\n})(errorUtil || (errorUtil = {}));\n\nclass ParseInputLazyPath {\n    constructor(parent, value, path, key) {\n        this._cachedPath = [];\n        this.parent = parent;\n        this.data = value;\n        this._path = path;\n        this._key = key;\n    }\n    get path() {\n        if (!this._cachedPath.length) {\n            if (this._key instanceof Array) {\n                this._cachedPath.push(...this._path, ...this._key);\n            }\n            else {\n                this._cachedPath.push(...this._path, this._key);\n            }\n        }\n        return this._cachedPath;\n    }\n}\nconst handleResult = (ctx, result) => {\n    if (isValid(result)) {\n        return { success: true, data: result.value };\n    }\n    else {\n        if (!ctx.common.issues.length) {\n            throw new Error(\"Validation failed but no issues detected.\");\n        }\n        return {\n            success: false,\n            get error() {\n                if (this._error)\n                    return this._error;\n                const error = new ZodError(ctx.common.issues);\n                this._error = error;\n                return this._error;\n            },\n        };\n    }\n};\nfunction processCreateParams(params) {\n    if (!params)\n        return {};\n    const { errorMap, invalid_type_error, required_error, description } = params;\n    if (errorMap && (invalid_type_error || required_error)) {\n        throw new Error(`Can't use \"invalid_type_error\" or \"required_error\" in conjunction with custom error map.`);\n    }\n    if (errorMap)\n        return { errorMap: errorMap, description };\n    const customMap = (iss, ctx) => {\n        if (iss.code !== \"invalid_type\")\n            return { message: ctx.defaultError };\n        if (typeof ctx.data === \"undefined\") {\n            return { message: required_error !== null && required_error !== void 0 ? required_error : ctx.defaultError };\n        }\n        return { message: invalid_type_error !== null && invalid_type_error !== void 0 ? invalid_type_error : ctx.defaultError };\n    };\n    return { errorMap: customMap, description };\n}\nclass ZodType {\n    constructor(def) {\n        /** Alias of safeParseAsync */\n        this.spa = this.safeParseAsync;\n        this._def = def;\n        this.parse = this.parse.bind(this);\n        this.safeParse = this.safeParse.bind(this);\n        this.parseAsync = this.parseAsync.bind(this);\n        this.safeParseAsync = this.safeParseAsync.bind(this);\n        this.spa = this.spa.bind(this);\n        this.refine = this.refine.bind(this);\n        this.refinement = this.refinement.bind(this);\n        this.superRefine = this.superRefine.bind(this);\n        this.optional = this.optional.bind(this);\n        this.nullable = this.nullable.bind(this);\n        this.nullish = this.nullish.bind(this);\n        this.array = this.array.bind(this);\n        this.promise = this.promise.bind(this);\n        this.or = this.or.bind(this);\n        this.and = this.and.bind(this);\n        this.transform = this.transform.bind(this);\n        this.brand = this.brand.bind(this);\n        this.default = this.default.bind(this);\n        this.catch = this.catch.bind(this);\n        this.describe = this.describe.bind(this);\n        this.pipe = this.pipe.bind(this);\n        this.isNullable = this.isNullable.bind(this);\n        this.isOptional = this.isOptional.bind(this);\n    }\n    get description() {\n        return this._def.description;\n    }\n    _getType(input) {\n        return getParsedType(input.data);\n    }\n    _getOrReturnCtx(input, ctx) {\n        return (ctx || {\n            common: input.parent.common,\n            data: input.data,\n            parsedType: getParsedType(input.data),\n            schemaErrorMap: this._def.errorMap,\n            path: input.path,\n            parent: input.parent,\n        });\n    }\n    _processInputParams(input) {\n        return {\n            status: new ParseStatus(),\n            ctx: {\n                common: input.parent.common,\n                data: input.data,\n                parsedType: getParsedType(input.data),\n                schemaErrorMap: this._def.errorMap,\n                path: input.path,\n                parent: input.parent,\n            },\n        };\n    }\n    _parseSync(input) {\n        const result = this._parse(input);\n        if (isAsync(result)) {\n            throw new Error(\"Synchronous parse encountered promise.\");\n        }\n        return result;\n    }\n    _parseAsync(input) {\n        const result = this._parse(input);\n        return Promise.resolve(result);\n    }\n    parse(data, params) {\n        const result = this.safeParse(data, params);\n        if (result.success)\n            return result.data;\n        throw result.error;\n    }\n    safeParse(data, params) {\n        var _a;\n        const ctx = {\n            common: {\n                issues: [],\n                async: (_a = params === null || params === void 0 ? void 0 : params.async) !== null && _a !== void 0 ? _a : false,\n                contextualErrorMap: params === null || params === void 0 ? void 0 : params.errorMap,\n            },\n            path: (params === null || params === void 0 ? void 0 : params.path) || [],\n            schemaErrorMap: this._def.errorMap,\n            parent: null,\n            data,\n            parsedType: getParsedType(data),\n        };\n        const result = this._parseSync({ data, path: ctx.path, parent: ctx });\n        return handleResult(ctx, result);\n    }\n    async parseAsync(data, params) {\n        const result = await this.safeParseAsync(data, params);\n        if (result.success)\n            return result.data;\n        throw result.error;\n    }\n    async safeParseAsync(data, params) {\n        const ctx = {\n            common: {\n                issues: [],\n                contextualErrorMap: params === null || params === void 0 ? void 0 : params.errorMap,\n                async: true,\n            },\n            path: (params === null || params === void 0 ? void 0 : params.path) || [],\n            schemaErrorMap: this._def.errorMap,\n            parent: null,\n            data,\n            parsedType: getParsedType(data),\n        };\n        const maybeAsyncResult = this._parse({ data, path: ctx.path, parent: ctx });\n        const result = await (isAsync(maybeAsyncResult)\n            ? maybeAsyncResult\n            : Promise.resolve(maybeAsyncResult));\n        return handleResult(ctx, result);\n    }\n    refine(check, message) {\n        const getIssueProperties = (val) => {\n            if (typeof message === \"string\" || typeof message === \"undefined\") {\n                return { message };\n            }\n            else if (typeof message === \"function\") {\n                return message(val);\n            }\n            else {\n                return message;\n            }\n        };\n        return this._refinement((val, ctx) => {\n            const result = check(val);\n            const setError = () => ctx.addIssue({\n                code: ZodIssueCode.custom,\n                ...getIssueProperties(val),\n            });\n            if (typeof Promise !== \"undefined\" && result instanceof Promise) {\n                return result.then((data) => {\n                    if (!data) {\n                        setError();\n                        return false;\n                    }\n                    else {\n                        return true;\n                    }\n                });\n            }\n            if (!result) {\n                setError();\n                return false;\n            }\n            else {\n                return true;\n            }\n        });\n    }\n    refinement(check, refinementData) {\n        return this._refinement((val, ctx) => {\n            if (!check(val)) {\n                ctx.addIssue(typeof refinementData === \"function\"\n                    ? refinementData(val, ctx)\n                    : refinementData);\n                return false;\n            }\n            else {\n                return true;\n            }\n        });\n    }\n    _refinement(refinement) {\n        return new ZodEffects({\n            schema: this,\n            typeName: ZodFirstPartyTypeKind.ZodEffects,\n            effect: { type: \"refinement\", refinement },\n        });\n    }\n    superRefine(refinement) {\n        return this._refinement(refinement);\n    }\n    optional() {\n        return ZodOptional.create(this, this._def);\n    }\n    nullable() {\n        return ZodNullable.create(this, this._def);\n    }\n    nullish() {\n        return this.nullable().optional();\n    }\n    array() {\n        return ZodArray.create(this, this._def);\n    }\n    promise() {\n        return ZodPromise.create(this, this._def);\n    }\n    or(option) {\n        return ZodUnion.create([this, option], this._def);\n    }\n    and(incoming) {\n        return ZodIntersection.create(this, incoming, this._def);\n    }\n    transform(transform) {\n        return new ZodEffects({\n            ...processCreateParams(this._def),\n            schema: this,\n            typeName: ZodFirstPartyTypeKind.ZodEffects,\n            effect: { type: \"transform\", transform },\n        });\n    }\n    default(def) {\n        const defaultValueFunc = typeof def === \"function\" ? def : () => def;\n        return new ZodDefault({\n            ...processCreateParams(this._def),\n            innerType: this,\n            defaultValue: defaultValueFunc,\n            typeName: ZodFirstPartyTypeKind.ZodDefault,\n        });\n    }\n    brand() {\n        return new ZodBranded({\n            typeName: ZodFirstPartyTypeKind.ZodBranded,\n            type: this,\n            ...processCreateParams(this._def),\n        });\n    }\n    catch(def) {\n        const catchValueFunc = typeof def === \"function\" ? def : () => def;\n        return new ZodCatch({\n            ...processCreateParams(this._def),\n            innerType: this,\n            catchValue: catchValueFunc,\n            typeName: ZodFirstPartyTypeKind.ZodCatch,\n        });\n    }\n    describe(description) {\n        const This = this.constructor;\n        return new This({\n            ...this._def,\n            description,\n        });\n    }\n    pipe(target) {\n        return ZodPipeline.create(this, target);\n    }\n    isOptional() {\n        return this.safeParse(undefined).success;\n    }\n    isNullable() {\n        return this.safeParse(null).success;\n    }\n}\nconst cuidRegex = /^c[^\\s-]{8,}$/i;\nconst cuid2Regex = /^[a-z][a-z0-9]*$/;\nconst ulidRegex = /[0-9A-HJKMNP-TV-Z]{26}/;\nconst uuidRegex = /^([a-f0-9]{8}-[a-f0-9]{4}-[1-5][a-f0-9]{3}-[a-f0-9]{4}-[a-f0-9]{12}|00000000-0000-0000-0000-000000000000)$/i;\n// from https://stackoverflow.com/a/46181/1550155\n// old version: too slow, didn't support unicode\n// const emailRegex = /^((([a-z]|\\d|[!#\\$%&'\\*\\+\\-\\/=\\?\\^_`{\\|}~]|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])+(\\.([a-z]|\\d|[!#\\$%&'\\*\\+\\-\\/=\\?\\^_`{\\|}~]|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])+)*)|((\\x22)((((\\x20|\\x09)*(\\x0d\\x0a))?(\\x20|\\x09)+)?(([\\x01-\\x08\\x0b\\x0c\\x0e-\\x1f\\x7f]|\\x21|[\\x23-\\x5b]|[\\x5d-\\x7e]|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])|(\\\\([\\x01-\\x09\\x0b\\x0c\\x0d-\\x7f]|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF]))))*(((\\x20|\\x09)*(\\x0d\\x0a))?(\\x20|\\x09)+)?(\\x22)))@((([a-z]|\\d|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])|(([a-z]|\\d|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])([a-z]|\\d|-|\\.|_|~|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])*([a-z]|\\d|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])))\\.)+(([a-z]|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])|(([a-z]|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])([a-z]|\\d|-|\\.|_|~|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])*([a-z]|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])))$/i;\n//old email regex\n// const emailRegex = /^(([^<>()[\\].,;:\\s@\"]+(\\.[^<>()[\\].,;:\\s@\"]+)*)|(\".+\"))@((?!-)([^<>()[\\].,;:\\s@\"]+\\.)+[^<>()[\\].,;:\\s@\"]{1,})[^-<>()[\\].,;:\\s@\"]$/i;\n// eslint-disable-next-line\nconst emailRegex = /^(([^<>()[\\]\\\\.,;:\\s@\\\"]+(\\.[^<>()[\\]\\\\.,;:\\s@\\\"]+)*)|(\\\".+\\\"))@((\\[(((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))\\.){3}((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))\\])|(\\[IPv6:(([a-f0-9]{1,4}:){7}|::([a-f0-9]{1,4}:){0,6}|([a-f0-9]{1,4}:){1}:([a-f0-9]{1,4}:){0,5}|([a-f0-9]{1,4}:){2}:([a-f0-9]{1,4}:){0,4}|([a-f0-9]{1,4}:){3}:([a-f0-9]{1,4}:){0,3}|([a-f0-9]{1,4}:){4}:([a-f0-9]{1,4}:){0,2}|([a-f0-9]{1,4}:){5}:([a-f0-9]{1,4}:){0,1})([a-f0-9]{1,4}|(((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))\\.){3}((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2})))\\])|([A-Za-z0-9]([A-Za-z0-9-]*[A-Za-z0-9])*(\\.[A-Za-z]{2,})+))$/;\n// from https://thekevinscott.com/emojis-in-javascript/#writing-a-regular-expression\nconst emojiRegex = /^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$/u;\nconst ipv4Regex = /^(((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))\\.){3}((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))$/;\nconst ipv6Regex = /^(([a-f0-9]{1,4}:){7}|::([a-f0-9]{1,4}:){0,6}|([a-f0-9]{1,4}:){1}:([a-f0-9]{1,4}:){0,5}|([a-f0-9]{1,4}:){2}:([a-f0-9]{1,4}:){0,4}|([a-f0-9]{1,4}:){3}:([a-f0-9]{1,4}:){0,3}|([a-f0-9]{1,4}:){4}:([a-f0-9]{1,4}:){0,2}|([a-f0-9]{1,4}:){5}:([a-f0-9]{1,4}:){0,1})([a-f0-9]{1,4}|(((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))\\.){3}((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2})))$/;\n// Adapted from https://stackoverflow.com/a/3143231\nconst datetimeRegex = (args) => {\n    if (args.precision) {\n        if (args.offset) {\n            return new RegExp(`^\\\\d{4}-\\\\d{2}-\\\\d{2}T\\\\d{2}:\\\\d{2}:\\\\d{2}\\\\.\\\\d{${args.precision}}(([+-]\\\\d{2}(:?\\\\d{2})?)|Z)$`);\n        }\n        else {\n            return new RegExp(`^\\\\d{4}-\\\\d{2}-\\\\d{2}T\\\\d{2}:\\\\d{2}:\\\\d{2}\\\\.\\\\d{${args.precision}}Z$`);\n        }\n    }\n    else if (args.precision === 0) {\n        if (args.offset) {\n            return new RegExp(`^\\\\d{4}-\\\\d{2}-\\\\d{2}T\\\\d{2}:\\\\d{2}:\\\\d{2}(([+-]\\\\d{2}(:?\\\\d{2})?)|Z)$`);\n        }\n        else {\n            return new RegExp(`^\\\\d{4}-\\\\d{2}-\\\\d{2}T\\\\d{2}:\\\\d{2}:\\\\d{2}Z$`);\n        }\n    }\n    else {\n        if (args.offset) {\n            return new RegExp(`^\\\\d{4}-\\\\d{2}-\\\\d{2}T\\\\d{2}:\\\\d{2}:\\\\d{2}(\\\\.\\\\d+)?(([+-]\\\\d{2}(:?\\\\d{2})?)|Z)$`);\n        }\n        else {\n            return new RegExp(`^\\\\d{4}-\\\\d{2}-\\\\d{2}T\\\\d{2}:\\\\d{2}:\\\\d{2}(\\\\.\\\\d+)?Z$`);\n        }\n    }\n};\nfunction isValidIP(ip, version) {\n    if ((version === \"v4\" || !version) && ipv4Regex.test(ip)) {\n        return true;\n    }\n    if ((version === \"v6\" || !version) && ipv6Regex.test(ip)) {\n        return true;\n    }\n    return false;\n}\nclass ZodString extends ZodType {\n    constructor() {\n        super(...arguments);\n        this._regex = (regex, validation, message) => this.refinement((data) => regex.test(data), {\n            validation,\n            code: ZodIssueCode.invalid_string,\n            ...errorUtil.errToObj(message),\n        });\n        /**\n         * @deprecated Use z.string().min(1) instead.\n         * @see {@link ZodString.min}\n         */\n        this.nonempty = (message) => this.min(1, errorUtil.errToObj(message));\n        this.trim = () => new ZodString({\n            ...this._def,\n            checks: [...this._def.checks, { kind: \"trim\" }],\n        });\n        this.toLowerCase = () => new ZodString({\n            ...this._def,\n            checks: [...this._def.checks, { kind: \"toLowerCase\" }],\n        });\n        this.toUpperCase = () => new ZodString({\n            ...this._def,\n            checks: [...this._def.checks, { kind: \"toUpperCase\" }],\n        });\n    }\n    _parse(input) {\n        if (this._def.coerce) {\n            input.data = String(input.data);\n        }\n        const parsedType = this._getType(input);\n        if (parsedType !== ZodParsedType.string) {\n            const ctx = this._getOrReturnCtx(input);\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_type,\n                expected: ZodParsedType.string,\n                received: ctx.parsedType,\n            }\n            //\n            );\n            return INVALID;\n        }\n        const status = new ParseStatus();\n        let ctx = undefined;\n        for (const check of this._def.checks) {\n            if (check.kind === \"min\") {\n                if (input.data.length < check.value) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        code: ZodIssueCode.too_small,\n                        minimum: check.value,\n                        type: \"string\",\n                        inclusive: true,\n                        exact: false,\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"max\") {\n                if (input.data.length > check.value) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        code: ZodIssueCode.too_big,\n                        maximum: check.value,\n                        type: \"string\",\n                        inclusive: true,\n                        exact: false,\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"length\") {\n                const tooBig = input.data.length > check.value;\n                const tooSmall = input.data.length < check.value;\n                if (tooBig || tooSmall) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    if (tooBig) {\n                        addIssueToContext(ctx, {\n                            code: ZodIssueCode.too_big,\n                            maximum: check.value,\n                            type: \"string\",\n                            inclusive: true,\n                            exact: true,\n                            message: check.message,\n                        });\n                    }\n                    else if (tooSmall) {\n                        addIssueToContext(ctx, {\n                            code: ZodIssueCode.too_small,\n                            minimum: check.value,\n                            type: \"string\",\n                            inclusive: true,\n                            exact: true,\n                            message: check.message,\n                        });\n                    }\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"email\") {\n                if (!emailRegex.test(input.data)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        validation: \"email\",\n                        code: ZodIssueCode.invalid_string,\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"emoji\") {\n                if (!emojiRegex.test(input.data)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        validation: \"emoji\",\n                        code: ZodIssueCode.invalid_string,\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"uuid\") {\n                if (!uuidRegex.test(input.data)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        validation: \"uuid\",\n                        code: ZodIssueCode.invalid_string,\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"cuid\") {\n                if (!cuidRegex.test(input.data)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        validation: \"cuid\",\n                        code: ZodIssueCode.invalid_string,\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"cuid2\") {\n                if (!cuid2Regex.test(input.data)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        validation: \"cuid2\",\n                        code: ZodIssueCode.invalid_string,\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"ulid\") {\n                if (!ulidRegex.test(input.data)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        validation: \"ulid\",\n                        code: ZodIssueCode.invalid_string,\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"url\") {\n                try {\n                    new URL(input.data);\n                }\n                catch (_a) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        validation: \"url\",\n                        code: ZodIssueCode.invalid_string,\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"regex\") {\n                check.regex.lastIndex = 0;\n                const testResult = check.regex.test(input.data);\n                if (!testResult) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        validation: \"regex\",\n                        code: ZodIssueCode.invalid_string,\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"trim\") {\n                input.data = input.data.trim();\n            }\n            else if (check.kind === \"includes\") {\n                if (!input.data.includes(check.value, check.position)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        code: ZodIssueCode.invalid_string,\n                        validation: { includes: check.value, position: check.position },\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"toLowerCase\") {\n                input.data = input.data.toLowerCase();\n            }\n            else if (check.kind === \"toUpperCase\") {\n                input.data = input.data.toUpperCase();\n            }\n            else if (check.kind === \"startsWith\") {\n                if (!input.data.startsWith(check.value)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        code: ZodIssueCode.invalid_string,\n                        validation: { startsWith: check.value },\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"endsWith\") {\n                if (!input.data.endsWith(check.value)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        code: ZodIssueCode.invalid_string,\n                        validation: { endsWith: check.value },\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"datetime\") {\n                const regex = datetimeRegex(check);\n                if (!regex.test(input.data)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        code: ZodIssueCode.invalid_string,\n                        validation: \"datetime\",\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"ip\") {\n                if (!isValidIP(input.data, check.version)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        validation: \"ip\",\n                        code: ZodIssueCode.invalid_string,\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else {\n                util.assertNever(check);\n            }\n        }\n        return { status: status.value, value: input.data };\n    }\n    _addCheck(check) {\n        return new ZodString({\n            ...this._def,\n            checks: [...this._def.checks, check],\n        });\n    }\n    email(message) {\n        return this._addCheck({ kind: \"email\", ...errorUtil.errToObj(message) });\n    }\n    url(message) {\n        return this._addCheck({ kind: \"url\", ...errorUtil.errToObj(message) });\n    }\n    emoji(message) {\n        return this._addCheck({ kind: \"emoji\", ...errorUtil.errToObj(message) });\n    }\n    uuid(message) {\n        return this._addCheck({ kind: \"uuid\", ...errorUtil.errToObj(message) });\n    }\n    cuid(message) {\n        return this._addCheck({ kind: \"cuid\", ...errorUtil.errToObj(message) });\n    }\n    cuid2(message) {\n        return this._addCheck({ kind: \"cuid2\", ...errorUtil.errToObj(message) });\n    }\n    ulid(message) {\n        return this._addCheck({ kind: \"ulid\", ...errorUtil.errToObj(message) });\n    }\n    ip(options) {\n        return this._addCheck({ kind: \"ip\", ...errorUtil.errToObj(options) });\n    }\n    datetime(options) {\n        var _a;\n        if (typeof options === \"string\") {\n            return this._addCheck({\n                kind: \"datetime\",\n                precision: null,\n                offset: false,\n                message: options,\n            });\n        }\n        return this._addCheck({\n            kind: \"datetime\",\n            precision: typeof (options === null || options === void 0 ? void 0 : options.precision) === \"undefined\" ? null : options === null || options === void 0 ? void 0 : options.precision,\n            offset: (_a = options === null || options === void 0 ? void 0 : options.offset) !== null && _a !== void 0 ? _a : false,\n            ...errorUtil.errToObj(options === null || options === void 0 ? void 0 : options.message),\n        });\n    }\n    regex(regex, message) {\n        return this._addCheck({\n            kind: \"regex\",\n            regex: regex,\n            ...errorUtil.errToObj(message),\n        });\n    }\n    includes(value, options) {\n        return this._addCheck({\n            kind: \"includes\",\n            value: value,\n            position: options === null || options === void 0 ? void 0 : options.position,\n            ...errorUtil.errToObj(options === null || options === void 0 ? void 0 : options.message),\n        });\n    }\n    startsWith(value, message) {\n        return this._addCheck({\n            kind: \"startsWith\",\n            value: value,\n            ...errorUtil.errToObj(message),\n        });\n    }\n    endsWith(value, message) {\n        return this._addCheck({\n            kind: \"endsWith\",\n            value: value,\n            ...errorUtil.errToObj(message),\n        });\n    }\n    min(minLength, message) {\n        return this._addCheck({\n            kind: \"min\",\n            value: minLength,\n            ...errorUtil.errToObj(message),\n        });\n    }\n    max(maxLength, message) {\n        return this._addCheck({\n            kind: \"max\",\n            value: maxLength,\n            ...errorUtil.errToObj(message),\n        });\n    }\n    length(len, message) {\n        return this._addCheck({\n            kind: \"length\",\n            value: len,\n            ...errorUtil.errToObj(message),\n        });\n    }\n    get isDatetime() {\n        return !!this._def.checks.find((ch) => ch.kind === \"datetime\");\n    }\n    get isEmail() {\n        return !!this._def.checks.find((ch) => ch.kind === \"email\");\n    }\n    get isURL() {\n        return !!this._def.checks.find((ch) => ch.kind === \"url\");\n    }\n    get isEmoji() {\n        return !!this._def.checks.find((ch) => ch.kind === \"emoji\");\n    }\n    get isUUID() {\n        return !!this._def.checks.find((ch) => ch.kind === \"uuid\");\n    }\n    get isCUID() {\n        return !!this._def.checks.find((ch) => ch.kind === \"cuid\");\n    }\n    get isCUID2() {\n        return !!this._def.checks.find((ch) => ch.kind === \"cuid2\");\n    }\n    get isULID() {\n        return !!this._def.checks.find((ch) => ch.kind === \"ulid\");\n    }\n    get isIP() {\n        return !!this._def.checks.find((ch) => ch.kind === \"ip\");\n    }\n    get minLength() {\n        let min = null;\n        for (const ch of this._def.checks) {\n            if (ch.kind === \"min\") {\n                if (min === null || ch.value > min)\n                    min = ch.value;\n            }\n        }\n        return min;\n    }\n    get maxLength() {\n        let max = null;\n        for (const ch of this._def.checks) {\n            if (ch.kind === \"max\") {\n                if (max === null || ch.value < max)\n                    max = ch.value;\n            }\n        }\n        return max;\n    }\n}\nZodString.create = (params) => {\n    var _a;\n    return new ZodString({\n        checks: [],\n        typeName: ZodFirstPartyTypeKind.ZodString,\n        coerce: (_a = params === null || params === void 0 ? void 0 : params.coerce) !== null && _a !== void 0 ? _a : false,\n        ...processCreateParams(params),\n    });\n};\n// https://stackoverflow.com/questions/3966484/why-does-modulus-operator-return-fractional-number-in-javascript/31711034#31711034\nfunction floatSafeRemainder(val, step) {\n    const valDecCount = (val.toString().split(\".\")[1] || \"\").length;\n    const stepDecCount = (step.toString().split(\".\")[1] || \"\").length;\n    const decCount = valDecCount > stepDecCount ? valDecCount : stepDecCount;\n    const valInt = parseInt(val.toFixed(decCount).replace(\".\", \"\"));\n    const stepInt = parseInt(step.toFixed(decCount).replace(\".\", \"\"));\n    return (valInt % stepInt) / Math.pow(10, decCount);\n}\nclass ZodNumber extends ZodType {\n    constructor() {\n        super(...arguments);\n        this.min = this.gte;\n        this.max = this.lte;\n        this.step = this.multipleOf;\n    }\n    _parse(input) {\n        if (this._def.coerce) {\n            input.data = Number(input.data);\n        }\n        const parsedType = this._getType(input);\n        if (parsedType !== ZodParsedType.number) {\n            const ctx = this._getOrReturnCtx(input);\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_type,\n                expected: ZodParsedType.number,\n                received: ctx.parsedType,\n            });\n            return INVALID;\n        }\n        let ctx = undefined;\n        const status = new ParseStatus();\n        for (const check of this._def.checks) {\n            if (check.kind === \"int\") {\n                if (!util.isInteger(input.data)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        code: ZodIssueCode.invalid_type,\n                        expected: \"integer\",\n                        received: \"float\",\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"min\") {\n                const tooSmall = check.inclusive\n                    ? input.data < check.value\n                    : input.data <= check.value;\n                if (tooSmall) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        code: ZodIssueCode.too_small,\n                        minimum: check.value,\n                        type: \"number\",\n                        inclusive: check.inclusive,\n                        exact: false,\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"max\") {\n                const tooBig = check.inclusive\n                    ? input.data > check.value\n                    : input.data >= check.value;\n                if (tooBig) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        code: ZodIssueCode.too_big,\n                        maximum: check.value,\n                        type: \"number\",\n                        inclusive: check.inclusive,\n                        exact: false,\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"multipleOf\") {\n                if (floatSafeRemainder(input.data, check.value) !== 0) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        code: ZodIssueCode.not_multiple_of,\n                        multipleOf: check.value,\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"finite\") {\n                if (!Number.isFinite(input.data)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        code: ZodIssueCode.not_finite,\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else {\n                util.assertNever(check);\n            }\n        }\n        return { status: status.value, value: input.data };\n    }\n    gte(value, message) {\n        return this.setLimit(\"min\", value, true, errorUtil.toString(message));\n    }\n    gt(value, message) {\n        return this.setLimit(\"min\", value, false, errorUtil.toString(message));\n    }\n    lte(value, message) {\n        return this.setLimit(\"max\", value, true, errorUtil.toString(message));\n    }\n    lt(value, message) {\n        return this.setLimit(\"max\", value, false, errorUtil.toString(message));\n    }\n    setLimit(kind, value, inclusive, message) {\n        return new ZodNumber({\n            ...this._def,\n            checks: [\n                ...this._def.checks,\n                {\n                    kind,\n                    value,\n                    inclusive,\n                    message: errorUtil.toString(message),\n                },\n            ],\n        });\n    }\n    _addCheck(check) {\n        return new ZodNumber({\n            ...this._def,\n            checks: [...this._def.checks, check],\n        });\n    }\n    int(message) {\n        return this._addCheck({\n            kind: \"int\",\n            message: errorUtil.toString(message),\n        });\n    }\n    positive(message) {\n        return this._addCheck({\n            kind: \"min\",\n            value: 0,\n            inclusive: false,\n            message: errorUtil.toString(message),\n        });\n    }\n    negative(message) {\n        return this._addCheck({\n            kind: \"max\",\n            value: 0,\n            inclusive: false,\n            message: errorUtil.toString(message),\n        });\n    }\n    nonpositive(message) {\n        return this._addCheck({\n            kind: \"max\",\n            value: 0,\n            inclusive: true,\n            message: errorUtil.toString(message),\n        });\n    }\n    nonnegative(message) {\n        return this._addCheck({\n            kind: \"min\",\n            value: 0,\n            inclusive: true,\n            message: errorUtil.toString(message),\n        });\n    }\n    multipleOf(value, message) {\n        return this._addCheck({\n            kind: \"multipleOf\",\n            value: value,\n            message: errorUtil.toString(message),\n        });\n    }\n    finite(message) {\n        return this._addCheck({\n            kind: \"finite\",\n            message: errorUtil.toString(message),\n        });\n    }\n    safe(message) {\n        return this._addCheck({\n            kind: \"min\",\n            inclusive: true,\n            value: Number.MIN_SAFE_INTEGER,\n            message: errorUtil.toString(message),\n        })._addCheck({\n            kind: \"max\",\n            inclusive: true,\n            value: Number.MAX_SAFE_INTEGER,\n            message: errorUtil.toString(message),\n        });\n    }\n    get minValue() {\n        let min = null;\n        for (const ch of this._def.checks) {\n            if (ch.kind === \"min\") {\n                if (min === null || ch.value > min)\n                    min = ch.value;\n            }\n        }\n        return min;\n    }\n    get maxValue() {\n        let max = null;\n        for (const ch of this._def.checks) {\n            if (ch.kind === \"max\") {\n                if (max === null || ch.value < max)\n                    max = ch.value;\n            }\n        }\n        return max;\n    }\n    get isInt() {\n        return !!this._def.checks.find((ch) => ch.kind === \"int\" ||\n            (ch.kind === \"multipleOf\" && util.isInteger(ch.value)));\n    }\n    get isFinite() {\n        let max = null, min = null;\n        for (const ch of this._def.checks) {\n            if (ch.kind === \"finite\" ||\n                ch.kind === \"int\" ||\n                ch.kind === \"multipleOf\") {\n                return true;\n            }\n            else if (ch.kind === \"min\") {\n                if (min === null || ch.value > min)\n                    min = ch.value;\n            }\n            else if (ch.kind === \"max\") {\n                if (max === null || ch.value < max)\n                    max = ch.value;\n            }\n        }\n        return Number.isFinite(min) && Number.isFinite(max);\n    }\n}\nZodNumber.create = (params) => {\n    return new ZodNumber({\n        checks: [],\n        typeName: ZodFirstPartyTypeKind.ZodNumber,\n        coerce: (params === null || params === void 0 ? void 0 : params.coerce) || false,\n        ...processCreateParams(params),\n    });\n};\nclass ZodBigInt extends ZodType {\n    constructor() {\n        super(...arguments);\n        this.min = this.gte;\n        this.max = this.lte;\n    }\n    _parse(input) {\n        if (this._def.coerce) {\n            input.data = BigInt(input.data);\n        }\n        const parsedType = this._getType(input);\n        if (parsedType !== ZodParsedType.bigint) {\n            const ctx = this._getOrReturnCtx(input);\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_type,\n                expected: ZodParsedType.bigint,\n                received: ctx.parsedType,\n            });\n            return INVALID;\n        }\n        let ctx = undefined;\n        const status = new ParseStatus();\n        for (const check of this._def.checks) {\n            if (check.kind === \"min\") {\n                const tooSmall = check.inclusive\n                    ? input.data < check.value\n                    : input.data <= check.value;\n                if (tooSmall) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        code: ZodIssueCode.too_small,\n                        type: \"bigint\",\n                        minimum: check.value,\n                        inclusive: check.inclusive,\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"max\") {\n                const tooBig = check.inclusive\n                    ? input.data > check.value\n                    : input.data >= check.value;\n                if (tooBig) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        code: ZodIssueCode.too_big,\n                        type: \"bigint\",\n                        maximum: check.value,\n                        inclusive: check.inclusive,\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"multipleOf\") {\n                if (input.data % check.value !== BigInt(0)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        code: ZodIssueCode.not_multiple_of,\n                        multipleOf: check.value,\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else {\n                util.assertNever(check);\n            }\n        }\n        return { status: status.value, value: input.data };\n    }\n    gte(value, message) {\n        return this.setLimit(\"min\", value, true, errorUtil.toString(message));\n    }\n    gt(value, message) {\n        return this.setLimit(\"min\", value, false, errorUtil.toString(message));\n    }\n    lte(value, message) {\n        return this.setLimit(\"max\", value, true, errorUtil.toString(message));\n    }\n    lt(value, message) {\n        return this.setLimit(\"max\", value, false, errorUtil.toString(message));\n    }\n    setLimit(kind, value, inclusive, message) {\n        return new ZodBigInt({\n            ...this._def,\n            checks: [\n                ...this._def.checks,\n                {\n                    kind,\n                    value,\n                    inclusive,\n                    message: errorUtil.toString(message),\n                },\n            ],\n        });\n    }\n    _addCheck(check) {\n        return new ZodBigInt({\n            ...this._def,\n            checks: [...this._def.checks, check],\n        });\n    }\n    positive(message) {\n        return this._addCheck({\n            kind: \"min\",\n            value: BigInt(0),\n            inclusive: false,\n            message: errorUtil.toString(message),\n        });\n    }\n    negative(message) {\n        return this._addCheck({\n            kind: \"max\",\n            value: BigInt(0),\n            inclusive: false,\n            message: errorUtil.toString(message),\n        });\n    }\n    nonpositive(message) {\n        return this._addCheck({\n            kind: \"max\",\n            value: BigInt(0),\n            inclusive: true,\n            message: errorUtil.toString(message),\n        });\n    }\n    nonnegative(message) {\n        return this._addCheck({\n            kind: \"min\",\n            value: BigInt(0),\n            inclusive: true,\n            message: errorUtil.toString(message),\n        });\n    }\n    multipleOf(value, message) {\n        return this._addCheck({\n            kind: \"multipleOf\",\n            value,\n            message: errorUtil.toString(message),\n        });\n    }\n    get minValue() {\n        let min = null;\n        for (const ch of this._def.checks) {\n            if (ch.kind === \"min\") {\n                if (min === null || ch.value > min)\n                    min = ch.value;\n            }\n        }\n        return min;\n    }\n    get maxValue() {\n        let max = null;\n        for (const ch of this._def.checks) {\n            if (ch.kind === \"max\") {\n                if (max === null || ch.value < max)\n                    max = ch.value;\n            }\n        }\n        return max;\n    }\n}\nZodBigInt.create = (params) => {\n    var _a;\n    return new ZodBigInt({\n        checks: [],\n        typeName: ZodFirstPartyTypeKind.ZodBigInt,\n        coerce: (_a = params === null || params === void 0 ? void 0 : params.coerce) !== null && _a !== void 0 ? _a : false,\n        ...processCreateParams(params),\n    });\n};\nclass ZodBoolean extends ZodType {\n    _parse(input) {\n        if (this._def.coerce) {\n            input.data = Boolean(input.data);\n        }\n        const parsedType = this._getType(input);\n        if (parsedType !== ZodParsedType.boolean) {\n            const ctx = this._getOrReturnCtx(input);\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_type,\n                expected: ZodParsedType.boolean,\n                received: ctx.parsedType,\n            });\n            return INVALID;\n        }\n        return OK(input.data);\n    }\n}\nZodBoolean.create = (params) => {\n    return new ZodBoolean({\n        typeName: ZodFirstPartyTypeKind.ZodBoolean,\n        coerce: (params === null || params === void 0 ? void 0 : params.coerce) || false,\n        ...processCreateParams(params),\n    });\n};\nclass ZodDate extends ZodType {\n    _parse(input) {\n        if (this._def.coerce) {\n            input.data = new Date(input.data);\n        }\n        const parsedType = this._getType(input);\n        if (parsedType !== ZodParsedType.date) {\n            const ctx = this._getOrReturnCtx(input);\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_type,\n                expected: ZodParsedType.date,\n                received: ctx.parsedType,\n            });\n            return INVALID;\n        }\n        if (isNaN(input.data.getTime())) {\n            const ctx = this._getOrReturnCtx(input);\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_date,\n            });\n            return INVALID;\n        }\n        const status = new ParseStatus();\n        let ctx = undefined;\n        for (const check of this._def.checks) {\n            if (check.kind === \"min\") {\n                if (input.data.getTime() < check.value) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        code: ZodIssueCode.too_small,\n                        message: check.message,\n                        inclusive: true,\n                        exact: false,\n                        minimum: check.value,\n                        type: \"date\",\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"max\") {\n                if (input.data.getTime() > check.value) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        code: ZodIssueCode.too_big,\n                        message: check.message,\n                        inclusive: true,\n                        exact: false,\n                        maximum: check.value,\n                        type: \"date\",\n                    });\n                    status.dirty();\n                }\n            }\n            else {\n                util.assertNever(check);\n            }\n        }\n        return {\n            status: status.value,\n            value: new Date(input.data.getTime()),\n        };\n    }\n    _addCheck(check) {\n        return new ZodDate({\n            ...this._def,\n            checks: [...this._def.checks, check],\n        });\n    }\n    min(minDate, message) {\n        return this._addCheck({\n            kind: \"min\",\n            value: minDate.getTime(),\n            message: errorUtil.toString(message),\n        });\n    }\n    max(maxDate, message) {\n        return this._addCheck({\n            kind: \"max\",\n            value: maxDate.getTime(),\n            message: errorUtil.toString(message),\n        });\n    }\n    get minDate() {\n        let min = null;\n        for (const ch of this._def.checks) {\n            if (ch.kind === \"min\") {\n                if (min === null || ch.value > min)\n                    min = ch.value;\n            }\n        }\n        return min != null ? new Date(min) : null;\n    }\n    get maxDate() {\n        let max = null;\n        for (const ch of this._def.checks) {\n            if (ch.kind === \"max\") {\n                if (max === null || ch.value < max)\n                    max = ch.value;\n            }\n        }\n        return max != null ? new Date(max) : null;\n    }\n}\nZodDate.create = (params) => {\n    return new ZodDate({\n        checks: [],\n        coerce: (params === null || params === void 0 ? void 0 : params.coerce) || false,\n        typeName: ZodFirstPartyTypeKind.ZodDate,\n        ...processCreateParams(params),\n    });\n};\nclass ZodSymbol extends ZodType {\n    _parse(input) {\n        const parsedType = this._getType(input);\n        if (parsedType !== ZodParsedType.symbol) {\n            const ctx = this._getOrReturnCtx(input);\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_type,\n                expected: ZodParsedType.symbol,\n                received: ctx.parsedType,\n            });\n            return INVALID;\n        }\n        return OK(input.data);\n    }\n}\nZodSymbol.create = (params) => {\n    return new ZodSymbol({\n        typeName: ZodFirstPartyTypeKind.ZodSymbol,\n        ...processCreateParams(params),\n    });\n};\nclass ZodUndefined extends ZodType {\n    _parse(input) {\n        const parsedType = this._getType(input);\n        if (parsedType !== ZodParsedType.undefined) {\n            const ctx = this._getOrReturnCtx(input);\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_type,\n                expected: ZodParsedType.undefined,\n                received: ctx.parsedType,\n            });\n            return INVALID;\n        }\n        return OK(input.data);\n    }\n}\nZodUndefined.create = (params) => {\n    return new ZodUndefined({\n        typeName: ZodFirstPartyTypeKind.ZodUndefined,\n        ...processCreateParams(params),\n    });\n};\nclass ZodNull extends ZodType {\n    _parse(input) {\n        const parsedType = this._getType(input);\n        if (parsedType !== ZodParsedType.null) {\n            const ctx = this._getOrReturnCtx(input);\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_type,\n                expected: ZodParsedType.null,\n                received: ctx.parsedType,\n            });\n            return INVALID;\n        }\n        return OK(input.data);\n    }\n}\nZodNull.create = (params) => {\n    return new ZodNull({\n        typeName: ZodFirstPartyTypeKind.ZodNull,\n        ...processCreateParams(params),\n    });\n};\nclass ZodAny extends ZodType {\n    constructor() {\n        super(...arguments);\n        // to prevent instances of other classes from extending ZodAny. this causes issues with catchall in ZodObject.\n        this._any = true;\n    }\n    _parse(input) {\n        return OK(input.data);\n    }\n}\nZodAny.create = (params) => {\n    return new ZodAny({\n        typeName: ZodFirstPartyTypeKind.ZodAny,\n        ...processCreateParams(params),\n    });\n};\nclass ZodUnknown extends ZodType {\n    constructor() {\n        super(...arguments);\n        // required\n        this._unknown = true;\n    }\n    _parse(input) {\n        return OK(input.data);\n    }\n}\nZodUnknown.create = (params) => {\n    return new ZodUnknown({\n        typeName: ZodFirstPartyTypeKind.ZodUnknown,\n        ...processCreateParams(params),\n    });\n};\nclass ZodNever extends ZodType {\n    _parse(input) {\n        const ctx = this._getOrReturnCtx(input);\n        addIssueToContext(ctx, {\n            code: ZodIssueCode.invalid_type,\n            expected: ZodParsedType.never,\n            received: ctx.parsedType,\n        });\n        return INVALID;\n    }\n}\nZodNever.create = (params) => {\n    return new ZodNever({\n        typeName: ZodFirstPartyTypeKind.ZodNever,\n        ...processCreateParams(params),\n    });\n};\nclass ZodVoid extends ZodType {\n    _parse(input) {\n        const parsedType = this._getType(input);\n        if (parsedType !== ZodParsedType.undefined) {\n            const ctx = this._getOrReturnCtx(input);\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_type,\n                expected: ZodParsedType.void,\n                received: ctx.parsedType,\n            });\n            return INVALID;\n        }\n        return OK(input.data);\n    }\n}\nZodVoid.create = (params) => {\n    return new ZodVoid({\n        typeName: ZodFirstPartyTypeKind.ZodVoid,\n        ...processCreateParams(params),\n    });\n};\nclass ZodArray extends ZodType {\n    _parse(input) {\n        const { ctx, status } = this._processInputParams(input);\n        const def = this._def;\n        if (ctx.parsedType !== ZodParsedType.array) {\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_type,\n                expected: ZodParsedType.array,\n                received: ctx.parsedType,\n            });\n            return INVALID;\n        }\n        if (def.exactLength !== null) {\n            const tooBig = ctx.data.length > def.exactLength.value;\n            const tooSmall = ctx.data.length < def.exactLength.value;\n            if (tooBig || tooSmall) {\n                addIssueToContext(ctx, {\n                    code: tooBig ? ZodIssueCode.too_big : ZodIssueCode.too_small,\n                    minimum: (tooSmall ? def.exactLength.value : undefined),\n                    maximum: (tooBig ? def.exactLength.value : undefined),\n                    type: \"array\",\n                    inclusive: true,\n                    exact: true,\n                    message: def.exactLength.message,\n                });\n                status.dirty();\n            }\n        }\n        if (def.minLength !== null) {\n            if (ctx.data.length < def.minLength.value) {\n                addIssueToContext(ctx, {\n                    code: ZodIssueCode.too_small,\n                    minimum: def.minLength.value,\n                    type: \"array\",\n                    inclusive: true,\n                    exact: false,\n                    message: def.minLength.message,\n                });\n                status.dirty();\n            }\n        }\n        if (def.maxLength !== null) {\n            if (ctx.data.length > def.maxLength.value) {\n                addIssueToContext(ctx, {\n                    code: ZodIssueCode.too_big,\n                    maximum: def.maxLength.value,\n                    type: \"array\",\n                    inclusive: true,\n                    exact: false,\n                    message: def.maxLength.message,\n                });\n                status.dirty();\n            }\n        }\n        if (ctx.common.async) {\n            return Promise.all([...ctx.data].map((item, i) => {\n                return def.type._parseAsync(new ParseInputLazyPath(ctx, item, ctx.path, i));\n            })).then((result) => {\n                return ParseStatus.mergeArray(status, result);\n            });\n        }\n        const result = [...ctx.data].map((item, i) => {\n            return def.type._parseSync(new ParseInputLazyPath(ctx, item, ctx.path, i));\n        });\n        return ParseStatus.mergeArray(status, result);\n    }\n    get element() {\n        return this._def.type;\n    }\n    min(minLength, message) {\n        return new ZodArray({\n            ...this._def,\n            minLength: { value: minLength, message: errorUtil.toString(message) },\n        });\n    }\n    max(maxLength, message) {\n        return new ZodArray({\n            ...this._def,\n            maxLength: { value: maxLength, message: errorUtil.toString(message) },\n        });\n    }\n    length(len, message) {\n        return new ZodArray({\n            ...this._def,\n            exactLength: { value: len, message: errorUtil.toString(message) },\n        });\n    }\n    nonempty(message) {\n        return this.min(1, message);\n    }\n}\nZodArray.create = (schema, params) => {\n    return new ZodArray({\n        type: schema,\n        minLength: null,\n        maxLength: null,\n        exactLength: null,\n        typeName: ZodFirstPartyTypeKind.ZodArray,\n        ...processCreateParams(params),\n    });\n};\nfunction deepPartialify(schema) {\n    if (schema instanceof ZodObject) {\n        const newShape = {};\n        for (const key in schema.shape) {\n            const fieldSchema = schema.shape[key];\n            newShape[key] = ZodOptional.create(deepPartialify(fieldSchema));\n        }\n        return new ZodObject({\n            ...schema._def,\n            shape: () => newShape,\n        });\n    }\n    else if (schema instanceof ZodArray) {\n        return new ZodArray({\n            ...schema._def,\n            type: deepPartialify(schema.element),\n        });\n    }\n    else if (schema instanceof ZodOptional) {\n        return ZodOptional.create(deepPartialify(schema.unwrap()));\n    }\n    else if (schema instanceof ZodNullable) {\n        return ZodNullable.create(deepPartialify(schema.unwrap()));\n    }\n    else if (schema instanceof ZodTuple) {\n        return ZodTuple.create(schema.items.map((item) => deepPartialify(item)));\n    }\n    else {\n        return schema;\n    }\n}\nclass ZodObject extends ZodType {\n    constructor() {\n        super(...arguments);\n        this._cached = null;\n        /**\n         * @deprecated In most cases, this is no longer needed - unknown properties are now silently stripped.\n         * If you want to pass through unknown properties, use `.passthrough()` instead.\n         */\n        this.nonstrict = this.passthrough;\n        // extend<\n        //   Augmentation extends ZodRawShape,\n        //   NewOutput extends util.flatten<{\n        //     [k in keyof Augmentation | keyof Output]: k extends keyof Augmentation\n        //       ? Augmentation[k][\"_output\"]\n        //       : k extends keyof Output\n        //       ? Output[k]\n        //       : never;\n        //   }>,\n        //   NewInput extends util.flatten<{\n        //     [k in keyof Augmentation | keyof Input]: k extends keyof Augmentation\n        //       ? Augmentation[k][\"_input\"]\n        //       : k extends keyof Input\n        //       ? Input[k]\n        //       : never;\n        //   }>\n        // >(\n        //   augmentation: Augmentation\n        // ): ZodObject<\n        //   extendShape<T, Augmentation>,\n        //   UnknownKeys,\n        //   Catchall,\n        //   NewOutput,\n        //   NewInput\n        // > {\n        //   return new ZodObject({\n        //     ...this._def,\n        //     shape: () => ({\n        //       ...this._def.shape(),\n        //       ...augmentation,\n        //     }),\n        //   }) as any;\n        // }\n        /**\n         * @deprecated Use `.extend` instead\n         *  */\n        this.augment = this.extend;\n    }\n    _getCached() {\n        if (this._cached !== null)\n            return this._cached;\n        const shape = this._def.shape();\n        const keys = util.objectKeys(shape);\n        return (this._cached = { shape, keys });\n    }\n    _parse(input) {\n        const parsedType = this._getType(input);\n        if (parsedType !== ZodParsedType.object) {\n            const ctx = this._getOrReturnCtx(input);\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_type,\n                expected: ZodParsedType.object,\n                received: ctx.parsedType,\n            });\n            return INVALID;\n        }\n        const { status, ctx } = this._processInputParams(input);\n        const { shape, keys: shapeKeys } = this._getCached();\n        const extraKeys = [];\n        if (!(this._def.catchall instanceof ZodNever &&\n            this._def.unknownKeys === \"strip\")) {\n            for (const key in ctx.data) {\n                if (!shapeKeys.includes(key)) {\n                    extraKeys.push(key);\n                }\n            }\n        }\n        const pairs = [];\n        for (const key of shapeKeys) {\n            const keyValidator = shape[key];\n            const value = ctx.data[key];\n            pairs.push({\n                key: { status: \"valid\", value: key },\n                value: keyValidator._parse(new ParseInputLazyPath(ctx, value, ctx.path, key)),\n                alwaysSet: key in ctx.data,\n            });\n        }\n        if (this._def.catchall instanceof ZodNever) {\n            const unknownKeys = this._def.unknownKeys;\n            if (unknownKeys === \"passthrough\") {\n                for (const key of extraKeys) {\n                    pairs.push({\n                        key: { status: \"valid\", value: key },\n                        value: { status: \"valid\", value: ctx.data[key] },\n                    });\n                }\n            }\n            else if (unknownKeys === \"strict\") {\n                if (extraKeys.length > 0) {\n                    addIssueToContext(ctx, {\n                        code: ZodIssueCode.unrecognized_keys,\n                        keys: extraKeys,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (unknownKeys === \"strip\") ;\n            else {\n                throw new Error(`Internal ZodObject error: invalid unknownKeys value.`);\n            }\n        }\n        else {\n            // run catchall validation\n            const catchall = this._def.catchall;\n            for (const key of extraKeys) {\n                const value = ctx.data[key];\n                pairs.push({\n                    key: { status: \"valid\", value: key },\n                    value: catchall._parse(new ParseInputLazyPath(ctx, value, ctx.path, key) //, ctx.child(key), value, getParsedType(value)\n                    ),\n                    alwaysSet: key in ctx.data,\n                });\n            }\n        }\n        if (ctx.common.async) {\n            return Promise.resolve()\n                .then(async () => {\n                const syncPairs = [];\n                for (const pair of pairs) {\n                    const key = await pair.key;\n                    syncPairs.push({\n                        key,\n                        value: await pair.value,\n                        alwaysSet: pair.alwaysSet,\n                    });\n                }\n                return syncPairs;\n            })\n                .then((syncPairs) => {\n                return ParseStatus.mergeObjectSync(status, syncPairs);\n            });\n        }\n        else {\n            return ParseStatus.mergeObjectSync(status, pairs);\n        }\n    }\n    get shape() {\n        return this._def.shape();\n    }\n    strict(message) {\n        errorUtil.errToObj;\n        return new ZodObject({\n            ...this._def,\n            unknownKeys: \"strict\",\n            ...(message !== undefined\n                ? {\n                    errorMap: (issue, ctx) => {\n                        var _a, _b, _c, _d;\n                        const defaultError = (_c = (_b = (_a = this._def).errorMap) === null || _b === void 0 ? void 0 : _b.call(_a, issue, ctx).message) !== null && _c !== void 0 ? _c : ctx.defaultError;\n                        if (issue.code === \"unrecognized_keys\")\n                            return {\n                                message: (_d = errorUtil.errToObj(message).message) !== null && _d !== void 0 ? _d : defaultError,\n                            };\n                        return {\n                            message: defaultError,\n                        };\n                    },\n                }\n                : {}),\n        });\n    }\n    strip() {\n        return new ZodObject({\n            ...this._def,\n            unknownKeys: \"strip\",\n        });\n    }\n    passthrough() {\n        return new ZodObject({\n            ...this._def,\n            unknownKeys: \"passthrough\",\n        });\n    }\n    // const AugmentFactory =\n    //   <Def extends ZodObjectDef>(def: Def) =>\n    //   <Augmentation extends ZodRawShape>(\n    //     augmentation: Augmentation\n    //   ): ZodObject<\n    //     extendShape<ReturnType<Def[\"shape\"]>, Augmentation>,\n    //     Def[\"unknownKeys\"],\n    //     Def[\"catchall\"]\n    //   > => {\n    //     return new ZodObject({\n    //       ...def,\n    //       shape: () => ({\n    //         ...def.shape(),\n    //         ...augmentation,\n    //       }),\n    //     }) as any;\n    //   };\n    extend(augmentation) {\n        return new ZodObject({\n            ...this._def,\n            shape: () => ({\n                ...this._def.shape(),\n                ...augmentation,\n            }),\n        });\n    }\n    /**\n     * Prior to zod@1.0.12 there was a bug in the\n     * inferred type of merged objects. Please\n     * upgrade if you are experiencing issues.\n     */\n    merge(merging) {\n        const merged = new ZodObject({\n            unknownKeys: merging._def.unknownKeys,\n            catchall: merging._def.catchall,\n            shape: () => ({\n                ...this._def.shape(),\n                ...merging._def.shape(),\n            }),\n            typeName: ZodFirstPartyTypeKind.ZodObject,\n        });\n        return merged;\n    }\n    // merge<\n    //   Incoming extends AnyZodObject,\n    //   Augmentation extends Incoming[\"shape\"],\n    //   NewOutput extends {\n    //     [k in keyof Augmentation | keyof Output]: k extends keyof Augmentation\n    //       ? Augmentation[k][\"_output\"]\n    //       : k extends keyof Output\n    //       ? Output[k]\n    //       : never;\n    //   },\n    //   NewInput extends {\n    //     [k in keyof Augmentation | keyof Input]: k extends keyof Augmentation\n    //       ? Augmentation[k][\"_input\"]\n    //       : k extends keyof Input\n    //       ? Input[k]\n    //       : never;\n    //   }\n    // >(\n    //   merging: Incoming\n    // ): ZodObject<\n    //   extendShape<T, ReturnType<Incoming[\"_def\"][\"shape\"]>>,\n    //   Incoming[\"_def\"][\"unknownKeys\"],\n    //   Incoming[\"_def\"][\"catchall\"],\n    //   NewOutput,\n    //   NewInput\n    // > {\n    //   const merged: any = new ZodObject({\n    //     unknownKeys: merging._def.unknownKeys,\n    //     catchall: merging._def.catchall,\n    //     shape: () =>\n    //       objectUtil.mergeShapes(this._def.shape(), merging._def.shape()),\n    //     typeName: ZodFirstPartyTypeKind.ZodObject,\n    //   }) as any;\n    //   return merged;\n    // }\n    setKey(key, schema) {\n        return this.augment({ [key]: schema });\n    }\n    // merge<Incoming extends AnyZodObject>(\n    //   merging: Incoming\n    // ): //ZodObject<T & Incoming[\"_shape\"], UnknownKeys, Catchall> = (merging) => {\n    // ZodObject<\n    //   extendShape<T, ReturnType<Incoming[\"_def\"][\"shape\"]>>,\n    //   Incoming[\"_def\"][\"unknownKeys\"],\n    //   Incoming[\"_def\"][\"catchall\"]\n    // > {\n    //   // const mergedShape = objectUtil.mergeShapes(\n    //   //   this._def.shape(),\n    //   //   merging._def.shape()\n    //   // );\n    //   const merged: any = new ZodObject({\n    //     unknownKeys: merging._def.unknownKeys,\n    //     catchall: merging._def.catchall,\n    //     shape: () =>\n    //       objectUtil.mergeShapes(this._def.shape(), merging._def.shape()),\n    //     typeName: ZodFirstPartyTypeKind.ZodObject,\n    //   }) as any;\n    //   return merged;\n    // }\n    catchall(index) {\n        return new ZodObject({\n            ...this._def,\n            catchall: index,\n        });\n    }\n    pick(mask) {\n        const shape = {};\n        util.objectKeys(mask).forEach((key) => {\n            if (mask[key] && this.shape[key]) {\n                shape[key] = this.shape[key];\n            }\n        });\n        return new ZodObject({\n            ...this._def,\n            shape: () => shape,\n        });\n    }\n    omit(mask) {\n        const shape = {};\n        util.objectKeys(this.shape).forEach((key) => {\n            if (!mask[key]) {\n                shape[key] = this.shape[key];\n            }\n        });\n        return new ZodObject({\n            ...this._def,\n            shape: () => shape,\n        });\n    }\n    /**\n     * @deprecated\n     */\n    deepPartial() {\n        return deepPartialify(this);\n    }\n    partial(mask) {\n        const newShape = {};\n        util.objectKeys(this.shape).forEach((key) => {\n            const fieldSchema = this.shape[key];\n            if (mask && !mask[key]) {\n                newShape[key] = fieldSchema;\n            }\n            else {\n                newShape[key] = fieldSchema.optional();\n            }\n        });\n        return new ZodObject({\n            ...this._def,\n            shape: () => newShape,\n        });\n    }\n    required(mask) {\n        const newShape = {};\n        util.objectKeys(this.shape).forEach((key) => {\n            if (mask && !mask[key]) {\n                newShape[key] = this.shape[key];\n            }\n            else {\n                const fieldSchema = this.shape[key];\n                let newField = fieldSchema;\n                while (newField instanceof ZodOptional) {\n                    newField = newField._def.innerType;\n                }\n                newShape[key] = newField;\n            }\n        });\n        return new ZodObject({\n            ...this._def,\n            shape: () => newShape,\n        });\n    }\n    keyof() {\n        return createZodEnum(util.objectKeys(this.shape));\n    }\n}\nZodObject.create = (shape, params) => {\n    return new ZodObject({\n        shape: () => shape,\n        unknownKeys: \"strip\",\n        catchall: ZodNever.create(),\n        typeName: ZodFirstPartyTypeKind.ZodObject,\n        ...processCreateParams(params),\n    });\n};\nZodObject.strictCreate = (shape, params) => {\n    return new ZodObject({\n        shape: () => shape,\n        unknownKeys: \"strict\",\n        catchall: ZodNever.create(),\n        typeName: ZodFirstPartyTypeKind.ZodObject,\n        ...processCreateParams(params),\n    });\n};\nZodObject.lazycreate = (shape, params) => {\n    return new ZodObject({\n        shape,\n        unknownKeys: \"strip\",\n        catchall: ZodNever.create(),\n        typeName: ZodFirstPartyTypeKind.ZodObject,\n        ...processCreateParams(params),\n    });\n};\nclass ZodUnion extends ZodType {\n    _parse(input) {\n        const { ctx } = this._processInputParams(input);\n        const options = this._def.options;\n        function handleResults(results) {\n            // return first issue-free validation if it exists\n            for (const result of results) {\n                if (result.result.status === \"valid\") {\n                    return result.result;\n                }\n            }\n            for (const result of results) {\n                if (result.result.status === \"dirty\") {\n                    // add issues from dirty option\n                    ctx.common.issues.push(...result.ctx.common.issues);\n                    return result.result;\n                }\n            }\n            // return invalid\n            const unionErrors = results.map((result) => new ZodError(result.ctx.common.issues));\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_union,\n                unionErrors,\n            });\n            return INVALID;\n        }\n        if (ctx.common.async) {\n            return Promise.all(options.map(async (option) => {\n                const childCtx = {\n                    ...ctx,\n                    common: {\n                        ...ctx.common,\n                        issues: [],\n                    },\n                    parent: null,\n                };\n                return {\n                    result: await option._parseAsync({\n                        data: ctx.data,\n                        path: ctx.path,\n                        parent: childCtx,\n                    }),\n                    ctx: childCtx,\n                };\n            })).then(handleResults);\n        }\n        else {\n            let dirty = undefined;\n            const issues = [];\n            for (const option of options) {\n                const childCtx = {\n                    ...ctx,\n                    common: {\n                        ...ctx.common,\n                        issues: [],\n                    },\n                    parent: null,\n                };\n                const result = option._parseSync({\n                    data: ctx.data,\n                    path: ctx.path,\n                    parent: childCtx,\n                });\n                if (result.status === \"valid\") {\n                    return result;\n                }\n                else if (result.status === \"dirty\" && !dirty) {\n                    dirty = { result, ctx: childCtx };\n                }\n                if (childCtx.common.issues.length) {\n                    issues.push(childCtx.common.issues);\n                }\n            }\n            if (dirty) {\n                ctx.common.issues.push(...dirty.ctx.common.issues);\n                return dirty.result;\n            }\n            const unionErrors = issues.map((issues) => new ZodError(issues));\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_union,\n                unionErrors,\n            });\n            return INVALID;\n        }\n    }\n    get options() {\n        return this._def.options;\n    }\n}\nZodUnion.create = (types, params) => {\n    return new ZodUnion({\n        options: types,\n        typeName: ZodFirstPartyTypeKind.ZodUnion,\n        ...processCreateParams(params),\n    });\n};\n/////////////////////////////////////////////////////\n/////////////////////////////////////////////////////\n//////////                                 //////////\n//////////      ZodDiscriminatedUnion      //////////\n//////////                                 //////////\n/////////////////////////////////////////////////////\n/////////////////////////////////////////////////////\nconst getDiscriminator = (type) => {\n    if (type instanceof ZodLazy) {\n        return getDiscriminator(type.schema);\n    }\n    else if (type instanceof ZodEffects) {\n        return getDiscriminator(type.innerType());\n    }\n    else if (type instanceof ZodLiteral) {\n        return [type.value];\n    }\n    else if (type instanceof ZodEnum) {\n        return type.options;\n    }\n    else if (type instanceof ZodNativeEnum) {\n        // eslint-disable-next-line ban/ban\n        return Object.keys(type.enum);\n    }\n    else if (type instanceof ZodDefault) {\n        return getDiscriminator(type._def.innerType);\n    }\n    else if (type instanceof ZodUndefined) {\n        return [undefined];\n    }\n    else if (type instanceof ZodNull) {\n        return [null];\n    }\n    else {\n        return null;\n    }\n};\nclass ZodDiscriminatedUnion extends ZodType {\n    _parse(input) {\n        const { ctx } = this._processInputParams(input);\n        if (ctx.parsedType !== ZodParsedType.object) {\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_type,\n                expected: ZodParsedType.object,\n                received: ctx.parsedType,\n            });\n            return INVALID;\n        }\n        const discriminator = this.discriminator;\n        const discriminatorValue = ctx.data[discriminator];\n        const option = this.optionsMap.get(discriminatorValue);\n        if (!option) {\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_union_discriminator,\n                options: Array.from(this.optionsMap.keys()),\n                path: [discriminator],\n            });\n            return INVALID;\n        }\n        if (ctx.common.async) {\n            return option._parseAsync({\n                data: ctx.data,\n                path: ctx.path,\n                parent: ctx,\n            });\n        }\n        else {\n            return option._parseSync({\n                data: ctx.data,\n                path: ctx.path,\n                parent: ctx,\n            });\n        }\n    }\n    get discriminator() {\n        return this._def.discriminator;\n    }\n    get options() {\n        return this._def.options;\n    }\n    get optionsMap() {\n        return this._def.optionsMap;\n    }\n    /**\n     * The constructor of the discriminated union schema. Its behaviour is very similar to that of the normal z.union() constructor.\n     * However, it only allows a union of objects, all of which need to share a discriminator property. This property must\n     * have a different value for each object in the union.\n     * @param discriminator the name of the discriminator property\n     * @param types an array of object schemas\n     * @param params\n     */\n    static create(discriminator, options, params) {\n        // Get all the valid discriminator values\n        const optionsMap = new Map();\n        // try {\n        for (const type of options) {\n            const discriminatorValues = getDiscriminator(type.shape[discriminator]);\n            if (!discriminatorValues) {\n                throw new Error(`A discriminator value for key \\`${discriminator}\\` could not be extracted from all schema options`);\n            }\n            for (const value of discriminatorValues) {\n                if (optionsMap.has(value)) {\n                    throw new Error(`Discriminator property ${String(discriminator)} has duplicate value ${String(value)}`);\n                }\n                optionsMap.set(value, type);\n            }\n        }\n        return new ZodDiscriminatedUnion({\n            typeName: ZodFirstPartyTypeKind.ZodDiscriminatedUnion,\n            discriminator,\n            options,\n            optionsMap,\n            ...processCreateParams(params),\n        });\n    }\n}\nfunction mergeValues(a, b) {\n    const aType = getParsedType(a);\n    const bType = getParsedType(b);\n    if (a === b) {\n        return { valid: true, data: a };\n    }\n    else if (aType === ZodParsedType.object && bType === ZodParsedType.object) {\n        const bKeys = util.objectKeys(b);\n        const sharedKeys = util\n            .objectKeys(a)\n            .filter((key) => bKeys.indexOf(key) !== -1);\n        const newObj = { ...a, ...b };\n        for (const key of sharedKeys) {\n            const sharedValue = mergeValues(a[key], b[key]);\n            if (!sharedValue.valid) {\n                return { valid: false };\n            }\n            newObj[key] = sharedValue.data;\n        }\n        return { valid: true, data: newObj };\n    }\n    else if (aType === ZodParsedType.array && bType === ZodParsedType.array) {\n        if (a.length !== b.length) {\n            return { valid: false };\n        }\n        const newArray = [];\n        for (let index = 0; index < a.length; index++) {\n            const itemA = a[index];\n            const itemB = b[index];\n            const sharedValue = mergeValues(itemA, itemB);\n            if (!sharedValue.valid) {\n                return { valid: false };\n            }\n            newArray.push(sharedValue.data);\n        }\n        return { valid: true, data: newArray };\n    }\n    else if (aType === ZodParsedType.date &&\n        bType === ZodParsedType.date &&\n        +a === +b) {\n        return { valid: true, data: a };\n    }\n    else {\n        return { valid: false };\n    }\n}\nclass ZodIntersection extends ZodType {\n    _parse(input) {\n        const { status, ctx } = this._processInputParams(input);\n        const handleParsed = (parsedLeft, parsedRight) => {\n            if (isAborted(parsedLeft) || isAborted(parsedRight)) {\n                return INVALID;\n            }\n            const merged = mergeValues(parsedLeft.value, parsedRight.value);\n            if (!merged.valid) {\n                addIssueToContext(ctx, {\n                    code: ZodIssueCode.invalid_intersection_types,\n                });\n                return INVALID;\n            }\n            if (isDirty(parsedLeft) || isDirty(parsedRight)) {\n                status.dirty();\n            }\n            return { status: status.value, value: merged.data };\n        };\n        if (ctx.common.async) {\n            return Promise.all([\n                this._def.left._parseAsync({\n                    data: ctx.data,\n                    path: ctx.path,\n                    parent: ctx,\n                }),\n                this._def.right._parseAsync({\n                    data: ctx.data,\n                    path: ctx.path,\n                    parent: ctx,\n                }),\n            ]).then(([left, right]) => handleParsed(left, right));\n        }\n        else {\n            return handleParsed(this._def.left._parseSync({\n                data: ctx.data,\n                path: ctx.path,\n                parent: ctx,\n            }), this._def.right._parseSync({\n                data: ctx.data,\n                path: ctx.path,\n                parent: ctx,\n            }));\n        }\n    }\n}\nZodIntersection.create = (left, right, params) => {\n    return new ZodIntersection({\n        left: left,\n        right: right,\n        typeName: ZodFirstPartyTypeKind.ZodIntersection,\n        ...processCreateParams(params),\n    });\n};\nclass ZodTuple extends ZodType {\n    _parse(input) {\n        const { status, ctx } = this._processInputParams(input);\n        if (ctx.parsedType !== ZodParsedType.array) {\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_type,\n                expected: ZodParsedType.array,\n                received: ctx.parsedType,\n            });\n            return INVALID;\n        }\n        if (ctx.data.length < this._def.items.length) {\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.too_small,\n                minimum: this._def.items.length,\n                inclusive: true,\n                exact: false,\n                type: \"array\",\n            });\n            return INVALID;\n        }\n        const rest = this._def.rest;\n        if (!rest && ctx.data.length > this._def.items.length) {\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.too_big,\n                maximum: this._def.items.length,\n                inclusive: true,\n                exact: false,\n                type: \"array\",\n            });\n            status.dirty();\n        }\n        const items = [...ctx.data]\n            .map((item, itemIndex) => {\n            const schema = this._def.items[itemIndex] || this._def.rest;\n            if (!schema)\n                return null;\n            return schema._parse(new ParseInputLazyPath(ctx, item, ctx.path, itemIndex));\n        })\n            .filter((x) => !!x); // filter nulls\n        if (ctx.common.async) {\n            return Promise.all(items).then((results) => {\n                return ParseStatus.mergeArray(status, results);\n            });\n        }\n        else {\n            return ParseStatus.mergeArray(status, items);\n        }\n    }\n    get items() {\n        return this._def.items;\n    }\n    rest(rest) {\n        return new ZodTuple({\n            ...this._def,\n            rest,\n        });\n    }\n}\nZodTuple.create = (schemas, params) => {\n    if (!Array.isArray(schemas)) {\n        throw new Error(\"You must pass an array of schemas to z.tuple([ ... ])\");\n    }\n    return new ZodTuple({\n        items: schemas,\n        typeName: ZodFirstPartyTypeKind.ZodTuple,\n        rest: null,\n        ...processCreateParams(params),\n    });\n};\nclass ZodRecord extends ZodType {\n    get keySchema() {\n        return this._def.keyType;\n    }\n    get valueSchema() {\n        return this._def.valueType;\n    }\n    _parse(input) {\n        const { status, ctx } = this._processInputParams(input);\n        if (ctx.parsedType !== ZodParsedType.object) {\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_type,\n                expected: ZodParsedType.object,\n                received: ctx.parsedType,\n            });\n            return INVALID;\n        }\n        const pairs = [];\n        const keyType = this._def.keyType;\n        const valueType = this._def.valueType;\n        for (const key in ctx.data) {\n            pairs.push({\n                key: keyType._parse(new ParseInputLazyPath(ctx, key, ctx.path, key)),\n                value: valueType._parse(new ParseInputLazyPath(ctx, ctx.data[key], ctx.path, key)),\n            });\n        }\n        if (ctx.common.async) {\n            return ParseStatus.mergeObjectAsync(status, pairs);\n        }\n        else {\n            return ParseStatus.mergeObjectSync(status, pairs);\n        }\n    }\n    get element() {\n        return this._def.valueType;\n    }\n    static create(first, second, third) {\n        if (second instanceof ZodType) {\n            return new ZodRecord({\n                keyType: first,\n                valueType: second,\n                typeName: ZodFirstPartyTypeKind.ZodRecord,\n                ...processCreateParams(third),\n            });\n        }\n        return new ZodRecord({\n            keyType: ZodString.create(),\n            valueType: first,\n            typeName: ZodFirstPartyTypeKind.ZodRecord,\n            ...processCreateParams(second),\n        });\n    }\n}\nclass ZodMap extends ZodType {\n    _parse(input) {\n        const { status, ctx } = this._processInputParams(input);\n        if (ctx.parsedType !== ZodParsedType.map) {\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_type,\n                expected: ZodParsedType.map,\n                received: ctx.parsedType,\n            });\n            return INVALID;\n        }\n        const keyType = this._def.keyType;\n        const valueType = this._def.valueType;\n        const pairs = [...ctx.data.entries()].map(([key, value], index) => {\n            return {\n                key: keyType._parse(new ParseInputLazyPath(ctx, key, ctx.path, [index, \"key\"])),\n                value: valueType._parse(new ParseInputLazyPath(ctx, value, ctx.path, [index, \"value\"])),\n            };\n        });\n        if (ctx.common.async) {\n            const finalMap = new Map();\n            return Promise.resolve().then(async () => {\n                for (const pair of pairs) {\n                    const key = await pair.key;\n                    const value = await pair.value;\n                    if (key.status === \"aborted\" || value.status === \"aborted\") {\n                        return INVALID;\n                    }\n                    if (key.status === \"dirty\" || value.status === \"dirty\") {\n                        status.dirty();\n                    }\n                    finalMap.set(key.value, value.value);\n                }\n                return { status: status.value, value: finalMap };\n            });\n        }\n        else {\n            const finalMap = new Map();\n            for (const pair of pairs) {\n                const key = pair.key;\n                const value = pair.value;\n                if (key.status === \"aborted\" || value.status === \"aborted\") {\n                    return INVALID;\n                }\n                if (key.status === \"dirty\" || value.status === \"dirty\") {\n                    status.dirty();\n                }\n                finalMap.set(key.value, value.value);\n            }\n            return { status: status.value, value: finalMap };\n        }\n    }\n}\nZodMap.create = (keyType, valueType, params) => {\n    return new ZodMap({\n        valueType,\n        keyType,\n        typeName: ZodFirstPartyTypeKind.ZodMap,\n        ...processCreateParams(params),\n    });\n};\nclass ZodSet extends ZodType {\n    _parse(input) {\n        const { status, ctx } = this._processInputParams(input);\n        if (ctx.parsedType !== ZodParsedType.set) {\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_type,\n                expected: ZodParsedType.set,\n                received: ctx.parsedType,\n            });\n            return INVALID;\n        }\n        const def = this._def;\n        if (def.minSize !== null) {\n            if (ctx.data.size < def.minSize.value) {\n                addIssueToContext(ctx, {\n                    code: ZodIssueCode.too_small,\n                    minimum: def.minSize.value,\n                    type: \"set\",\n                    inclusive: true,\n                    exact: false,\n                    message: def.minSize.message,\n                });\n                status.dirty();\n            }\n        }\n        if (def.maxSize !== null) {\n            if (ctx.data.size > def.maxSize.value) {\n                addIssueToContext(ctx, {\n                    code: ZodIssueCode.too_big,\n                    maximum: def.maxSize.value,\n                    type: \"set\",\n                    inclusive: true,\n                    exact: false,\n                    message: def.maxSize.message,\n                });\n                status.dirty();\n            }\n        }\n        const valueType = this._def.valueType;\n        function finalizeSet(elements) {\n            const parsedSet = new Set();\n            for (const element of elements) {\n                if (element.status === \"aborted\")\n                    return INVALID;\n                if (element.status === \"dirty\")\n                    status.dirty();\n                parsedSet.add(element.value);\n            }\n            return { status: status.value, value: parsedSet };\n        }\n        const elements = [...ctx.data.values()].map((item, i) => valueType._parse(new ParseInputLazyPath(ctx, item, ctx.path, i)));\n        if (ctx.common.async) {\n            return Promise.all(elements).then((elements) => finalizeSet(elements));\n        }\n        else {\n            return finalizeSet(elements);\n        }\n    }\n    min(minSize, message) {\n        return new ZodSet({\n            ...this._def,\n            minSize: { value: minSize, message: errorUtil.toString(message) },\n        });\n    }\n    max(maxSize, message) {\n        return new ZodSet({\n            ...this._def,\n            maxSize: { value: maxSize, message: errorUtil.toString(message) },\n        });\n    }\n    size(size, message) {\n        return this.min(size, message).max(size, message);\n    }\n    nonempty(message) {\n        return this.min(1, message);\n    }\n}\nZodSet.create = (valueType, params) => {\n    return new ZodSet({\n        valueType,\n        minSize: null,\n        maxSize: null,\n        typeName: ZodFirstPartyTypeKind.ZodSet,\n        ...processCreateParams(params),\n    });\n};\nclass ZodFunction extends ZodType {\n    constructor() {\n        super(...arguments);\n        this.validate = this.implement;\n    }\n    _parse(input) {\n        const { ctx } = this._processInputParams(input);\n        if (ctx.parsedType !== ZodParsedType.function) {\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_type,\n                expected: ZodParsedType.function,\n                received: ctx.parsedType,\n            });\n            return INVALID;\n        }\n        function makeArgsIssue(args, error) {\n            return makeIssue({\n                data: args,\n                path: ctx.path,\n                errorMaps: [\n                    ctx.common.contextualErrorMap,\n                    ctx.schemaErrorMap,\n                    getErrorMap(),\n                    errorMap,\n                ].filter((x) => !!x),\n                issueData: {\n                    code: ZodIssueCode.invalid_arguments,\n                    argumentsError: error,\n                },\n            });\n        }\n        function makeReturnsIssue(returns, error) {\n            return makeIssue({\n                data: returns,\n                path: ctx.path,\n                errorMaps: [\n                    ctx.common.contextualErrorMap,\n                    ctx.schemaErrorMap,\n                    getErrorMap(),\n                    errorMap,\n                ].filter((x) => !!x),\n                issueData: {\n                    code: ZodIssueCode.invalid_return_type,\n                    returnTypeError: error,\n                },\n            });\n        }\n        const params = { errorMap: ctx.common.contextualErrorMap };\n        const fn = ctx.data;\n        if (this._def.returns instanceof ZodPromise) {\n            return OK(async (...args) => {\n                const error = new ZodError([]);\n                const parsedArgs = await this._def.args\n                    .parseAsync(args, params)\n                    .catch((e) => {\n                    error.addIssue(makeArgsIssue(args, e));\n                    throw error;\n                });\n                const result = await fn(...parsedArgs);\n                const parsedReturns = await this._def.returns._def.type\n                    .parseAsync(result, params)\n                    .catch((e) => {\n                    error.addIssue(makeReturnsIssue(result, e));\n                    throw error;\n                });\n                return parsedReturns;\n            });\n        }\n        else {\n            return OK((...args) => {\n                const parsedArgs = this._def.args.safeParse(args, params);\n                if (!parsedArgs.success) {\n                    throw new ZodError([makeArgsIssue(args, parsedArgs.error)]);\n                }\n                const result = fn(...parsedArgs.data);\n                const parsedReturns = this._def.returns.safeParse(result, params);\n                if (!parsedReturns.success) {\n                    throw new ZodError([makeReturnsIssue(result, parsedReturns.error)]);\n                }\n                return parsedReturns.data;\n            });\n        }\n    }\n    parameters() {\n        return this._def.args;\n    }\n    returnType() {\n        return this._def.returns;\n    }\n    args(...items) {\n        return new ZodFunction({\n            ...this._def,\n            args: ZodTuple.create(items).rest(ZodUnknown.create()),\n        });\n    }\n    returns(returnType) {\n        return new ZodFunction({\n            ...this._def,\n            returns: returnType,\n        });\n    }\n    implement(func) {\n        const validatedFunc = this.parse(func);\n        return validatedFunc;\n    }\n    strictImplement(func) {\n        const validatedFunc = this.parse(func);\n        return validatedFunc;\n    }\n    static create(args, returns, params) {\n        return new ZodFunction({\n            args: (args\n                ? args\n                : ZodTuple.create([]).rest(ZodUnknown.create())),\n            returns: returns || ZodUnknown.create(),\n            typeName: ZodFirstPartyTypeKind.ZodFunction,\n            ...processCreateParams(params),\n        });\n    }\n}\nclass ZodLazy extends ZodType {\n    get schema() {\n        return this._def.getter();\n    }\n    _parse(input) {\n        const { ctx } = this._processInputParams(input);\n        const lazySchema = this._def.getter();\n        return lazySchema._parse({ data: ctx.data, path: ctx.path, parent: ctx });\n    }\n}\nZodLazy.create = (getter, params) => {\n    return new ZodLazy({\n        getter: getter,\n        typeName: ZodFirstPartyTypeKind.ZodLazy,\n        ...processCreateParams(params),\n    });\n};\nclass ZodLiteral extends ZodType {\n    _parse(input) {\n        if (input.data !== this._def.value) {\n            const ctx = this._getOrReturnCtx(input);\n            addIssueToContext(ctx, {\n                received: ctx.data,\n                code: ZodIssueCode.invalid_literal,\n                expected: this._def.value,\n            });\n            return INVALID;\n        }\n        return { status: \"valid\", value: input.data };\n    }\n    get value() {\n        return this._def.value;\n    }\n}\nZodLiteral.create = (value, params) => {\n    return new ZodLiteral({\n        value: value,\n        typeName: ZodFirstPartyTypeKind.ZodLiteral,\n        ...processCreateParams(params),\n    });\n};\nfunction createZodEnum(values, params) {\n    return new ZodEnum({\n        values: values,\n        typeName: ZodFirstPartyTypeKind.ZodEnum,\n        ...processCreateParams(params),\n    });\n}\nclass ZodEnum extends ZodType {\n    _parse(input) {\n        if (typeof input.data !== \"string\") {\n            const ctx = this._getOrReturnCtx(input);\n            const expectedValues = this._def.values;\n            addIssueToContext(ctx, {\n                expected: util.joinValues(expectedValues),\n                received: ctx.parsedType,\n                code: ZodIssueCode.invalid_type,\n            });\n            return INVALID;\n        }\n        if (this._def.values.indexOf(input.data) === -1) {\n            const ctx = this._getOrReturnCtx(input);\n            const expectedValues = this._def.values;\n            addIssueToContext(ctx, {\n                received: ctx.data,\n                code: ZodIssueCode.invalid_enum_value,\n                options: expectedValues,\n            });\n            return INVALID;\n        }\n        return OK(input.data);\n    }\n    get options() {\n        return this._def.values;\n    }\n    get enum() {\n        const enumValues = {};\n        for (const val of this._def.values) {\n            enumValues[val] = val;\n        }\n        return enumValues;\n    }\n    get Values() {\n        const enumValues = {};\n        for (const val of this._def.values) {\n            enumValues[val] = val;\n        }\n        return enumValues;\n    }\n    get Enum() {\n        const enumValues = {};\n        for (const val of this._def.values) {\n            enumValues[val] = val;\n        }\n        return enumValues;\n    }\n    extract(values) {\n        return ZodEnum.create(values);\n    }\n    exclude(values) {\n        return ZodEnum.create(this.options.filter((opt) => !values.includes(opt)));\n    }\n}\nZodEnum.create = createZodEnum;\nclass ZodNativeEnum extends ZodType {\n    _parse(input) {\n        const nativeEnumValues = util.getValidEnumValues(this._def.values);\n        const ctx = this._getOrReturnCtx(input);\n        if (ctx.parsedType !== ZodParsedType.string &&\n            ctx.parsedType !== ZodParsedType.number) {\n            const expectedValues = util.objectValues(nativeEnumValues);\n            addIssueToContext(ctx, {\n                expected: util.joinValues(expectedValues),\n                received: ctx.parsedType,\n                code: ZodIssueCode.invalid_type,\n            });\n            return INVALID;\n        }\n        if (nativeEnumValues.indexOf(input.data) === -1) {\n            const expectedValues = util.objectValues(nativeEnumValues);\n            addIssueToContext(ctx, {\n                received: ctx.data,\n                code: ZodIssueCode.invalid_enum_value,\n                options: expectedValues,\n            });\n            return INVALID;\n        }\n        return OK(input.data);\n    }\n    get enum() {\n        return this._def.values;\n    }\n}\nZodNativeEnum.create = (values, params) => {\n    return new ZodNativeEnum({\n        values: values,\n        typeName: ZodFirstPartyTypeKind.ZodNativeEnum,\n        ...processCreateParams(params),\n    });\n};\nclass ZodPromise extends ZodType {\n    unwrap() {\n        return this._def.type;\n    }\n    _parse(input) {\n        const { ctx } = this._processInputParams(input);\n        if (ctx.parsedType !== ZodParsedType.promise &&\n            ctx.common.async === false) {\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_type,\n                expected: ZodParsedType.promise,\n                received: ctx.parsedType,\n            });\n            return INVALID;\n        }\n        const promisified = ctx.parsedType === ZodParsedType.promise\n            ? ctx.data\n            : Promise.resolve(ctx.data);\n        return OK(promisified.then((data) => {\n            return this._def.type.parseAsync(data, {\n                path: ctx.path,\n                errorMap: ctx.common.contextualErrorMap,\n            });\n        }));\n    }\n}\nZodPromise.create = (schema, params) => {\n    return new ZodPromise({\n        type: schema,\n        typeName: ZodFirstPartyTypeKind.ZodPromise,\n        ...processCreateParams(params),\n    });\n};\nclass ZodEffects extends ZodType {\n    innerType() {\n        return this._def.schema;\n    }\n    sourceType() {\n        return this._def.schema._def.typeName === ZodFirstPartyTypeKind.ZodEffects\n            ? this._def.schema.sourceType()\n            : this._def.schema;\n    }\n    _parse(input) {\n        const { status, ctx } = this._processInputParams(input);\n        const effect = this._def.effect || null;\n        if (effect.type === \"preprocess\") {\n            const processed = effect.transform(ctx.data);\n            if (ctx.common.async) {\n                return Promise.resolve(processed).then((processed) => {\n                    return this._def.schema._parseAsync({\n                        data: processed,\n                        path: ctx.path,\n                        parent: ctx,\n                    });\n                });\n            }\n            else {\n                return this._def.schema._parseSync({\n                    data: processed,\n                    path: ctx.path,\n                    parent: ctx,\n                });\n            }\n        }\n        const checkCtx = {\n            addIssue: (arg) => {\n                addIssueToContext(ctx, arg);\n                if (arg.fatal) {\n                    status.abort();\n                }\n                else {\n                    status.dirty();\n                }\n            },\n            get path() {\n                return ctx.path;\n            },\n        };\n        checkCtx.addIssue = checkCtx.addIssue.bind(checkCtx);\n        if (effect.type === \"refinement\") {\n            const executeRefinement = (acc\n            // effect: RefinementEffect<any>\n            ) => {\n                const result = effect.refinement(acc, checkCtx);\n                if (ctx.common.async) {\n                    return Promise.resolve(result);\n                }\n                if (result instanceof Promise) {\n                    throw new Error(\"Async refinement encountered during synchronous parse operation. Use .parseAsync instead.\");\n                }\n                return acc;\n            };\n            if (ctx.common.async === false) {\n                const inner = this._def.schema._parseSync({\n                    data: ctx.data,\n                    path: ctx.path,\n                    parent: ctx,\n                });\n                if (inner.status === \"aborted\")\n                    return INVALID;\n                if (inner.status === \"dirty\")\n                    status.dirty();\n                // return value is ignored\n                executeRefinement(inner.value);\n                return { status: status.value, value: inner.value };\n            }\n            else {\n                return this._def.schema\n                    ._parseAsync({ data: ctx.data, path: ctx.path, parent: ctx })\n                    .then((inner) => {\n                    if (inner.status === \"aborted\")\n                        return INVALID;\n                    if (inner.status === \"dirty\")\n                        status.dirty();\n                    return executeRefinement(inner.value).then(() => {\n                        return { status: status.value, value: inner.value };\n                    });\n                });\n            }\n        }\n        if (effect.type === \"transform\") {\n            if (ctx.common.async === false) {\n                const base = this._def.schema._parseSync({\n                    data: ctx.data,\n                    path: ctx.path,\n                    parent: ctx,\n                });\n                if (!isValid(base))\n                    return base;\n                const result = effect.transform(base.value, checkCtx);\n                if (result instanceof Promise) {\n                    throw new Error(`Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.`);\n                }\n                return { status: status.value, value: result };\n            }\n            else {\n                return this._def.schema\n                    ._parseAsync({ data: ctx.data, path: ctx.path, parent: ctx })\n                    .then((base) => {\n                    if (!isValid(base))\n                        return base;\n                    return Promise.resolve(effect.transform(base.value, checkCtx)).then((result) => ({ status: status.value, value: result }));\n                });\n            }\n        }\n        util.assertNever(effect);\n    }\n}\nZodEffects.create = (schema, effect, params) => {\n    return new ZodEffects({\n        schema,\n        typeName: ZodFirstPartyTypeKind.ZodEffects,\n        effect,\n        ...processCreateParams(params),\n    });\n};\nZodEffects.createWithPreprocess = (preprocess, schema, params) => {\n    return new ZodEffects({\n        schema,\n        effect: { type: \"preprocess\", transform: preprocess },\n        typeName: ZodFirstPartyTypeKind.ZodEffects,\n        ...processCreateParams(params),\n    });\n};\nclass ZodOptional extends ZodType {\n    _parse(input) {\n        const parsedType = this._getType(input);\n        if (parsedType === ZodParsedType.undefined) {\n            return OK(undefined);\n        }\n        return this._def.innerType._parse(input);\n    }\n    unwrap() {\n        return this._def.innerType;\n    }\n}\nZodOptional.create = (type, params) => {\n    return new ZodOptional({\n        innerType: type,\n        typeName: ZodFirstPartyTypeKind.ZodOptional,\n        ...processCreateParams(params),\n    });\n};\nclass ZodNullable extends ZodType {\n    _parse(input) {\n        const parsedType = this._getType(input);\n        if (parsedType === ZodParsedType.null) {\n            return OK(null);\n        }\n        return this._def.innerType._parse(input);\n    }\n    unwrap() {\n        return this._def.innerType;\n    }\n}\nZodNullable.create = (type, params) => {\n    return new ZodNullable({\n        innerType: type,\n        typeName: ZodFirstPartyTypeKind.ZodNullable,\n        ...processCreateParams(params),\n    });\n};\nclass ZodDefault extends ZodType {\n    _parse(input) {\n        const { ctx } = this._processInputParams(input);\n        let data = ctx.data;\n        if (ctx.parsedType === ZodParsedType.undefined) {\n            data = this._def.defaultValue();\n        }\n        return this._def.innerType._parse({\n            data,\n            path: ctx.path,\n            parent: ctx,\n        });\n    }\n    removeDefault() {\n        return this._def.innerType;\n    }\n}\nZodDefault.create = (type, params) => {\n    return new ZodDefault({\n        innerType: type,\n        typeName: ZodFirstPartyTypeKind.ZodDefault,\n        defaultValue: typeof params.default === \"function\"\n            ? params.default\n            : () => params.default,\n        ...processCreateParams(params),\n    });\n};\nclass ZodCatch extends ZodType {\n    _parse(input) {\n        const { ctx } = this._processInputParams(input);\n        // newCtx is used to not collect issues from inner types in ctx\n        const newCtx = {\n            ...ctx,\n            common: {\n                ...ctx.common,\n                issues: [],\n            },\n        };\n        const result = this._def.innerType._parse({\n            data: newCtx.data,\n            path: newCtx.path,\n            parent: {\n                ...newCtx,\n            },\n        });\n        if (isAsync(result)) {\n            return result.then((result) => {\n                return {\n                    status: \"valid\",\n                    value: result.status === \"valid\"\n                        ? result.value\n                        : this._def.catchValue({\n                            get error() {\n                                return new ZodError(newCtx.common.issues);\n                            },\n                            input: newCtx.data,\n                        }),\n                };\n            });\n        }\n        else {\n            return {\n                status: \"valid\",\n                value: result.status === \"valid\"\n                    ? result.value\n                    : this._def.catchValue({\n                        get error() {\n                            return new ZodError(newCtx.common.issues);\n                        },\n                        input: newCtx.data,\n                    }),\n            };\n        }\n    }\n    removeCatch() {\n        return this._def.innerType;\n    }\n}\nZodCatch.create = (type, params) => {\n    return new ZodCatch({\n        innerType: type,\n        typeName: ZodFirstPartyTypeKind.ZodCatch,\n        catchValue: typeof params.catch === \"function\" ? params.catch : () => params.catch,\n        ...processCreateParams(params),\n    });\n};\nclass ZodNaN extends ZodType {\n    _parse(input) {\n        const parsedType = this._getType(input);\n        if (parsedType !== ZodParsedType.nan) {\n            const ctx = this._getOrReturnCtx(input);\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_type,\n                expected: ZodParsedType.nan,\n                received: ctx.parsedType,\n            });\n            return INVALID;\n        }\n        return { status: \"valid\", value: input.data };\n    }\n}\nZodNaN.create = (params) => {\n    return new ZodNaN({\n        typeName: ZodFirstPartyTypeKind.ZodNaN,\n        ...processCreateParams(params),\n    });\n};\nconst BRAND = Symbol(\"zod_brand\");\nclass ZodBranded extends ZodType {\n    _parse(input) {\n        const { ctx } = this._processInputParams(input);\n        const data = ctx.data;\n        return this._def.type._parse({\n            data,\n            path: ctx.path,\n            parent: ctx,\n        });\n    }\n    unwrap() {\n        return this._def.type;\n    }\n}\nclass ZodPipeline extends ZodType {\n    _parse(input) {\n        const { status, ctx } = this._processInputParams(input);\n        if (ctx.common.async) {\n            const handleAsync = async () => {\n                const inResult = await this._def.in._parseAsync({\n                    data: ctx.data,\n                    path: ctx.path,\n                    parent: ctx,\n                });\n                if (inResult.status === \"aborted\")\n                    return INVALID;\n                if (inResult.status === \"dirty\") {\n                    status.dirty();\n                    return DIRTY(inResult.value);\n                }\n                else {\n                    return this._def.out._parseAsync({\n                        data: inResult.value,\n                        path: ctx.path,\n                        parent: ctx,\n                    });\n                }\n            };\n            return handleAsync();\n        }\n        else {\n            const inResult = this._def.in._parseSync({\n                data: ctx.data,\n                path: ctx.path,\n                parent: ctx,\n            });\n            if (inResult.status === \"aborted\")\n                return INVALID;\n            if (inResult.status === \"dirty\") {\n                status.dirty();\n                return {\n                    status: \"dirty\",\n                    value: inResult.value,\n                };\n            }\n            else {\n                return this._def.out._parseSync({\n                    data: inResult.value,\n                    path: ctx.path,\n                    parent: ctx,\n                });\n            }\n        }\n    }\n    static create(a, b) {\n        return new ZodPipeline({\n            in: a,\n            out: b,\n            typeName: ZodFirstPartyTypeKind.ZodPipeline,\n        });\n    }\n}\nconst custom = (check, params = {}, \n/*\n * @deprecated\n *\n * Pass `fatal` into the params object instead:\n *\n * ```ts\n * z.string().custom((val) => val.length > 5, { fatal: false })\n * ```\n *\n */\nfatal) => {\n    if (check)\n        return ZodAny.create().superRefine((data, ctx) => {\n            var _a, _b;\n            if (!check(data)) {\n                const p = typeof params === \"function\"\n                    ? params(data)\n                    : typeof params === \"string\"\n                        ? { message: params }\n                        : params;\n                const _fatal = (_b = (_a = p.fatal) !== null && _a !== void 0 ? _a : fatal) !== null && _b !== void 0 ? _b : true;\n                const p2 = typeof p === \"string\" ? { message: p } : p;\n                ctx.addIssue({ code: \"custom\", ...p2, fatal: _fatal });\n            }\n        });\n    return ZodAny.create();\n};\nconst late = {\n    object: ZodObject.lazycreate,\n};\nvar ZodFirstPartyTypeKind;\n(function (ZodFirstPartyTypeKind) {\n    ZodFirstPartyTypeKind[\"ZodString\"] = \"ZodString\";\n    ZodFirstPartyTypeKind[\"ZodNumber\"] = \"ZodNumber\";\n    ZodFirstPartyTypeKind[\"ZodNaN\"] = \"ZodNaN\";\n    ZodFirstPartyTypeKind[\"ZodBigInt\"] = \"ZodBigInt\";\n    ZodFirstPartyTypeKind[\"ZodBoolean\"] = \"ZodBoolean\";\n    ZodFirstPartyTypeKind[\"ZodDate\"] = \"ZodDate\";\n    ZodFirstPartyTypeKind[\"ZodSymbol\"] = \"ZodSymbol\";\n    ZodFirstPartyTypeKind[\"ZodUndefined\"] = \"ZodUndefined\";\n    ZodFirstPartyTypeKind[\"ZodNull\"] = \"ZodNull\";\n    ZodFirstPartyTypeKind[\"ZodAny\"] = \"ZodAny\";\n    ZodFirstPartyTypeKind[\"ZodUnknown\"] = \"ZodUnknown\";\n    ZodFirstPartyTypeKind[\"ZodNever\"] = \"ZodNever\";\n    ZodFirstPartyTypeKind[\"ZodVoid\"] = \"ZodVoid\";\n    ZodFirstPartyTypeKind[\"ZodArray\"] = \"ZodArray\";\n    ZodFirstPartyTypeKind[\"ZodObject\"] = \"ZodObject\";\n    ZodFirstPartyTypeKind[\"ZodUnion\"] = \"ZodUnion\";\n    ZodFirstPartyTypeKind[\"ZodDiscriminatedUnion\"] = \"ZodDiscriminatedUnion\";\n    ZodFirstPartyTypeKind[\"ZodIntersection\"] = \"ZodIntersection\";\n    ZodFirstPartyTypeKind[\"ZodTuple\"] = \"ZodTuple\";\n    ZodFirstPartyTypeKind[\"ZodRecord\"] = \"ZodRecord\";\n    ZodFirstPartyTypeKind[\"ZodMap\"] = \"ZodMap\";\n    ZodFirstPartyTypeKind[\"ZodSet\"] = \"ZodSet\";\n    ZodFirstPartyTypeKind[\"ZodFunction\"] = \"ZodFunction\";\n    ZodFirstPartyTypeKind[\"ZodLazy\"] = \"ZodLazy\";\n    ZodFirstPartyTypeKind[\"ZodLiteral\"] = \"ZodLiteral\";\n    ZodFirstPartyTypeKind[\"ZodEnum\"] = \"ZodEnum\";\n    ZodFirstPartyTypeKind[\"ZodEffects\"] = \"ZodEffects\";\n    ZodFirstPartyTypeKind[\"ZodNativeEnum\"] = \"ZodNativeEnum\";\n    ZodFirstPartyTypeKind[\"ZodOptional\"] = \"ZodOptional\";\n    ZodFirstPartyTypeKind[\"ZodNullable\"] = \"ZodNullable\";\n    ZodFirstPartyTypeKind[\"ZodDefault\"] = \"ZodDefault\";\n    ZodFirstPartyTypeKind[\"ZodCatch\"] = \"ZodCatch\";\n    ZodFirstPartyTypeKind[\"ZodPromise\"] = \"ZodPromise\";\n    ZodFirstPartyTypeKind[\"ZodBranded\"] = \"ZodBranded\";\n    ZodFirstPartyTypeKind[\"ZodPipeline\"] = \"ZodPipeline\";\n})(ZodFirstPartyTypeKind || (ZodFirstPartyTypeKind = {}));\nconst instanceOfType = (\n// const instanceOfType = <T extends new (...args: any[]) => any>(\ncls, params = {\n    message: `Input not instance of ${cls.name}`,\n}) => custom((data) => data instanceof cls, params);\nconst stringType = ZodString.create;\nconst numberType = ZodNumber.create;\nconst nanType = ZodNaN.create;\nconst bigIntType = ZodBigInt.create;\nconst booleanType = ZodBoolean.create;\nconst dateType = ZodDate.create;\nconst symbolType = ZodSymbol.create;\nconst undefinedType = ZodUndefined.create;\nconst nullType = ZodNull.create;\nconst anyType = ZodAny.create;\nconst unknownType = ZodUnknown.create;\nconst neverType = ZodNever.create;\nconst voidType = ZodVoid.create;\nconst arrayType = ZodArray.create;\nconst objectType = ZodObject.create;\nconst strictObjectType = ZodObject.strictCreate;\nconst unionType = ZodUnion.create;\nconst discriminatedUnionType = ZodDiscriminatedUnion.create;\nconst intersectionType = ZodIntersection.create;\nconst tupleType = ZodTuple.create;\nconst recordType = ZodRecord.create;\nconst mapType = ZodMap.create;\nconst setType = ZodSet.create;\nconst functionType = ZodFunction.create;\nconst lazyType = ZodLazy.create;\nconst literalType = ZodLiteral.create;\nconst enumType = ZodEnum.create;\nconst nativeEnumType = ZodNativeEnum.create;\nconst promiseType = ZodPromise.create;\nconst effectsType = ZodEffects.create;\nconst optionalType = ZodOptional.create;\nconst nullableType = ZodNullable.create;\nconst preprocessType = ZodEffects.createWithPreprocess;\nconst pipelineType = ZodPipeline.create;\nconst ostring = () => stringType().optional();\nconst onumber = () => numberType().optional();\nconst oboolean = () => booleanType().optional();\nconst coerce = {\n    string: ((arg) => ZodString.create({ ...arg, coerce: true })),\n    number: ((arg) => ZodNumber.create({ ...arg, coerce: true })),\n    boolean: ((arg) => ZodBoolean.create({\n        ...arg,\n        coerce: true,\n    })),\n    bigint: ((arg) => ZodBigInt.create({ ...arg, coerce: true })),\n    date: ((arg) => ZodDate.create({ ...arg, coerce: true })),\n};\nconst NEVER = INVALID;\n\nvar z = /*#__PURE__*/Object.freeze({\n    __proto__: null,\n    defaultErrorMap: errorMap,\n    setErrorMap: setErrorMap,\n    getErrorMap: getErrorMap,\n    makeIssue: makeIssue,\n    EMPTY_PATH: EMPTY_PATH,\n    addIssueToContext: addIssueToContext,\n    ParseStatus: ParseStatus,\n    INVALID: INVALID,\n    DIRTY: DIRTY,\n    OK: OK,\n    isAborted: isAborted,\n    isDirty: isDirty,\n    isValid: isValid,\n    isAsync: isAsync,\n    get util () { return util; },\n    get objectUtil () { return objectUtil; },\n    ZodParsedType: ZodParsedType,\n    getParsedType: getParsedType,\n    ZodType: ZodType,\n    ZodString: ZodString,\n    ZodNumber: ZodNumber,\n    ZodBigInt: ZodBigInt,\n    ZodBoolean: ZodBoolean,\n    ZodDate: ZodDate,\n    ZodSymbol: ZodSymbol,\n    ZodUndefined: ZodUndefined,\n    ZodNull: ZodNull,\n    ZodAny: ZodAny,\n    ZodUnknown: ZodUnknown,\n    ZodNever: ZodNever,\n    ZodVoid: ZodVoid,\n    ZodArray: ZodArray,\n    ZodObject: ZodObject,\n    ZodUnion: ZodUnion,\n    ZodDiscriminatedUnion: ZodDiscriminatedUnion,\n    ZodIntersection: ZodIntersection,\n    ZodTuple: ZodTuple,\n    ZodRecord: ZodRecord,\n    ZodMap: ZodMap,\n    ZodSet: ZodSet,\n    ZodFunction: ZodFunction,\n    ZodLazy: ZodLazy,\n    ZodLiteral: ZodLiteral,\n    ZodEnum: ZodEnum,\n    ZodNativeEnum: ZodNativeEnum,\n    ZodPromise: ZodPromise,\n    ZodEffects: ZodEffects,\n    ZodTransformer: ZodEffects,\n    ZodOptional: ZodOptional,\n    ZodNullable: ZodNullable,\n    ZodDefault: ZodDefault,\n    ZodCatch: ZodCatch,\n    ZodNaN: ZodNaN,\n    BRAND: BRAND,\n    ZodBranded: ZodBranded,\n    ZodPipeline: ZodPipeline,\n    custom: custom,\n    Schema: ZodType,\n    ZodSchema: ZodType,\n    late: late,\n    get ZodFirstPartyTypeKind () { return ZodFirstPartyTypeKind; },\n    coerce: coerce,\n    any: anyType,\n    array: arrayType,\n    bigint: bigIntType,\n    boolean: booleanType,\n    date: dateType,\n    discriminatedUnion: discriminatedUnionType,\n    effect: effectsType,\n    'enum': enumType,\n    'function': functionType,\n    'instanceof': instanceOfType,\n    intersection: intersectionType,\n    lazy: lazyType,\n    literal: literalType,\n    map: mapType,\n    nan: nanType,\n    nativeEnum: nativeEnumType,\n    never: neverType,\n    'null': nullType,\n    nullable: nullableType,\n    number: numberType,\n    object: objectType,\n    oboolean: oboolean,\n    onumber: onumber,\n    optional: optionalType,\n    ostring: ostring,\n    pipeline: pipelineType,\n    preprocess: preprocessType,\n    promise: promiseType,\n    record: recordType,\n    set: setType,\n    strictObject: strictObjectType,\n    string: stringType,\n    symbol: symbolType,\n    transformer: effectsType,\n    tuple: tupleType,\n    'undefined': undefinedType,\n    union: unionType,\n    unknown: unknownType,\n    'void': voidType,\n    NEVER: NEVER,\n    ZodIssueCode: ZodIssueCode,\n    quotelessJson: quotelessJson,\n    ZodError: ZodError\n});\n\nexport { BRAND, DIRTY, EMPTY_PATH, INVALID, NEVER, OK, ParseStatus, ZodType as Schema, ZodAny, ZodArray, ZodBigInt, ZodBoolean, ZodBranded, ZodCatch, ZodDate, ZodDefault, ZodDiscriminatedUnion, ZodEffects, ZodEnum, ZodError, ZodFirstPartyTypeKind, ZodFunction, ZodIntersection, ZodIssueCode, ZodLazy, ZodLiteral, ZodMap, ZodNaN, ZodNativeEnum, ZodNever, ZodNull, ZodNullable, ZodNumber, ZodObject, ZodOptional, ZodParsedType, ZodPipeline, ZodPromise, ZodRecord, ZodType as ZodSchema, ZodSet, ZodString, ZodSymbol, ZodEffects as ZodTransformer, ZodTuple, ZodType, ZodUndefined, ZodUnion, ZodUnknown, ZodVoid, addIssueToContext, anyType as any, arrayType as array, bigIntType as bigint, booleanType as boolean, coerce, custom, dateType as date, z as default, errorMap as defaultErrorMap, discriminatedUnionType as discriminatedUnion, effectsType as effect, enumType as enum, functionType as function, getErrorMap, getParsedType, instanceOfType as instanceof, intersectionType as intersection, isAborted, isAsync, isDirty, isValid, late, lazyType as lazy, literalType as literal, makeIssue, mapType as map, nanType as nan, nativeEnumType as nativeEnum, neverType as never, nullType as null, nullableType as nullable, numberType as number, objectType as object, objectUtil, oboolean, onumber, optionalType as optional, ostring, pipelineType as pipeline, preprocessType as preprocess, promiseType as promise, quotelessJson, recordType as record, setType as set, setErrorMap, strictObjectType as strictObject, stringType as string, symbolType as symbol, effectsType as transformer, tupleType as tuple, undefinedType as undefined, unionType as union, unknownType as unknown, util, voidType as void, z };\n", "'use strict';\n\n/// <reference path=\"../../../js/common.d.ts\" />\n/// <reference types=\"@types/lodash\" />\n/// <reference path=\"../../jszip/jszip.d.ts\" />\n\nimport {AmeCustomizable, AmeCustomizableViewModel} from '../../pro-customizables/assets/customizable.js';\nimport {registerBaseComponents} from '../../pro-customizables/ko-components/ame-components.js';\nimport AmeAcStructure from './ko-components/ame-ac-structure.js';\nimport AmeAcSection from './ko-components/ame-ac-section.js';\nimport AmeAcSectionLink from './ko-components/ame-ac-section-link.js';\nimport AmeAcControl from './ko-components/ame-ac-control.js';\nimport AmeAcControlGroup from './ko-components/ame-ac-control-group.js';\nimport AmeAcContentSection from './ko-components/ame-ac-content-section.js';\nimport {AmeAdminCustomizerBase} from './admin-customizer-base.js';\nimport AmeAcSeparator from './ko-components/ame-ac-separator.js';\nimport AmeAcValidationErrors from './ko-components/ame-ac-validation-errors.js';\nimport z, {ZodError, ZodType} from '../../zod/lib/index.js';\n\ndeclare var wsAmeLodash: _.LoDashStatic;\ndeclare const wsAmeAdminCustomizerData: AmeAdminCustomizer.ScriptData;\n\nexport namespace AmeAdminCustomizer {\n\timport Setting = AmeCustomizable.Setting;\n\timport SettingCollection = AmeCustomizable.SettingCollection;\n\timport InterfaceStructureData = AmeCustomizable.InterfaceStructureData;\n\timport InterfaceStructure = AmeCustomizable.InterfaceStructure;\n\timport unserializeUiElement = AmeCustomizable.unserializeUiElement;\n\timport unserializeSetting = AmeCustomizable.unserializeSetting;\n\timport AnySpecificElementData = AmeCustomizable.AnySpecificElementData;\n\timport CustomizableVmInterface = AmeCustomizableViewModel.CustomizableVmInterface;\n\n\tconst $ = jQuery;\n\tconst _ = wsAmeLodash;\n\n\tregisterBaseComponents();\n\tko.components.register('ame-ac-structure', AmeAcStructure);\n\tko.components.register('ame-ac-section', AmeAcSection);\n\tko.components.register('ame-ac-section-link', AmeAcSectionLink);\n\tko.components.register('ame-ac-content-section', AmeAcContentSection);\n\tko.components.register('ame-ac-control-group', AmeAcControlGroup);\n\tko.components.register('ame-ac-control', AmeAcControl);\n\tko.components.register('ame-ac-separator', AmeAcSeparator);\n\tko.components.register('ame-ac-validation-errors', AmeAcValidationErrors);\n\n\texport interface ScriptData extends AmeAdminCustomizerBase.ScriptData, AdminThemeTexts {\n\t\tajaxUrl: string;\n\t\tsaveChangesetNonce: string;\n\t\ttrashChangesetNonce: string;\n\t\tchangesetItemCount: number;\n\t\tchangesetStatus: string;\n\t\tchangesetThemeMetadata: AdminThemeMetadata | null;\n\n\t\trefreshPreviewNonce: string;\n\t\tinitialPreviewUrl: string;\n\t\tinterfaceStructure: InterfaceStructureData;\n\n\t\t/**\n\t\t * The template to use when generating the URL for a changeset.\n\t\t *\n\t\t * By default, the changeset name is added as a query parameter. Alternatively,\n\t\t * you can use a path template that includes a \"{changeset}\" placeholder, which\n\t\t * will be replaced with the changeset name.\n\t\t */\n\t\tchangesetPathTemplate: string | null;\n\n\t\t/**\n\t\t * Whether to use pushState() to update the URL when the changeset name changes.\n\t\t *\n\t\t * By default, we discourage navigating to the old URL (no pushState()) because\n\t\t * the name is only expected to change when the old changeset becomes invalid\n\t\t * (e.g. it's deleted or published).\n\t\t */\n\t\tchangesetPushStateEnabled: boolean;\n\n\t\t/**\n\t\t * Admin Customizer base path. Defaults to the current URL path.\n\t\t *\n\t\t * Note that setting this to a non-empty value will also stop AC from\n\t\t * adding the \"page\" query parameter to the URL.\n\t\t */\n\t\tcustomBasePath: string | null;\n\n\t\texitPromptMode?: number;\n\n\t\t/**\n\t\t * Whether generating an admin theme requires the current changeset to be non-empty.\n\t\t *\n\t\t * By default, even if the current changeset is empty, the user can still generate an admin\n\t\t * theme from previously saved settings. If there are no settings, the generated theme just\n\t\t * won't change the admin interface at all.\n\t\t */\n\t\tdownloadOnlyIfChangesetIsNonEmpty?: boolean;\n\t}\n\n\tinterface AdminThemeTexts {\n\t\tgeneratorCreditPhrase?: string;\n\t\tstandalonePluginNote?: string;\n\t}\n\n\tconst reducedMotionQuery = window.matchMedia('(prefers-reduced-motion: reduce)');\n\tlet prefersReducedMotion = reducedMotionQuery && reducedMotionQuery.matches;\n\treducedMotionQuery.addEventListener('change', () => {\n\t\tprefersReducedMotion = reducedMotionQuery.matches;\n\t});\n\n\tclass CustomizerSettingsCollection extends SettingCollection {\n\t\t/**\n\t\t * Settings that have changed since the last save attempt.\n\t\t */\n\t\tprivate pendingSettings: Record<string, Setting> = {};\n\t\t/**\n\t\t * Settings that in the process of being sent to the server to be saved.\n\t\t * They might not be saved yet.\n\t\t */\n\t\tprivate sentSettings: Record<string, Setting> = {};\n\t\tprivate currentChangesetRequest: JQueryXHR | null = null;\n\t\tprivate saveTriggerTimeoutId: null | ReturnType<typeof setTimeout> = null;\n\n\t\tprivate readonly currentChangeset: KnockoutObservable<Changeset>;\n\t\tpublic readonly changesetName: KnockoutComputed<string>;\n\n\t\tpublic readonly adminThemeMetadata: KnockoutObservable<AdminThemeMetadata | null>;\n\t\tprivate readonly underlyingMetadata: KnockoutObservable<AdminThemeMetadata | null> = ko.observable<AdminThemeMetadata | null>(null);\n\t\tprivate readonly metadataHasChanged: KnockoutObservable<boolean> = ko.observable<boolean>(false);\n\n\t\tpublic readonly isExclusiveOperationInProgress: KnockoutComputed<boolean>;\n\t\tprivate readonly exclusiveOperation: KnockoutObservable<boolean> = ko.observable(false);\n\n\t\tconstructor(\n\t\t\tpublic readonly ajaxUrl: string,\n\t\t\tpublic readonly saveChangesetNonce: string,\n\t\t\tpublic readonly trashChangesetNonce: string,\n\t\t\tchangesetName: string,\n\t\t\tchangesetItemCount: number = 0,\n\t\t\tchangesetStatus: string | null = null\n\t\t) {\n\t\t\tsuper();\n\t\t\tconst self = this;\n\n\t\t\tthis.currentChangeset = ko.observable(\n\t\t\t\tnew Changeset(changesetName, changesetItemCount, changesetStatus)\n\t\t\t);\n\t\t\tthis.changesetName = ko.pureComputed(() => {\n\t\t\t\treturn (self.currentChangeset()?.name()) || '';\n\t\t\t});\n\n\t\t\tthis.adminThemeMetadata = ko.computed({\n\t\t\t\tread: () => this.underlyingMetadata(),\n\t\t\t\twrite: (newValue) => {\n\t\t\t\t\tconst oldValue = this.underlyingMetadata.peek();\n\t\t\t\t\tif (!_.isEqual(newValue, oldValue)) {\n\t\t\t\t\t\tthis.underlyingMetadata(newValue);\n\t\t\t\t\t\tthis.metadataHasChanged(true);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\n\t\t\t//Automatically save the changeset when any settings change.\n\t\t\tconst totalChangeCount = ko.pureComputed(() => {\n\t\t\t\tconst changeset = self.currentChangeset();\n\t\t\t\treturn (changeset ? changeset.currentSessionChanges() : 0);\n\t\t\t});\n\t\t\tconst debouncedSaveTrigger = _.debounce(\n\t\t\t\t() => {\n\t\t\t\t\t//Only save if there are changes. This may look like a duplicate check,\n\t\t\t\t\t//but it's not: the totalChangeCount() may change between the time\n\t\t\t\t\t//the debounced function is called and the time this code is executed.\n\t\t\t\t\t//\n\t\t\t\t\t//Also save if the metadata has changed, but only if the changeset\n\t\t\t\t\t//is not empty. Saving a changeset with only metadata is not useful.\n\t\t\t\t\tif (\n\t\t\t\t\t\t(totalChangeCount() > 0)\n\t\t\t\t\t\t|| (this.metadataHasChanged() && this.currentChangeset().isNonEmpty())\n\t\t\t\t\t) {\n\t\t\t\t\t\tself.queueChangesetUpdate()\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\t3000,\n\t\t\t\t{leading: true, trailing: true}\n\t\t\t)\n\t\t\ttotalChangeCount.subscribe((counter) => {\n\t\t\t\tif (counter > 0) {\n\t\t\t\t\tdebouncedSaveTrigger();\n\t\t\t\t}\n\t\t\t});\n\t\t\t//Also save when theme metadata changes.\n\t\t\tthis.metadataHasChanged.subscribe((hasChanged) => {\n\t\t\t\tif (hasChanged) {\n\t\t\t\t\tdebouncedSaveTrigger();\n\t\t\t\t}\n\t\t\t});\n\n\t\t\tthis.isExclusiveOperationInProgress = ko.pureComputed(() => {\n\t\t\t\treturn self.exclusiveOperation();\n\t\t\t});\n\n\t\t\t//Keep track of unsaved changes and changesets.\n\t\t\tthis.addChangeListener((setting: Setting) => {\n\t\t\t\tthis.pendingSettings[setting.id] = setting;\n\n\t\t\t\tlet changeset = this.currentChangeset();\n\t\t\t\t//If the current changeset cannot be modified, create a new one\n\t\t\t\t//for the changed setting(s).\n\t\t\t\tif (!changeset?.canBeModified()) {\n\t\t\t\t\tchangeset = new Changeset();\n\t\t\t\t\tthis.currentChangeset(changeset);\n\t\t\t\t}\n\t\t\t\t//Track the number of changes in the current session.\n\t\t\t\tchangeset.currentSessionChanges(changeset.currentSessionChanges() + 1);\n\t\t\t});\n\t\t}\n\n\t\tqueueChangesetUpdate(delay: number = 0) {\n\t\t\tif (delay > 0) {\n\t\t\t\tif (this.saveTriggerTimeoutId !== null) {\n\t\t\t\t\t//Replace the existing timeout with a new one.\n\t\t\t\t\tclearTimeout(this.saveTriggerTimeoutId);\n\t\t\t\t}\n\t\t\t\tthis.saveTriggerTimeoutId = setTimeout(() => {\n\t\t\t\t\tthis.saveTriggerTimeoutId = null;\n\t\t\t\t\tthis.queueChangesetUpdate(0);\n\t\t\t\t}, delay);\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tif (this.saveTriggerTimeoutId !== null) {\n\t\t\t\treturn; //Another timeout is already waiting.\n\t\t\t}\n\n\t\t\tif (this.currentChangesetRequest !== null) {\n\t\t\t\t//There's an in-progress request, so wait until it's done.\n\t\t\t\tthis.currentChangesetRequest.always(() => {\n\t\t\t\t\t//Wait a bit to avoid hammering the server.\n\t\t\t\t\tthis.queueChangesetUpdate(1000);\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tthis.saveChangeset();\n\t\t}\n\n\t\tprivate saveChangeset(status: string | null = null): JQueryPromise<any> {\n\t\t\t//Do nothing if there are no changes.\n\t\t\tif (_.isEmpty(this.pendingSettings) && (status === null) && !this.metadataHasChanged()) {\n\t\t\t\treturn $.Deferred().reject(new Error('There are no changes to save.')).promise();\n\t\t\t}\n\n\t\t\tif (this.isExclusiveOperationInProgress()) {\n\t\t\t\treturn $.Deferred().reject(\n\t\t\t\t\tnew Error('Another exclusive changeset operation is in progress.')\n\t\t\t\t).promise();\n\t\t\t}\n\n\t\t\tlet isExclusiveRequest = (status === 'publish') || (status === 'trash');\n\t\t\tif (isExclusiveRequest) {\n\t\t\t\tthis.exclusiveOperation(true);\n\t\t\t}\n\n\t\t\tconst savedChangeset = this.currentChangeset();\n\n\t\t\t//Keep a local copy of the settings in case something changes instance\n\t\t\t//properties while the request is in progress (should never happen).\n\t\t\tconst settingsToSend = this.pendingSettings;\n\t\t\tthis.sentSettings = settingsToSend;\n\t\t\tthis.pendingSettings = {};\n\n\t\t\tconst modifiedSettings = _.mapValues(settingsToSend, setting => setting.value());\n\t\t\tconst requestData: Record<string, any> = {\n\t\t\t\taction: 'ws_ame_ac_save_changeset',\n\t\t\t\t_ajax_nonce: this.saveChangesetNonce,\n\t\t\t\tchangeset: (savedChangeset?.name()) ?? '',\n\t\t\t\tmodified: JSON.stringify(modifiedSettings),\n\t\t\t};\n\t\t\tif (status !== null) {\n\t\t\t\trequestData['status'] = status;\n\t\t\t}\n\t\t\t//If the changeset doesn't have a name, it is new.\n\t\t\tif (!savedChangeset?.hasName()) {\n\t\t\t\trequestData['createNew'] = 1;\n\t\t\t}\n\n\t\t\t//Also send the metadata if it has changed.\n\t\t\tconst metadataWasChanged = this.metadataHasChanged();\n\t\t\tif (metadataWasChanged) {\n\t\t\t\tconst metadata = this.adminThemeMetadata();\n\t\t\t\trequestData['adminThemeMetadata'] = JSON.stringify(metadata);\n\t\t\t}\n\t\t\tthis.metadataHasChanged(false);\n\n\t\t\tconst request = $.ajax({\n\t\t\t\turl: this.ajaxUrl,\n\t\t\t\tmethod: 'POST',\n\t\t\t\tdata: requestData,\n\t\t\t\tdataType: 'json',\n\t\t\t\ttimeout: 20000,\n\t\t\t});\n\t\t\tthis.currentChangesetRequest = request;\n\n\t\t\tinterface ServerValidationResults {\n\t\t\t\t[settingId: string]: {\n\t\t\t\t\tisValid: boolean;\n\t\t\t\t\terrors: Array<{ code: string; message: string; }>;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tconst self = this;\n\n\t\t\tfunction storeValidationResultsFrom(serverResponse: any) {\n\t\t\t\tconst results: ServerValidationResults = _.get(\n\t\t\t\t\tserverResponse,\n\t\t\t\t\t['data', 'validationResults']\n\t\t\t\t);\n\t\t\t\tif (typeof results !== 'object') {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tfor (const settingId in results) {\n\t\t\t\t\tconst setting = self.get(settingId);\n\t\t\t\t\tif (!setting.isDefined()) {\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\n\t\t\t\t\tif (!modifiedSettings.hasOwnProperty(settingId)) {\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\t\t\t\t\tconst sentValue = modifiedSettings[settingId];\n\n\t\t\t\t\tconst state = results[settingId];\n\t\t\t\t\tif (state.isValid) {\n\t\t\t\t\t\tsetting.get().clearValidationErrorsForValue(sentValue);\n\t\t\t\t\t} else {\n\t\t\t\t\t\t//Since the server response is not fully validated, some typeof checks\n\t\t\t\t\t\t//are still useful.\n\t\t\t\t\t\t// noinspection SuspiciousTypeOfGuard\n\t\t\t\t\t\tsetting.get().addValidationErrorsForValue(\n\t\t\t\t\t\t\tsentValue,\n\t\t\t\t\t\t\t_.filter(state.errors, error => (typeof error.message === 'string'))\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tfunction storeChangesetDetailsFrom(serverResponse: any) {\n\t\t\t\tif (!savedChangeset) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\t//Store the returned changeset name in case a new changeset was created\n\t\t\t\t//or an existing changeset was forked due to permissions.\n\t\t\t\tconst newName = _.get(serverResponse, ['data', 'changeset']);\n\t\t\t\tif (!savedChangeset.hasName() || (newName !== savedChangeset.name())) {\n\t\t\t\t\tif (typeof newName === 'string') {\n\t\t\t\t\t\tsavedChangeset.name(newName);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t//Store the changeset status.\n\t\t\t\tconst newStatus = _.get(serverResponse, ['data', 'changesetStatus']);\n\t\t\t\tif (typeof newStatus === 'string') {\n\t\t\t\t\tsavedChangeset.status(newStatus);\n\t\t\t\t}\n\n\t\t\t\t//Store the number of changes in the changeset.\n\t\t\t\tconst newChangeCount = _.get(serverResponse, ['data', 'changesetItemCount']);\n\t\t\t\tif (typeof newChangeCount === 'number') {\n\t\t\t\t\tsavedChangeset.knownItemCount(newChangeCount);\n\t\t\t\t}\n\n\t\t\t\t//Was the changeset published? Because changesets are typically moved\n\t\t\t\t//to trash after publishing, \"status\" might be \"trash\" instead of \"publish\",\n\t\t\t\t//but we still want to know if it was successfully published.\n\t\t\t\tconst wasPublished = _.get(serverResponse, ['data', 'changesetWasPublished'], null);\n\t\t\t\tif (wasPublished) {\n\t\t\t\t\tsavedChangeset.wasPublished(wasPublished);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\trequest.done(function (response) {\n\t\t\t\tstoreChangesetDetailsFrom(response);\n\t\t\t\tstoreValidationResultsFrom(response);\n\n\t\t\t\t//After successfully publishing a changeset, it has no more\n\t\t\t\t//unsaved changes.\n\t\t\t\tconst isPublished =\n\t\t\t\t\t(savedChangeset.status() === 'publish')\n\t\t\t\t\t|| (savedChangeset.status() === 'future')\n\t\t\t\t\t|| (savedChangeset.wasPublished());\n\t\t\t\tif (isPublished) {\n\t\t\t\t\tsavedChangeset.currentSessionChanges(0);\n\t\t\t\t}\n\n\t\t\t\t//After a changeset is published or trashed, it can no longer\n\t\t\t\t//be edited. We may be able to replace it with a new changeset\n\t\t\t\t//that was created on the server.\n\t\t\t\tif (!self.currentChangeset().canBeModified()) {\n\t\t\t\t\tconst nextChangeset = _.get(response, ['data', 'nextChangeset']);\n\t\t\t\t\tif ((typeof nextChangeset === 'string') && (nextChangeset !== '')) {\n\t\t\t\t\t\tself.currentChangeset(new Changeset(nextChangeset));\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\n\t\t\trequest.fail((requestObject: JQueryXHR) => {\n\t\t\t\tif (typeof requestObject.responseJSON === 'object') {\n\t\t\t\t\tstoreValidationResultsFrom(requestObject.responseJSON);\n\t\t\t\t\tstoreChangesetDetailsFrom(requestObject.responseJSON);\n\t\t\t\t}\n\n\t\t\t\t//Add the unsaved settings back to the pending list.\n\t\t\t\tfor (const id in settingsToSend) {\n\t\t\t\t\t//Keep only settings that still exist.\n\t\t\t\t\tif (this.get(id).isDefined()) {\n\t\t\t\t\t\tthis.pendingSettings[id] = settingsToSend[id];\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t//We don't automatically retry because the problem might be something\n\t\t\t\t//that doesn't get better on its own, like missing permissions.\n\t\t\t});\n\n\t\t\trequest.always(() => {\n\t\t\t\tthis.currentChangesetRequest = null;\n\t\t\t\tthis.sentSettings = {};\n\t\t\t\tif (isExclusiveRequest) {\n\t\t\t\t\tthis.exclusiveOperation(false);\n\t\t\t\t}\n\t\t\t});\n\n\t\t\treturn request;\n\t\t}\n\n\t\tpublic savePendingSettings(timeout: number = 20): JQueryPromise<any> {\n\t\t\tif (this.isExclusiveOperationInProgress()) {\n\t\t\t\t//Wait for the exclusive operation to finish.\n\t\t\t\tconst deferred = $.Deferred();\n\t\t\t\tconst result = deferred.then(() => this.doSavePendingSettings());\n\n\t\t\t\tconst startTime = Date.now();\n\t\t\t\tconst timer = setInterval(() => {\n\t\t\t\t\tif (!this.isExclusiveOperationInProgress()) {\n\t\t\t\t\t\tclearInterval(timer);\n\t\t\t\t\t\tdeferred.resolve();\n\t\t\t\t\t} else if ((Date.now() - startTime) > timeout) {\n\t\t\t\t\t\tclearInterval(timer);\n\t\t\t\t\t\tdeferred.reject(new Error('Exclusive operation timed out.'));\n\t\t\t\t\t}\n\t\t\t\t}, 200);\n\n\t\t\t\treturn result;\n\t\t\t}\n\n\t\t\treturn this.doSavePendingSettings();\n\t\t}\n\n\t\tprivate doSavePendingSettings(): JQueryPromise<any> {\n\t\t\t//If there are no changes, we don't need to do anything.\n\t\t\tif (_.isEmpty(this.pendingSettings)) {\n\t\t\t\treturn $.Deferred().resolve().promise();\n\t\t\t}\n\t\t\treturn this.saveChangeset();\n\t\t}\n\n\t\tpublic getCurrentChangeset(): Changeset {\n\t\t\treturn this.currentChangeset();\n\t\t}\n\n\t\t/**\n\t\t * Get any unsaved setting changes.\n\t\t *\n\t\t * @returns Object An object mapping setting IDs to their modified values.\n\t\t */\n\t\tpublic get unsavedChanges(): Record<string, any> {\n\t\t\t//Include both pending settings and sent settings. Sent settings\n\t\t\t//might not be saved yet.\n\t\t\tlet unsavedSettings: Record<string, Setting> = {};\n\t\t\t_.defaults(unsavedSettings, this.pendingSettings, this.sentSettings);\n\n\t\t\treturn _.mapValues(unsavedSettings, setting => setting.value());\n\t\t}\n\n\t\tpublic publishChangeset(): JQueryPromise<any> {\n\t\t\tif (this.isExclusiveOperationInProgress()) {\n\t\t\t\treturn $.Deferred()\n\t\t\t\t\t.reject(new Error('Another exclusive changeset operation is already in progress.'))\n\t\t\t\t\t.promise();\n\t\t\t}\n\t\t\treturn this.saveChangeset('publish');\n\t\t}\n\n\t\tpublic trashChangeset(): JQueryPromise<any> {\n\t\t\tif (this.isExclusiveOperationInProgress()) {\n\t\t\t\treturn $.Deferred()\n\t\t\t\t\t.reject(new Error('Another exclusive changeset operation is already in progress.'))\n\t\t\t\t\t.promise();\n\t\t\t}\n\n\t\t\tconst changeset = this.currentChangeset();\n\t\t\tif (!changeset.hasName()) {\n\t\t\t\t//The changeset hasn't been saved yet, so we can just mark it as trashed.\n\t\t\t\tchangeset.status('trash');\n\t\t\t\tchangeset.currentSessionChanges(0);\n\n\t\t\t\t//It's a success of sorts.\n\t\t\t\treturn $.Deferred().resolve(true).promise();\n\t\t\t}\n\n\t\t\tthis.exclusiveOperation(true);\n\n\t\t\tconst requestData: Record<string, any> = {\n\t\t\t\taction: 'ws_ame_ac_trash_changeset',\n\t\t\t\t_ajax_nonce: this.trashChangesetNonce,\n\t\t\t\tchangeset: changeset.name\n\t\t\t};\n\n\t\t\tconst request = $.ajax({\n\t\t\t\turl: this.ajaxUrl,\n\t\t\t\tmethod: 'POST',\n\t\t\t\tdata: requestData,\n\t\t\t\tdataType: 'json',\n\t\t\t\ttimeout: 20000,\n\t\t\t});\n\t\t\tthis.currentChangesetRequest = request;\n\n\t\t\trequest.done(function () {\n\t\t\t\tchangeset.status('trash');\n\t\t\t\tchangeset.currentSessionChanges(0);\n\t\t\t});\n\n\t\t\t//Unfortunately, jQuery doesn't seem to allow us to create a custom\n\t\t\t//error object and pass it to other handlers, so code that uses this\n\t\t\t//method will have to parse the error response itself.\n\n\t\t\trequest.always(() => {\n\t\t\t\tthis.currentChangesetRequest = null;\n\t\t\t\tthis.exclusiveOperation(false);\n\t\t\t});\n\n\t\t\treturn request;\n\t\t}\n\n\t\tpublic addInitialThemeMetadata(metadata: AdminThemeMetadata | null) {\n\t\t\tthis.underlyingMetadata(metadata);\n\t\t\tthis.metadataHasChanged(false);\n\t\t}\n\t}\n\n\tclass Changeset {\n\t\tpublic readonly name: KnockoutObservable<string>;\n\t\tpublic readonly knownItemCount: KnockoutObservable<number>;\n\t\tpublic readonly status: KnockoutObservable<string>;\n\n\t\t/**\n\t\t * The number of times settings have been changed in this changeset\n\t\t * during the current customizer session.\n\t\t *\n\t\t * Note that this is not the same as the number settings in the changeset:\n\t\t * if the same setting is changed X times, this counter will increase by X,\n\t\t * but the changeset will still only have one entry for that setting.\n\t\t */\n\t\tpublic readonly currentSessionChanges: KnockoutObservable<number> = ko.observable(0);\n\n\t\t/**\n\t\t * Once a changeset has been published or deleted, its contents can't be modified any more.\n\t\t * @private\n\t\t */\n\t\tprivate readonly fixedContentStatuses: Record<string, any> =\n\t\t\t{'publish': true, 'trash': true, 'future': true};\n\n\t\tpublic readonly wasPublished: KnockoutObservable<boolean> = ko.observable(false);\n\n\t\tconstructor(name: string = '', knownItemCount: number = 0, initialStatus: string | null = '') {\n\t\t\tthis.name = ko.observable(name);\n\n\t\t\tthis.name.subscribe((newName) => {\n\t\t\t\t//In theory, the type system should ensure that the name is always a string,\n\t\t\t\t//but that only works on the TS side. I've previously run into a bug where\n\t\t\t\t//a null value was sent from the server. Let's add a check here to make it\n\t\t\t\t//easier to spot bugs like that in the future.\n\t\t\t\tif ((typeof (newName as unknown) !== 'string')) {\n\t\t\t\t\tthrow new Error('Changeset name must always be a string, found ' + (typeof newName));\n\t\t\t\t}\n\t\t\t});\n\n\t\t\tthis.knownItemCount = ko.observable(knownItemCount);\n\t\t\tthis.status = ko.observable(initialStatus ?? '');\n\t\t}\n\n\t\tpublic hasName(): boolean {\n\t\t\tconst name = this.name();\n\t\t\treturn (name !== '');\n\t\t}\n\n\t\tpublic canBeModified(): boolean {\n\t\t\treturn !this.fixedContentStatuses.hasOwnProperty(this.status());\n\t\t}\n\n\t\tpublic isNonEmpty(): boolean {\n\t\t\treturn (this.currentSessionChanges() > 0) || (this.knownItemCount() > 0)\n\t\t}\n\t}\n\n\tconst TemporaryChangesetName = 'temporary000'; //Note: Must match the value used in PHP.\n\n\t//region Admin theme\n\tconst UrlOrEmpty = z.union([\n\t\tz.string().url().max(1000),\n\t\tz.literal('')\n\t]);\n\n\tconst AdminThemeMetadata = z.object({\n\t\tpluginName: z.string().max(100),\n\t\tshortDescription: z.string().max(500),\n\n\t\tpluginSlug: z.string().max(64).toLowerCase().default('')\n\t\t\t.refine(\n\t\t\t\tfunction (input: string) {\n\t\t\t\t\t//Only allow alphanumeric characters, underscores, and dashes.\n\t\t\t\t\t//Empty string is allowed.\n\t\t\t\t\treturn /^[a-z0-9_-]*$/.test(input);\n\t\t\t\t},\n\t\t\t\t{message: 'The slug can only contain letters (a-z), numbers, underscores, and dashes.'}\n\t\t\t),\n\t\tidentifierPrefix: z.string().max(20).optional(),\n\n\t\tpluginVersion: z.string().default('1.0').optional(),\n\t\tpluginUrl: UrlOrEmpty.optional(),\n\t\tauthorName: z.string().max(100).optional(),\n\t\tauthorUrl: UrlOrEmpty.optional(),\n\t\trequiredWpVersion: z.string().max(30).default('4.7').optional(),\n\t\ttestedWpVersion: z.string().max(30).optional(),\n\t\twasEverConfirmed: z.boolean().default(false).optional(),\n\t});\n\n\ttype AdminThemeMetadata = z.infer<typeof AdminThemeMetadata>;\n\n\tconst AdminThemeSettings = z.record(\n\t\t//Key type\n\t\tz.string().min(1),\n\t\t//Value type\n\t\tz.any()\n\t);\n\n\tclass AdminThemeImportReport {\n\t\tpublic totalSettings: number = 0;\n\t\tpublic importedSettings: number = 0;\n\t\tpublic invalidSettings: number = 0;\n\t\tpublic skippedSettings: number = 0;\n\t\tpublic differentImportedSettings: number = 0;\n\n\t\tpublic readonly pluginName: string;\n\n\t\tconstructor(\n\t\t\tpublic readonly fileName: string,\n\t\t\tpublic readonly metadata: AdminThemeMetadata\n\t\t) {\n\t\t\tthis.pluginName = metadata.pluginName || '(Unnamed)';\n\t\t}\n\t}\n\n\tinterface WithZodValidationResults extends ObservableValidationFields {\n\t\tameZodValidationError: KnockoutObservable<z.ZodError | null>;\n\t}\n\n\ttype ZodValidatedObservable<T> = KnockoutComputed<T> & WithZodValidationResults;\n\n\tfunction observableWithZodValidation<T, S extends z.Schema<T>>(\n\t\tvalue: z.output<S>,\n\t\tschema: S\n\t): ZodValidatedObservable<z.output<S>> {\n\t\tconst underlyingObservable = ko.observable(value);\n\n\t\tconst observable: ZodValidatedObservable<T> = ko.pureComputed({\n\t\t\tread: underlyingObservable,\n\t\t\twrite: (newValue: T) => {\n\t\t\t\tconst validationResult = schema.safeParse(newValue);\n\t\t\t\tif (validationResult.success) {\n\t\t\t\t\tunderlyingObservable(validationResult.data);\n\t\t\t\t\tobservable.ameZodValidationError(null);\n\t\t\t\t\tobservable.ameValidationErrors([]);\n\t\t\t\t} else {\n\t\t\t\t\tobservable.ameZodValidationError(validationResult.error);\n\t\t\t\t\t//Convert Zod issues to ObservableValidationErrors.\n\t\t\t\t\tobservable.ameValidationErrors(validationResult.error.issues.map(issue => {\n\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\tcode: issue.code,\n\t\t\t\t\t\t\tmessage: issue.message\n\t\t\t\t\t\t} satisfies ObservableValidationError;\n\t\t\t\t\t}));\n\t\t\t\t}\n\t\t\t}\n\t\t}) as ZodValidatedObservable<T>;\n\n\t\tobservable.ameZodValidationError = ko.observable(null);\n\t\tobservable.ameValidationErrors = ko.observable([] as ObservableValidationError[]);\n\t\tobservable.ameIsValid = ko.pureComputed(() => {\n\t\t\tconst errors = observable.ameValidationErrors();\n\t\t\treturn !errors || errors.length === 0;\n\t\t});\n\n\t\treturn observable;\n\t}\n\n\tclass ObservableThemeMetadata {\n\t\tpublic readonly pluginName: ZodValidatedObservable<string>;\n\t\tpublic readonly shortDescription: ZodValidatedObservable<string>;\n\t\tpublic readonly pluginSlug: ZodValidatedObservable<AdminThemeMetadata['pluginSlug']>;\n\t\tpublic readonly identifierPrefix: ZodValidatedObservable<AdminThemeMetadata['identifierPrefix']>;\n\t\tpublic readonly pluginVersion: ZodValidatedObservable<AdminThemeMetadata['pluginVersion']>;\n\t\tpublic readonly pluginUrl: ZodValidatedObservable<AdminThemeMetadata['pluginUrl']>;\n\t\tpublic readonly authorName: ZodValidatedObservable<AdminThemeMetadata['authorName']>;\n\t\tpublic readonly authorUrl: ZodValidatedObservable<AdminThemeMetadata['authorUrl']>;\n\t\tpublic readonly requiredWpVersion: ZodValidatedObservable<AdminThemeMetadata['requiredWpVersion']>;\n\t\tpublic readonly testedWpVersion: ZodValidatedObservable<AdminThemeMetadata['testedWpVersion']>;\n\t\tpublic readonly wasEverConfirmed: ZodValidatedObservable<AdminThemeMetadata['wasEverConfirmed']>;\n\n\t\tconstructor(metadata: AdminThemeMetadata) {\n\t\t\tthis.pluginName = observableWithZodValidation(\n\t\t\t\tmetadata.pluginName,\n\t\t\t\tAdminThemeMetadata.shape.pluginName\n\t\t\t);\n\t\t\tthis.shortDescription = observableWithZodValidation(\n\t\t\t\tmetadata.shortDescription,\n\t\t\t\tAdminThemeMetadata.shape.shortDescription\n\t\t\t);\n\n\t\t\tthis.pluginSlug = observableWithZodValidation(\n\t\t\t\tmetadata.pluginSlug ?? '',\n\t\t\t\tAdminThemeMetadata.shape.pluginSlug\n\t\t\t);\n\t\t\tthis.identifierPrefix = observableWithZodValidation(\n\t\t\t\tmetadata.identifierPrefix ?? '',\n\t\t\t\tAdminThemeMetadata.shape.identifierPrefix\n\t\t\t);\n\n\t\t\tthis.pluginVersion = observableWithZodValidation(\n\t\t\t\tmetadata.pluginVersion ?? '',\n\t\t\t\tAdminThemeMetadata.shape.pluginVersion\n\t\t\t);\n\t\t\tthis.pluginUrl = observableWithZodValidation(\n\t\t\t\tmetadata.pluginUrl ?? '',\n\t\t\t\tAdminThemeMetadata.shape.pluginUrl\n\t\t\t);\n\t\t\tthis.authorName = observableWithZodValidation(\n\t\t\t\tmetadata.authorName ?? '',\n\t\t\t\tAdminThemeMetadata.shape.authorName\n\t\t\t);\n\t\t\tthis.authorUrl = observableWithZodValidation(\n\t\t\t\tmetadata.authorUrl ?? '',\n\t\t\t\tAdminThemeMetadata.shape.authorUrl\n\t\t\t);\n\t\t\tthis.requiredWpVersion = observableWithZodValidation(\n\t\t\t\tmetadata.requiredWpVersion ?? '',\n\t\t\t\tAdminThemeMetadata.shape.requiredWpVersion\n\t\t\t);\n\t\t\tthis.testedWpVersion = observableWithZodValidation(\n\t\t\t\tmetadata.testedWpVersion ?? '',\n\t\t\t\tAdminThemeMetadata.shape.testedWpVersion\n\t\t\t);\n\n\t\t\tthis.wasEverConfirmed = observableWithZodValidation(\n\t\t\t\tmetadata.wasEverConfirmed ?? false,\n\t\t\t\tAdminThemeMetadata.shape.wasEverConfirmed\n\t\t\t);\n\t\t}\n\n\t\tpublic toObject(): AdminThemeMetadata {\n\t\t\treturn {\n\t\t\t\tpluginName: this.pluginName(),\n\t\t\t\tshortDescription: this.shortDescription(),\n\t\t\t\tpluginSlug: this.pluginSlug(),\n\t\t\t\tidentifierPrefix: this.identifierPrefix(),\n\t\t\t\tpluginVersion: this.pluginVersion(),\n\t\t\t\tpluginUrl: this.pluginUrl(),\n\t\t\t\tauthorName: this.authorName(),\n\t\t\t\tauthorUrl: this.authorUrl(),\n\t\t\t\trequiredWpVersion: this.requiredWpVersion(),\n\t\t\t\ttestedWpVersion: this.testedWpVersion(),\n\t\t\t\twasEverConfirmed: this.wasEverConfirmed()\n\t\t\t};\n\t\t}\n\n\t\tisValid(): boolean {\n\t\t\t//This seems really inelegant, but I can't think of a better way to do it.\n\t\t\treturn this.pluginName.ameIsValid()\n\t\t\t\t&& this.shortDescription.ameIsValid()\n\t\t\t\t&& this.pluginSlug.ameIsValid()\n\t\t\t\t&& this.identifierPrefix.ameIsValid()\n\t\t\t\t&& this.pluginVersion.ameIsValid()\n\t\t\t\t&& this.pluginUrl.ameIsValid()\n\t\t\t\t&& this.authorName.ameIsValid()\n\t\t\t\t&& this.authorUrl.ameIsValid()\n\t\t\t\t&& this.requiredWpVersion.ameIsValid()\n\t\t\t\t&& this.testedWpVersion.ameIsValid()\n\t\t\t\t&& this.wasEverConfirmed.ameIsValid();\n\t\t}\n\t}\n\n\tenum MetadataDialogMode {\n\t\tDownload,\n\t\tEdit\n\t}\n\n\tclass DownloadThemeDialog extends AmeBaseKnockoutDialog {\n\t\tpublic readonly meta: KnockoutObservable<ObservableThemeMetadata>;\n\t\tpublic readonly areFieldsEditable: KnockoutComputed<boolean>;\n\t\tpublic readonly isOperationInProgress: KnockoutObservable<boolean> = ko.observable(false);\n\n\t\tpublic readonly mode: KnockoutObservable<MetadataDialogMode> = ko.observable<MetadataDialogMode>(MetadataDialogMode.Download);\n\n\t\tautoCancelButton: boolean = true;\n\t\tisConfirmButtonEnabled: KnockoutObservable<boolean>;\n\t\treadonly confirmButtonLabel: KnockoutObservable<string | null>;\n\n\t\tadvancedOptionsVisible: KnockoutObservable<boolean> = ko.observable(false);\n\t\tadvancedOptionsToggleLabel: KnockoutComputed<string>;\n\n\t\thelpVisible: KnockoutObservable<boolean> = ko.observable(false);\n\t\thelpToggleLabel: KnockoutComputed<string>;\n\t\thelpContainerVisible: KnockoutComputed<boolean>;\n\n\t\tchangesetName: KnockoutObservable<string> = ko.observable('');\n\t\tmetadataJson: KnockoutObservable<string> = ko.observable('');\n\t\tdownloadCookieName: KnockoutObservable<string> = ko.observable('');\n\n\t\tpublic readonly adminThemeTexts: Required<AdminThemeTexts>;\n\n\t\tprivate cleanupCurrentDownload: () => void = () => {\n\t\t};\n\n\t\tconstructor(\n\t\t\tprivate readonly getChangesetName: () => string,\n\t\t\tprivate readonly savePendingChangesetData: () => JQueryPromise<any>,\n\t\t\tprivate readonly metadataObservable: KnockoutObservable<AdminThemeMetadata | null>,\n\t\t\tcustomAdminThemeTexts: AdminThemeTexts\n\t\t) {\n\t\t\tsuper();\n\t\t\tthis.options.minWidth = 400;\n\n\t\t\tthis.adminThemeTexts = {\n\t\t\t\t...{\n\t\t\t\t\tgeneratorCreditPhrase: 'generated using the Admin Menu Editor Pro plugin.',\n\t\t\t\t\tstandalonePluginNote: 'The result is a standalone plugin that you can use without Admin Menu Editor Pro.',\n\t\t\t\t},\n\t\t\t\t...customAdminThemeTexts\n\t\t\t}\n\n\t\t\tlet initialMetadata = metadataObservable();\n\t\t\tif (initialMetadata === null) {\n\t\t\t\tinitialMetadata = this.getSampleMetadata();\n\t\t\t}\n\n\t\t\tthis.meta = ko.observable(new ObservableThemeMetadata(initialMetadata));\n\n\t\t\tthis.confirmButtonLabel = ko.computed(() => {\n\t\t\t\tif (this.mode() === MetadataDialogMode.Download) {\n\t\t\t\t\treturn 'Download Admin Theme';\n\t\t\t\t}\n\t\t\t\treturn 'OK';\n\t\t\t});\n\n\t\t\tthis.isConfirmButtonEnabled = ko.computed(() => {\n\t\t\t\tif (this.isOperationInProgress()) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\n\t\t\t\tif (getChangesetName() === '') {\n\t\t\t\t\t//To generate an admin theme, the changeset must have already been saved.\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t\treturn this.meta().isValid();\n\t\t\t});\n\n\t\t\tthis.areFieldsEditable = ko.computed(() => {\n\t\t\t\treturn !this.isOperationInProgress();\n\t\t\t});\n\n\t\t\tthis.advancedOptionsToggleLabel = ko.pureComputed((): string => {\n\t\t\t\treturn this.advancedOptionsVisible() ? 'Fewer options' : 'More options';\n\t\t\t});\n\t\t\tthis.helpToggleLabel = ko.pureComputed((): string => {\n\t\t\t\treturn this.helpVisible() ? 'Hide info' : 'How it works';\n\t\t\t});\n\n\t\t\t//Hide the help container in download mode.\n\t\t\tthis.helpContainerVisible = ko.pureComputed((): boolean => {\n\t\t\t\treturn this.mode() === MetadataDialogMode.Download;\n\t\t\t});\n\n\t\t\tthis.mode.subscribe((newMode: MetadataDialogMode) => {\n\t\t\t\tif (newMode === MetadataDialogMode.Download) {\n\t\t\t\t\tthis.title('Generate admin theme');\n\t\t\t\t} else if (newMode === MetadataDialogMode.Edit) {\n\t\t\t\t\tthis.title('Edit admin theme properties');\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\n\t\tprivate getSampleMetadata() {\n\t\t\treturn AdminThemeMetadata.parse({\n\t\t\t\tpluginName: 'Custom Admin Theme',\n\t\t\t\tshortDescription: 'A custom admin theme ' + this.adminThemeTexts.generatorCreditPhrase,\n\t\t\t\tpluginVersion: '1.0',\n\t\t\t});\n\t\t}\n\n\t\tonOpen(event: JQueryEventObject, ui: any): void {\n\t\t\tlet latestMetadata = this.metadataObservable();\n\t\t\tif (latestMetadata === null) {\n\t\t\t\tlatestMetadata = this.getSampleMetadata();\n\t\t\t}\n\t\t\tthis.meta(new ObservableThemeMetadata(latestMetadata));\n\t\t}\n\n\t\ttoggleAdvancedOptions(): void {\n\t\t\tthis.advancedOptionsVisible(!this.advancedOptionsVisible());\n\t\t}\n\n\t\ttoggleHelp(): void {\n\t\t\tthis.helpVisible(!this.helpVisible());\n\t\t}\n\n\t\tonConfirm(event: JQueryEventObject) {\n\t\t\tif (!this.meta().isValid()) {\n\t\t\t\t//This should never happen because the confirm button is disabled\n\t\t\t\t//when the metadata is invalid.\n\t\t\t\talert('Error: The admin theme details are not valid.');\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconst metadata = this.meta().toObject();\n\t\t\tmetadata.wasEverConfirmed = true;\n\t\t\tthis.metadataObservable(metadata);\n\n\t\t\tif (this.mode() === MetadataDialogMode.Edit) {\n\t\t\t\t//That's all we need to do in edit mode.\n\t\t\t\tthis.isOpen(false);\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tthis.triggerDownloadWithErrorReporting(metadata);\n\t\t}\n\n\t\tpublic triggerDownloadWithErrorReporting(metadata: AdminThemeMetadata) {\n\t\t\tif (this.isOperationInProgress()) {\n\t\t\t\talert('Error: Another operation is already in progress.');\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tthis.triggerDownload(metadata)\n\t\t\t\t.fail((error: string) => {\n\t\t\t\t\tif (error !== '') {\n\t\t\t\t\t\talert('Error: ' + error);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t}\n\n\t\tprivate triggerDownload(metadata: AdminThemeMetadata): JQueryPromise<any> {\n\t\t\tconst deferred = $.Deferred();\n\n\t\t\t//Sanity checks.\n\t\t\t//Download mode still requires a saved changeset.\n\t\t\tconst changesetName = this.getChangesetName();\n\t\t\tif (changesetName === '') {\n\t\t\t\treturn deferred.reject('The changeset has not been saved yet (name is empty).').promise();\n\t\t\t}\n\n\t\t\tthis.isOperationInProgress(true);\n\n\t\t\tconst $form = $('#ame-ac-theme-download-request-form');\n\t\t\tconst $frame = $('#ame-ac-theme-download-frame');\n\n\t\t\t//Cancel the operation and re-enable buttons if the request takes too long.\n\t\t\tlet isCancelledOrDone: boolean = false;\n\t\t\tconst requestTimeoutMs = 30000;\n\t\t\tconst requestStartTime = (new Date()).getTime();\n\t\t\tlet statusCheckInterval: ReturnType<typeof setTimeout> | null = null;\n\n\t\t\tconst cleanup = this.cleanupCurrentDownload = () => {\n\t\t\t\tisCancelledOrDone = true;\n\n\t\t\t\t$frame.off('load.ameAcDownloadAdminTheme');\n\t\t\t\tif (timeoutTimer) {\n\t\t\t\t\tclearTimeout(timeoutTimer);\n\t\t\t\t}\n\t\t\t\tif (statusCheckInterval) {\n\t\t\t\t\tclearInterval(statusCheckInterval);\n\t\t\t\t}\n\t\t\t\t$frame.attr('src', 'about:blank');\n\n\t\t\t\tthis.isOperationInProgress(false);\n\n\t\t\t\tif (this.cleanupCurrentDownload === cleanup) {\n\t\t\t\t\tthis.cleanupCurrentDownload = () => {\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tconst timeoutTimer = setTimeout(() => {\n\t\t\t\tdeferred.reject('The download operation timed out.');\n\t\t\t\tcleanup();\n\t\t\t}, requestTimeoutMs);\n\n\t\t\tthis.savePendingChangesetData().then(\n\t\t\t\t() => {\n\t\t\t\t\tif (isCancelledOrDone) {\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\tthis.changesetName(changesetName);\n\t\t\t\t\tthis.metadataJson(JSON.stringify(metadata));\n\n\t\t\t\t\t//The server will set a cookie with a unique name that can be used\n\t\t\t\t\t//to check if the download has been initiated. Note that the user\n\t\t\t\t\t//can still cancel the download.\n\t\t\t\t\tconst cookieName = ('ameAcFileDownload_'\n\t\t\t\t\t\t+ new Date().getTime()\n\t\t\t\t\t\t+ '_'\n\t\t\t\t\t\t+ Math.round(Math.random() * 10000) //No dots allowed in these cookie names.\n\t\t\t\t\t);\n\t\t\t\t\tthis.downloadCookieName(cookieName);\n\n\t\t\t\t\t//Clear the frame to prevent the old response from being read.\n\t\t\t\t\t$frame.attr('src', 'about:blank');\n\t\t\t\t\ttry {\n\t\t\t\t\t\t$frame.contents().find('body').html('');\n\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\t//Ignore but log cross-origin errors. These should not happen in practice.\n\t\t\t\t\t\tif (console && console.error) {\n\t\t\t\t\t\t\tconsole.error(e);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\tstatusCheckInterval = setInterval(() => {\n\t\t\t\t\t\tconst cookieValue = $.cookie(cookieName);\n\t\t\t\t\t\tif (cookieValue) {\n\t\t\t\t\t\t\tcleanup();\n\t\t\t\t\t\t\t$.removeCookie(cookieName);\n\n\t\t\t\t\t\t\t//Close the dialog when the download starts.\n\t\t\t\t\t\t\tthis.isOpen(false);\n\t\t\t\t\t\t\tdeferred.resolve();\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif ((new Date()).getTime() - requestStartTime > requestTimeoutMs) {\n\t\t\t\t\t\t\tcleanup();\n\t\t\t\t\t\t\tdeferred.reject('The download operation timed out.');\n\t\t\t\t\t\t}\n\t\t\t\t\t}, 1000);\n\n\t\t\t\t\t$frame.on('load.ameAcDownloadAdminTheme', () => {\n\t\t\t\t\t\t//Get the response from the frame. It should be JSON displayed as text.\n\t\t\t\t\t\tconst responseText = String($frame.contents().text()).trim();\n\t\t\t\t\t\tconst response = JSON.parse(responseText);\n\n\t\t\t\t\t\tcleanup();\n\n\t\t\t\t\t\tif ((response === null) || (typeof response !== 'object')) {\n\t\t\t\t\t\t\tdeferred.reject('Received an invalid response from the server.');\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tif (!response.success) {\n\t\t\t\t\t\t\t\tlet errorMessage;\n\t\t\t\t\t\t\t\tif (response.data.message) {\n\t\t\t\t\t\t\t\t\terrorMessage = response.data.message;\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\terrorMessage = 'An unknown error occurred on the server.';\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tdeferred.reject(errorMessage);\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t//This should never happen in practice.\n\t\t\t\t\t\t\t\tdeferred.reject('The server did not start the download correctly.');\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\n\t\t\t\t\t$form.trigger('submit');\n\t\t\t\t},\n\t\t\t\t() => {\n\t\t\t\t\tif (isCancelledOrDone) {\n\t\t\t\t\t\tif (deferred.state() === 'pending') {\n\t\t\t\t\t\t\tdeferred.reject(''); //No error message; the user probably cancelled the operation.\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\tcleanup();\n\t\t\t\t\tdeferred.reject('Could not save the changeset data before generating an admin theme.');\n\t\t\t\t}\n\t\t\t);\n\n\t\t\treturn deferred.promise();\n\t\t}\n\n\t\tonClose(event: JQueryEventObject, ui: any) {\n\t\t\tthis.cleanupCurrentDownload();\n\t\t}\n\t}\n\n\t//endregion\n\n\tclass SectionNavigation {\n\t\tprivate sectionNavStack: KnockoutObservableArray<string> = ko.observableArray([] as string[]);\n\t\tprivate $sectionList: JQuery;\n\n\t\tpublic readonly breadcrumbs: KnockoutObservable<NavigationBreadcrumb[]>;\n\n\t\tconstructor() {\n\t\t\tthis.$sectionList = $('#ame-ac-container-collection');\n\n\t\t\tthis.$sectionList.on('click', '.ame-ac-section-link', (event) => {\n\t\t\t\tevent.preventDefault()\n\n\t\t\t\tif (event.currentTarget === null) {\n\t\t\t\t\treturn; //Shouldn't happen in practice, but let's satisfy the type checker.\n\t\t\t\t}\n\n\t\t\t\tconst targetId = $(event.currentTarget).data('target-id');\n\t\t\t\tif (targetId) {\n\t\t\t\t\tthis.navigateToSection(targetId);\n\t\t\t\t}\n\t\t\t});\n\n\t\t\tthis.$sectionList.on('click', '.ame-ac-section-back-button', (event) => {\n\t\t\t\tevent.preventDefault()\n\t\t\t\tthis.navigateBack();\n\t\t\t});\n\n\t\t\tthis.breadcrumbs = ko.pureComputed(() => {\n\t\t\t\treturn this.sectionNavStack()\n\t\t\t\t\t.map((sectionId) => $('#' + sectionId))\n\t\t\t\t\t.filter(($section) => $section.length > 0)\n\t\t\t\t\t.map(($section) => {\n\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\ttitle: $section.find('.ame-ac-section-title .ame-ac-section-own-title')\n\t\t\t\t\t\t\t\t.first().text()\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t});\n\t\t}\n\n\t\tnavigateToSection(sectionElementId: string) {\n\t\t\tconst $section = $('#' + sectionElementId);\n\t\t\tif ($section.length === 0) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tif ($section.hasClass('ame-ac-current-section')) {\n\t\t\t\treturn; //Already on this section.\n\t\t\t}\n\n\t\t\t//If the requested section is in the navigation stack, navigate back\n\t\t\t//to it instead of putting more sections on the stack.\n\t\t\tconst stackIndex = this.sectionNavStack.indexOf(sectionElementId);\n\t\t\tif (stackIndex !== -1) {\n\t\t\t\twhile (this.sectionNavStack().length > stackIndex) {\n\t\t\t\t\tthis.navigateBack();\n\t\t\t\t}\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconst $previousSection = this.$sectionList.find('.ame-ac-current-section');\n\t\t\tif ($previousSection.length > 0) {\n\t\t\t\tthis.expectTransition($previousSection, '.ame-ac-section');\n\t\t\t\t$previousSection\n\t\t\t\t\t.removeClass('ame-ac-current-section')\n\t\t\t\t\t.addClass('ame-ac-previous-section');\n\t\t\t\tthis.sectionNavStack.push($previousSection.attr('id'));\n\n\t\t\t\t$previousSection.trigger('adminMenuEditor:leaveSection');\n\t\t\t}\n\n\t\t\tthis.expectTransition($section, '.ame-ac-section');\n\t\t\t$section.addClass('ame-ac-current-section');\n\n\t\t\t$section.trigger('adminMenuEditor:enterSection');\n\t\t}\n\n\t\tnavigateBack() {\n\t\t\tif (this.sectionNavStack().length < 1) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tconst $newCurrentSection = $('#' + this.sectionNavStack.pop());\n\t\t\tif ($newCurrentSection.length === 0) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconst $oldCurrentSection = this.$sectionList.find('.ame-ac-current-section');\n\t\t\tthis.expectTransition($oldCurrentSection, '.ame-ac-section');\n\t\t\t$oldCurrentSection.removeClass('ame-ac-current-section ame-ac-previous-section');\n\t\t\t$oldCurrentSection.trigger('adminMenuEditor:leaveSection');\n\n\t\t\tconst $oldPreviousSection = this.$sectionList.find('.ame-ac-previous-section');\n\t\t\t$oldPreviousSection.removeClass('ame-ac-previous-section');\n\n\t\t\t//Show the new current section.\n\t\t\tthis.expectTransition($newCurrentSection, '.ame-ac-section');\n\t\t\t$newCurrentSection.addClass('ame-ac-current-section');\n\t\t\t$newCurrentSection.trigger('adminMenuEditor:enterSection');\n\n\t\t\t//The next section in the stack becomes the previous section.\n\t\t\tif (this.sectionNavStack().length > 0) {\n\t\t\t\tthis.$sectionList.find('#' + this.sectionNavStack()[this.sectionNavStack().length - 1])\n\t\t\t\t\t.addClass('ame-ac-previous-section');\n\t\t\t}\n\t\t}\n\n\t\t//Add a special class to sections when they have an active CSS transition.\n\t\t//This is used to keep both sections visible while the previous section\n\t\t//slides out and the next section slides in.\n\t\texpectTransition($element: JQuery, requiredSelector: string) {\n\t\t\tif (prefersReducedMotion) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tif ($element.data('ameHasTransitionEvents')) {\n\t\t\t\treturn; //Event handler(s) already added.\n\t\t\t}\n\n\t\t\tconst transitionEvents = 'transitionend transitioncancel';\n\n\t\t\t$element.addClass('ame-ac-transitioning');\n\n\t\t\tfunction transitionEndCallback(event: JQueryEventObject) {\n\t\t\t\t//Ignore events that bubble from child elements.\n\t\t\t\tif (!$(event.target).is(requiredSelector)) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\t$element\n\t\t\t\t\t.off(transitionEvents, transitionEndCallback)\n\t\t\t\t\t.data('ameHasTransitionEvents', null)\n\t\t\t\t\t.removeClass('ame-ac-transitioning');\n\t\t\t}\n\n\t\t\t$element.data('ameHasTransitionEvents', true);\n\t\t\t$element.on(transitionEvents, transitionEndCallback);\n\t\t}\n\t}\n\n\texport interface NavigationBreadcrumb {\n\t\ttitle: string;\n\t}\n\n\t/**\n\t * Whether to ask for confirmation when the user tries to exit the customizer.\n\t */\n\tenum ExitPromptMode {\n\t\t/**\n\t\t * Ask if there are unsaved changes.\n\t\t */\n\t\tUnsavedChanges = 1,\n\n\t\t/**\n\t\t * Ask if the current changeset hasn't been published yet.\n\t\t */\n\t\tUnpublishedChanges = 2\n\t}\n\n\texport class AdminCustomizer extends AmeAdminCustomizerBase.AdminCustomizerBase implements CustomizableVmInterface {\n\t\tprivate readonly exitPromptMessage = 'Unsaved changes will be lost if you navigate away from this page.';\n\t\t//Admin themes generated by this plugin should be fairly small.\n\t\tprivate readonly maxImportFileSize = 500 * 1024;\n\n\t\tsectionNavigation: SectionNavigation;\n\t\tsettings: CustomizerSettingsCollection;\n\t\tpublic readonly interfaceStructure: InterfaceStructure;\n\n\t\tprivate readonly $previewFrame: JQuery;\n\n\t\t/**\n\t\t * Preview frame URL.\n\t\t */\n\t\tprivate currentPreviewUrl: string | null = null;\n\t\t/**\n\t\t * The default preview URL that can be used when the current frame URL cannot be detected.\n\t\t */\n\t\tprivate readonly initialPreviewUrl: string;\n\t\tprivate previewConnection: ReturnType<typeof AmeAcCommunicator.connectToChild> | null = null;\n\t\tprivate readonly refreshPreviewNonce: string;\n\n\t\tprivate readonly $saveButton: JQuery;\n\n\t\tpublic readonly downloadThemeDialog: DownloadThemeDialog;\n\t\tprivate $extraActionMenu: JQuery | null = null;\n\t\tprivate $extraActionButton: JQuery | null = null;\n\n\t\tprivate $importFileInput: JQuery | null = null;\n\t\tprivate isImporting: KnockoutObservable<boolean> = ko.observable(false);\n\t\tprivate lastImportReport: KnockoutObservable<AdminThemeImportReport | null> = ko.observable(null);\n\t\tprivate isImportReportVisible: KnockoutObservable<boolean> = ko.observable(true);\n\n\t\tprivate isDiscardingChanges: KnockoutObservable<boolean> = ko.observable(false);\n\n\t\tpublic readonly isGeneralOverlayVisible: KnockoutObservable<boolean>;\n\n\t\tprivate readonly importActionEnabled: KnockoutComputed<boolean>;\n\t\tprivate readonly discardChangesActionEnabled: KnockoutComputed<boolean>;\n\t\tprivate readonly downloadThemeActionEnabled: KnockoutComputed<boolean>;\n\n\t\tprivate readonly customBasePath: string | null;\n\t\tprivate readonly consoleLoggingEnabled: boolean;\n\t\tprivate readonly exitPromptMode: ExitPromptMode;\n\n\t\tprivate readonly downloadOnlyIfChangesetIsNonEmpty: boolean;\n\n\t\tconstructor(scriptData: ScriptData) {\n\t\t\tsuper(scriptData);\n\n\t\t\tthis.settings = new CustomizerSettingsCollection(\n\t\t\t\tscriptData.ajaxUrl,\n\t\t\t\tscriptData.saveChangesetNonce,\n\t\t\t\tscriptData.trashChangesetNonce,\n\t\t\t\tscriptData.changesetName,\n\t\t\t\tscriptData.changesetItemCount,\n\t\t\t\tscriptData.changesetStatus\n\t\t\t);\n\t\t\t_.forOwn(scriptData.settings, (data, id) => {\n\t\t\t\tif (typeof id === 'string') {\n\t\t\t\t\tthis.settings.add(unserializeSetting(id, data));\n\t\t\t\t}\n\t\t\t});\n\t\t\tif (scriptData.changesetThemeMetadata) {\n\t\t\t\tthis.settings.addInitialThemeMetadata(scriptData.changesetThemeMetadata);\n\t\t\t}\n\n\t\t\tthis.customBasePath = scriptData.customBasePath || null;\n\t\t\tthis.consoleLoggingEnabled = scriptData.isWpDebugEnabled || false;\n\t\t\tthis.downloadOnlyIfChangesetIsNonEmpty = scriptData.downloadOnlyIfChangesetIsNonEmpty || false;\n\n\t\t\tif ((typeof scriptData.exitPromptMode === 'number') && (scriptData.exitPromptMode in ExitPromptMode)) {\n\t\t\t\tthis.exitPromptMode = scriptData.exitPromptMode;\n\t\t\t} else {\n\t\t\t\tthis.exitPromptMode = ExitPromptMode.UnpublishedChanges;\n\t\t\t}\n\n\t\t\tlet sectionIdCounter = 0;\n\n\t\t\tthis.interfaceStructure = unserializeUiElement(\n\t\t\t\tscriptData.interfaceStructure,\n\t\t\t\tthis.settings.get.bind(this.settings),\n\t\t\t\t(data: AnySpecificElementData) => {\n\t\t\t\t\tswitch (data.t) {\n\t\t\t\t\t\tcase 'section':\n\t\t\t\t\t\t\tdata.component = 'ame-ac-section';\n\t\t\t\t\t\t\t//All sections must have unique IDs for navigation to work.\n\t\t\t\t\t\t\tif (!data.id) {\n\t\t\t\t\t\t\t\tdata.id = 'autoID-' + (++sectionIdCounter);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 'control-group':\n\t\t\t\t\t\t\tdata.component = 'ame-ac-control-group';\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 'control':\n\t\t\t\t\t\t\t//Tell controls that use number inputs to position the popup\n\t\t\t\t\t\t\t//slider within the customizer sidebar.\n\t\t\t\t\t\t\tif (\n\t\t\t\t\t\t\t\t(data.component === 'ame-number-input')\n\t\t\t\t\t\t\t\t|| (data.component === 'ame-box-dimensions')\n\t\t\t\t\t\t\t) {\n\t\t\t\t\t\t\t\tdata.params = data.params || {};\n\t\t\t\t\t\t\t\tdata.params.popupSliderWithin = '#ame-ac-sidebar-content';\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t//Replace regular separators with AC-specific ones.\n\t\t\t\t\t\t\tif (data.component === 'ame-horizontal-separator') {\n\t\t\t\t\t\t\t\tdata.component = 'ame-ac-separator';\n\t\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t);\n\n\t\t\t//Remove the reload parameter from the URL. It is only used to avoid\n\t\t\t//caching issues, and is not needed otherwise.\n\t\t\tconst currentUrl = new URL(window.location.href);\n\t\t\tif (currentUrl.searchParams.get('_ame-ac-reload') !== null) {\n\t\t\t\tcurrentUrl.searchParams.delete('_ame-ac-reload');\n\t\t\t\twindow.history.replaceState({}, '', currentUrl.href);\n\t\t\t}\n\n\t\t\t//Also remove the \"request new changeset\" parameter.\n\t\t\tif (currentUrl.searchParams.get('_ame-ac-new-changeset') !== null) {\n\t\t\t\tcurrentUrl.searchParams.delete('_ame-ac-new-changeset');\n\t\t\t\twindow.history.replaceState({}, '', currentUrl.href);\n\t\t\t}\n\n\t\t\tconst changesetPathTemplate: string | null = scriptData.changesetPathTemplate;\n\t\t\tconst changesetPlaceholder = '{changeset}';\n\n\t\t\tfunction addChangesetToUrl(currentUrl: string, changesetName: string): URL {\n\t\t\t\tconst url = new URL(currentUrl);\n\t\t\t\tif (changesetPathTemplate) {\n\t\t\t\t\turl.pathname = changesetPathTemplate.replace(changesetPlaceholder, changesetName);\n\t\t\t\t\t//With a custom path, the \"page\" parameter that points to the AC\n\t\t\t\t\t//admin page is not necessary and would be confusing.\n\t\t\t\t\turl.searchParams.delete('page');\n\t\t\t\t\t//When the changeset name is stored in the path, the \"ame-ac-changeset\"\n\t\t\t\t\t//parameter is no longer needed, and could be out of sync with the path.\n\t\t\t\t\turl.searchParams.delete('ame-ac-changeset');\n\t\t\t\t} else {\n\t\t\t\t\turl.searchParams.set('ame-ac-changeset', changesetName);\n\t\t\t\t}\n\t\t\t\treturn url;\n\t\t\t}\n\n\t\t\tfunction getChangesetFromUrl(url: string): string {\n\t\t\t\tconst parsedUrl = new URL(url);\n\t\t\t\tif (changesetPathTemplate) {\n\t\t\t\t\tfunction escapeRegExp(input: string): string {\n\t\t\t\t\t\treturn input.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\n\t\t\t\t\t}\n\n\t\t\t\t\tconst placeholderStart = changesetPathTemplate.indexOf(changesetPlaceholder);\n\t\t\t\t\tconst placeholderEnd = placeholderStart + changesetPlaceholder.length;\n\n\t\t\t\t\tconst changesetPathTemplateRegex = new RegExp(\n\t\t\t\t\t\t'^' + escapeRegExp(changesetPathTemplate.slice(0, placeholderStart))\n\t\t\t\t\t\t+ '([^a-zA-Z0-9]+)'\n\t\t\t\t\t\t+ escapeRegExp(changesetPathTemplate.slice(placeholderEnd))\n\t\t\t\t\t);\n\n\t\t\t\t\tconst match = parsedUrl.pathname.match(changesetPathTemplateRegex);\n\t\t\t\t\treturn match ? match[1] : '';\n\t\t\t\t} else {\n\t\t\t\t\treturn parsedUrl.searchParams.get('ame-ac-changeset') ?? '';\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t//Add the changeset name to the URL (if not already present).\n\t\t\tif (getChangesetFromUrl(window.location.href) !== this.settings.changesetName()) {\n\t\t\t\tconst newUrl = addChangesetToUrl(window.location.href, this.settings.changesetName());\n\t\t\t\twindow.history.replaceState({}, '', newUrl.href);\n\t\t\t}\n\n\t\t\t//When the changeset name changes, also change the URL.\n\t\t\tthis.settings.changesetName.subscribe((changesetName) => {\n\t\t\t\tconst url = addChangesetToUrl(window.location.href, changesetName);\n\t\t\t\tif (scriptData.changesetPushStateEnabled) {\n\t\t\t\t\twindow.history.pushState({}, '', url.href);\n\t\t\t\t} else {\n\t\t\t\t\twindow.history.replaceState({}, '', url.href);\n\t\t\t\t}\n\t\t\t});\n\n\t\t\tthis.$saveButton = $('#ame-ac-apply-changes');\n\n\t\t\t//The save button should be enabled when:\n\t\t\t// - There are non-zero changes in the current changeset.\n\t\t\t// - All settings are valid.\n\t\t\t// - The changeset is not in the process of being published, deleted, etc.\n\t\t\t// - The contents of the changeset can be modified (e.g. not already published).\n\t\t\tconst isSaveButtonEnabled = ko.pureComputed(() => {\n\t\t\t\tconst changeset = this.settings.getCurrentChangeset();\n\t\t\t\treturn (\n\t\t\t\t\tchangeset.isNonEmpty()\n\t\t\t\t\t&& changeset.canBeModified()\n\t\t\t\t\t&& !this.settings.isExclusiveOperationInProgress()\n\t\t\t\t\t&& !this.settings.hasValidationErrors()\n\t\t\t\t);\n\t\t\t});\n\t\t\t//Update button state when the customizer loads.\n\t\t\tthis.$saveButton.prop('disabled', !isSaveButtonEnabled());\n\t\t\t//And also on changes.\n\t\t\tisSaveButtonEnabled.subscribe((isEnabled) => {\n\t\t\t\tthis.$saveButton.prop('disabled', !isEnabled);\n\t\t\t\t//Change the text back to the default when the button is enabled.\n\t\t\t\tif (isEnabled) {\n\t\t\t\t\tthis.$saveButton.val(this.$saveButton.data('default-text') ?? 'Save Changes');\n\t\t\t\t}\n\t\t\t});\n\n\t\t\t//Handle the \"Save Changes\" button.\n\t\t\tthis.$saveButton.on('click', () => {\n\t\t\t\t//Show the spinner.\n\t\t\t\tconst $spinner = $('#ame-ac-primary-actions .spinner');\n\t\t\t\t$spinner.css('visibility', 'visible').show();\n\n\t\t\t\tconst publishFailNoticeId = 'ame-ac-publish-failed-notice';\n\t\t\t\t//Remove the previous error notification, if any.\n\t\t\t\t$('#' + publishFailNoticeId).remove();\n\n\t\t\t\tconst promise = this.settings.publishChangeset();\n\n\t\t\t\tpromise.fail((error) => {\n\t\t\t\t\t//Show a dismissible error notification.\n\t\t\t\t\tlet message = 'An unexpected error occurred while saving changes.';\n\t\t\t\t\tif (typeof error === 'string') {\n\t\t\t\t\t\tmessage = error;\n\t\t\t\t\t} else if (error instanceof Error) {\n\t\t\t\t\t\tmessage = error.message;\n\t\t\t\t\t} else if (typeof error.responseJSON === 'object') {\n\t\t\t\t\t\tmessage = _.get(error.responseJSON, ['data', 'message'], message);\n\t\t\t\t\t}\n\n\t\t\t\t\tconst $notice = $('<div>')\n\t\t\t\t\t\t.attr('id', publishFailNoticeId)\n\t\t\t\t\t\t.addClass('notice notice-error is-dismissible')\n\t\t\t\t\t\t.text(message);\n\n\t\t\t\t\t//WordPress won't automatically add the dismiss button to a dynamically\n\t\t\t\t\t//generated notice like this, so we have to do it.\n\t\t\t\t\t$notice.append(\n\t\t\t\t\t\t$('<button type=\"button\" class=\"notice-dismiss\"></button>')\n\t\t\t\t\t\t\t.append('<span class=\"screen-reader-text\">Dismiss this notice</span>')\n\t\t\t\t\t\t\t.on('click', (event) => {\n\t\t\t\t\t\t\t\tevent.preventDefault();\n\t\t\t\t\t\t\t\t$notice.remove(); //Not as fancy as WP does it.\n\t\t\t\t\t\t\t})\n\t\t\t\t\t);\n\n\t\t\t\t\tconst $container = $('#ame-ac-global-notification-area');\n\t\t\t\t\t$container.append($notice);\n\t\t\t\t})\n\n\t\t\t\tpromise.done(() => {\n\t\t\t\t\tthis.$saveButton.val(this.$saveButton.data('published-text') ?? 'Saved');\n\n\t\t\t\t\t//The preview could be stale. For example, the color scheme module\n\t\t\t\t\t//switches between \"actual\" and \"preview\" color schemes dynamically,\n\t\t\t\t\t//but the \"actual\" scheme could change after applying new settings.\n\t\t\t\t\t//Let's reload the preview frame to make sure it's up-to-date.\n\t\t\t\t\tthis.queuePreviewFrameReload();\n\t\t\t\t});\n\n\t\t\t\tpromise.always(() => {\n\t\t\t\t\t$spinner.css('visibility', 'hidden');\n\t\t\t\t});\n\t\t\t});\n\n\t\t\t//Prevent the user from interacting with settings while the changeset is being modified.\n\t\t\tthis.settings.isExclusiveOperationInProgress.subscribe((isInProgress) => {\n\t\t\t\t$('#ame-ac-sidebar-blocker-overlay').toggle(isInProgress);\n\t\t\t});\n\n\t\t\t//Show a general overlay with a progress spinner while something is happening.\n\t\t\tthis.isGeneralOverlayVisible = ko.pureComputed(() => {\n\t\t\t\treturn this.isImporting() || this.isDiscardingChanges();\n\t\t\t});\n\n\t\t\t//Initialize the \"download admin theme\" dialog.\n\t\t\tthis.downloadThemeDialog = new DownloadThemeDialog(\n\t\t\t\t() => this.settings.getCurrentChangeset().name(),\n\t\t\t\t() => this.settings.savePendingSettings(),\n\t\t\t\tthis.settings.adminThemeMetadata,\n\t\t\t\tscriptData\n\t\t\t);\n\n\t\t\t//Toggle available extra actions based on changeset status.\n\t\t\tthis.importActionEnabled = ko.pureComputed(() => {\n\t\t\t\tconst changeset = this.settings.getCurrentChangeset();\n\t\t\t\treturn changeset && changeset.canBeModified()\n\t\t\t\t\t&& !this.settings.isExclusiveOperationInProgress();\n\t\t\t});\n\t\t\tthis.importActionEnabled.subscribe((isEnabled) => {\n\t\t\t\tif (this.$extraActionMenu) {\n\t\t\t\t\tthis.$extraActionMenu.find('.ame-ac-import-theme-action')\n\t\t\t\t\t\t.toggleClass('ui-state-disabled', !isEnabled);\n\t\t\t\t}\n\t\t\t});\n\t\t\tthis.discardChangesActionEnabled = ko.pureComputed(() => {\n\t\t\t\tconst changeset = this.settings.getCurrentChangeset();\n\t\t\t\treturn changeset && changeset.isNonEmpty() && changeset.canBeModified()\n\t\t\t\t\t&& !this.settings.isExclusiveOperationInProgress()\n\t\t\t});\n\t\t\tthis.discardChangesActionEnabled.subscribe((isEnabled) => {\n\t\t\t\tif (this.$extraActionMenu) {\n\t\t\t\t\tthis.$extraActionMenu.find('.ame-ac-discard-changes-action')\n\t\t\t\t\t\t.toggleClass('ui-state-disabled', !isEnabled);\n\t\t\t\t}\n\t\t\t});\n\t\t\tthis.downloadThemeActionEnabled = ko.pureComputed(() => {\n\t\t\t\treturn (\n\t\t\t\t\t!this.settings.isExclusiveOperationInProgress()\n\t\t\t\t\t&& !this.downloadThemeDialog.isOperationInProgress()\n\t\t\t\t\t//The changeset must already be saved for the download to work,\n\t\t\t\t\t//which means it should have a name.\n\t\t\t\t\t&& (this.settings.getCurrentChangeset().name() !== '')\n\t\t\t\t\t&& (\n\t\t\t\t\t\t//Optionally, the download can be restricted to non-empty changesets.\n\t\t\t\t\t\t!this.downloadOnlyIfChangesetIsNonEmpty\n\t\t\t\t\t\t|| this.settings.getCurrentChangeset().isNonEmpty()\n\t\t\t\t\t)\n\t\t\t\t);\n\t\t\t});\n\t\t\tthis.downloadThemeActionEnabled.subscribe((isEnabled) => {\n\t\t\t\tif (this.$extraActionMenu) {\n\t\t\t\t\tthis.$extraActionMenu.find('.ame-ac-download-theme-action')\n\t\t\t\t\t\t.toggleClass('ui-state-disabled', !isEnabled);\n\t\t\t\t}\n\t\t\t});\n\n\t\t\tthis.sectionNavigation = new SectionNavigation();\n\n\t\t\t//Set up the preview frame.\n\t\t\tthis.$previewFrame = $('iframe#ame-ac-preview');\n\n\t\t\tthis.initialPreviewUrl = scriptData.initialPreviewUrl;\n\t\t\tthis.refreshPreviewNonce = scriptData.refreshPreviewNonce;\n\n\t\t\tthis.$previewFrame.on('load', () => {\n\t\t\t\tthis.isFrameLoading = false;\n\n\t\t\t\t//The URL that was actually loaded might not match the one that\n\t\t\t\t//was requested (e.g. because there was a redirect).\n\t\t\t\tthis.currentPreviewUrl = null;\n\n\t\t\t\t//Close the previous postMessage connection.\n\t\t\t\tif (this.previewConnection) {\n\t\t\t\t\tthis.previewConnection.disconnect();\n\t\t\t\t\tthis.previewConnection = null;\n\t\t\t\t}\n\n\t\t\t\tconst frame = this.$previewFrame.get(0) as HTMLIFrameElement;\n\t\t\t\tif (!frame || !(frame instanceof HTMLIFrameElement)) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\t//Try to get the preview URL from the iframe.\n\t\t\t\ttry {\n\t\t\t\t\tconst url = frame.contentWindow?.location.href;\n\t\t\t\t\tif (url) {\n\t\t\t\t\t\tthis.currentPreviewUrl = url;\n\t\t\t\t\t}\n\t\t\t\t} catch (e) {\n\t\t\t\t\t//We can't get the URL directly, probably because it's a cross-origin iframe.\n\t\t\t\t}\n\n\t\t\t\tthis.previewConnection = AmeAcCommunicator.connectToChild(\n\t\t\t\t\tframe,\n\t\t\t\t\t{\n\t\t\t\t\t\t'setPreviewUrl': (url: string) => {\n\t\t\t\t\t\t\tif (this.isPreviewableUrl(url)) {\n\t\t\t\t\t\t\t\tthis.previewUrl = url;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t},\n\t\t\t\t\t\t'notifyPreviewUrlChanged': (url: string) => {\n\t\t\t\t\t\t\tthis.currentPreviewUrl = url;\n\t\t\t\t\t\t}\n\t\t\t\t\t},\n\t\t\t\t\tthis.allowedCommOrigins,\n\t\t\t\t\tscriptData.isWpDebugEnabled\n\t\t\t\t);\n\n\t\t\t\tthis.previewConnection.promise.then((connection) => {\n\t\t\t\t\tif (typeof connection === 'undefined') {\n\t\t\t\t\t\t//This should never happen, but the type checker doesn't know that.\n\t\t\t\t\t\tthrow new Error('Unexpected error: Connection apparently succeeded, but the connection object is undefined');\n\t\t\t\t\t}\n\n\t\t\t\t\tconnection.execute('getCurrentUrl').then((url) => {\n\t\t\t\t\t\tif (url && (typeof url === 'string')) {\n\t\t\t\t\t\t\tthis.currentPreviewUrl = url;\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\n\t\t\t\t\t//Notify other scripts that the preview frame is loaded and\n\t\t\t\t\t//the postMessage connection is ready for use.\n\t\t\t\t\t$('body').trigger('adminMenuEditor:acPreviewConnectionReady');\n\t\t\t\t});\n\t\t\t});\n\n\t\t\tthis.previewUrl = this.initialPreviewUrl;\n\n\t\t\t//Notify other scripts. This lets them register custom controls and so on.\n\t\t\t$('#ame-ac-admin-customizer').trigger('adminMenuEditor:acRegister', [this]);\n\n\t\t\tconst throttledReloadPreview = _.throttle(\n\t\t\t\t() => {\n\t\t\t\t\tthis.queuePreviewFrameReload();\n\t\t\t\t},\n\t\t\t\t1000, //The reload method does its own throttling, so we use a low wait time here.\n\t\t\t\t{leading: true, trailing: true}\n\t\t\t);\n\n\t\t\t//Refresh the preview when any setting changes.\n\t\t\tthis.settings.addChangeListener((setting, newValue) => {\n\t\t\t\tif (\n\t\t\t\t\tsetting.supportsPostMessage\n\t\t\t\t\t&& this.previewConnection\n\t\t\t\t\t&& this.previewConnection.isConnected\n\t\t\t\t) {\n\t\t\t\t\tthis.previewConnection.execute('previewSetting', setting.id, newValue);\n\t\t\t\t} else {\n\t\t\t\t\tlet reason: string = 'Unknown';\n\t\t\t\t\tif (!setting.supportsPostMessage) {\n\t\t\t\t\t\treason = 'Setting \"' + setting.id + '\" does not support postMessage';\n\t\t\t\t\t} else if (!this.previewConnection) {\n\t\t\t\t\t\treason = 'No preview connection';\n\t\t\t\t\t} else if (!this.previewConnection.isConnected) {\n\t\t\t\t\t\treason = 'Preview connection is not connected';\n\t\t\t\t\t}\n\t\t\t\t\tthis.log('Reloading the preview frame because: ' + reason);\n\n\t\t\t\t\tthrottledReloadPreview();\n\t\t\t\t}\n\t\t\t});\n\n\t\t\tconst registerUnloadPrompt = () => {\n\t\t\t\t//Ask for confirmation when the user tries to leave the page and the changeset\n\t\t\t\t//has unpublished/unsaved changes.\n\t\t\t\t$(window).on('beforeunload.ame-ac-exit-confirm', (event) => {\n\t\t\t\t\tif (this.isExitPromptNeeded()) {\n\t\t\t\t\t\tevent.preventDefault();\n\t\t\t\t\t\t//Note: The confirmation prompt will only be displayed if the user\n\t\t\t\t\t\t//has interacted with the page (e.g. clicked something).\n\n\t\t\t\t\t\t//As of this writing, MDN says that some browsers still don't support triggering\n\t\t\t\t\t\t//an \"unsaved changes\" prompt with event.preventDefault(). You need to set\n\t\t\t\t\t\t//event.returnValue to a string or return a string from the event handler.\n\t\t\t\t\t\t//Modern browsers will ignore the content and display their own generic message.\n\t\t\t\t\t\treturn this.exitPromptMessage;\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\n\n\t\t\t/*\n\t\t\t Allegedly, registering a beforeunload handler can cause the browser to\n\t\t\t disable some optimizations, so let's only do it when the user changes\n\t\t\t something or the changeset already contains some changes.\n\t\t\t */\n\t\t\tif (this.settings.getCurrentChangeset().isNonEmpty()) {\n\t\t\t\tregisterUnloadPrompt();\n\t\t\t} else {\n\t\t\t\tconst listenerId = this.settings.addChangeListener(() => {\n\t\t\t\t\t//Remove the listener after it has been triggered once.\n\t\t\t\t\tthis.settings.removeChangeListener(listenerId);\n\t\t\t\t\tregisterUnloadPrompt();\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\n\t\tgetSettingObservable(settingId: string, defaultValue: any): KnockoutObservable<any> {\n\t\t\t//Let's just implement this temporarily while working on refactoring this\n\t\t\t//stuff to use KO components.\n\t\t\treturn this.settings\n\t\t\t\t.get(settingId)\n\t\t\t\t.map(setting => setting.value)\n\t\t\t\t.getOrElse(ko.observable(defaultValue));\n\t\t}\n\n\t\tgetAllSettingValues(): Record<string, any> {\n\t\t\tthrow new Error('Method not implemented.');\n\t\t}\n\n\t\tget previewUrl(): string | null {\n\t\t\treturn this.currentPreviewUrl;\n\t\t}\n\n\t\tset previewUrl(url: string | null) {\n\t\t\tif (url === this.currentPreviewUrl) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t//The URL starts out as null, but it cannot be set to NULL again after\n\t\t\t//the preview frame has been loaded.\n\t\t\tif (url === null) {\n\t\t\t\tthrow new Error('Cannot directly set preview URL to null');\n\t\t\t}\n\n\t\t\tif (this.isPreviewableUrl(url)) {\n\t\t\t\tthis.navigatePreviewFrame(url);\n\t\t\t}\n\t\t}\n\n\t\tprivate navigatePreviewFrame(url: string | null = null, forceReload: boolean = false) {\n\t\t\tconst oldUrl = this.previewUrl;\n\t\t\tif (url === null) {\n\t\t\t\turl = oldUrl ?? this.initialPreviewUrl;\n\t\t\t}\n\n\t\t\tconst isSameUrl = (oldUrl === url);\n\t\t\tif (isSameUrl && !forceReload) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t//If there are any unsaved changes, let's include them in the preview by simulating\n\t\t\t//a form submission and sending the changes as form data. The server-side component\n\t\t\t//will merge these changes with existing changeset data.\n\t\t\tconst unsavedChanges = this.settings.unsavedChanges;\n\t\t\tconst simulateFormSubmission = !_.isEmpty(unsavedChanges);\n\n\t\t\tconst parsedUrl = new URL(url);\n\n\t\t\t//If we're not using form submission, add a special parameter\n\t\t\t//to the URL to force a refresh.\n\t\t\tconst refreshParam = '_ame-ac-refresh-trigger';\n\t\t\tif (isSameUrl && !simulateFormSubmission) {\n\t\t\t\tparsedUrl.searchParams.set(refreshParam, Date.now() + '_' + Math.random());\n\t\t\t} else {\n\t\t\t\t//Otherwise, remove the parameter just to be safe.\n\t\t\t\tparsedUrl.searchParams.delete(refreshParam);\n\t\t\t}\n\n\t\t\t//Ensure that the changeset used in the preview matches the current\n\t\t\t//changeset and preview is enabled. This is just a precaution. Normally,\n\t\t\t//the preview script automatically changes link URLs.\n\t\t\tlet changesetName = this.settings.changesetName();\n\t\t\tif (changesetName === '') {\n\t\t\t\t//Use a special value if the changeset hasn't been saved yet.\n\t\t\t\tchangesetName = TemporaryChangesetName;\n\t\t\t}\n\t\t\tparsedUrl.searchParams.set('ame-ac-changeset', changesetName);\n\t\t\tparsedUrl.searchParams.set('ame-ac-preview', '1');\n\n\t\t\tthis.hasPendingPreviewReload = false; //Reloading now, so no longer pending.\n\t\t\tthis.isFrameLoading = true;\n\n\t\t\t//console.info('navigatePreviewFrame: Navigating to ' + parsedUrl.href);\n\t\t\tif (simulateFormSubmission) {\n\t\t\t\tconst formData = {\n\t\t\t\t\taction: 'ws_ame_ac_refresh_preview_frame',\n\t\t\t\t\t\"ame-ac-changeset\": changesetName,\n\t\t\t\t\tmodified: JSON.stringify(unsavedChanges),\n\t\t\t\t\tnonce: this.refreshPreviewNonce\n\t\t\t\t}\n\n\t\t\t\tconst $form = $('<form>')\n\t\t\t\t\t.attr('method', 'post')\n\t\t\t\t\t.attr('action', parsedUrl.href)\n\t\t\t\t\t.attr('target', 'ame-ac-preview-frame')\n\t\t\t\t\t.appendTo('body');\n\n\t\t\t\tlet key: keyof typeof formData;\n\t\t\t\tfor (key in formData) {\n\t\t\t\t\tconst value = formData[key];\n\t\t\t\t\t$('<input>')\n\t\t\t\t\t\t.attr('type', 'hidden')\n\t\t\t\t\t\t.attr('name', key)\n\t\t\t\t\t\t.val(value)\n\t\t\t\t\t\t.appendTo($form);\n\t\t\t\t}\n\n\t\t\t\tthis.currentPreviewUrl = parsedUrl.href;\n\t\t\t\t$form.trigger('submit');\n\t\t\t\t$form.remove();\n\t\t\t} else {\n\t\t\t\tthis.currentPreviewUrl = parsedUrl.href;\n\t\t\t\tthis.$previewFrame.attr('src', this.currentPreviewUrl);\n\t\t\t}\n\t\t}\n\n\t\tprivate _isFrameLoading: boolean = false;\n\t\tprivate frameLoadingTimeoutId: number | null = null;\n\t\tprivate lastPreviewLoadTimestamp: Date = new Date(0);\n\n\t\tprivate reloadWaitTimeoutId: number | null = null;\n\t\tprivate hasPendingPreviewReload: boolean = false;\n\n\t\tprivate set isFrameLoading(isLoading: boolean) {\n\t\t\tconst wasLoadingBefore = this._isFrameLoading;\n\t\t\tif (!isLoading && (isLoading === wasLoadingBefore)) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t//In some circumstances, we may start to load a new URL before\n\t\t\t//the previous one has finished loading. This is valid and should\n\t\t\t//reset the load timeout.\n\n\t\t\t$('#ame-ac-preview-refresh-indicator').toggleClass('ame-ac-show-indicator', isLoading);\n\t\t\tif (this.frameLoadingTimeoutId) {\n\t\t\t\tclearTimeout(this.frameLoadingTimeoutId);\n\t\t\t\tthis.frameLoadingTimeoutId = null;\n\t\t\t}\n\n\t\t\tif (isLoading) {\n\t\t\t\t//As a precaution, we'll assume that if the frame doesn't load in a reasonable\n\t\t\t\t//time, it will never finish loading.\n\t\t\t\tthis.frameLoadingTimeoutId = window.setTimeout(() => {\n\t\t\t\t\tif (this.isFrameLoading) {\n\t\t\t\t\t\tthis.isFrameLoading = false;\n\t\t\t\t\t}\n\t\t\t\t}, 20000);\n\t\t\t}\n\t\t\tthis._isFrameLoading = isLoading;\n\n\t\t\tif (wasLoadingBefore && !isLoading) {\n\t\t\t\tthis.lastPreviewLoadTimestamp = new Date();\n\t\t\t}\n\n\t\t\t//Once the frame is loaded, trigger any pending reload.\n\t\t\tif (!isLoading && this.hasPendingPreviewReload) {\n\t\t\t\tthis.hasPendingPreviewReload = false;\n\t\t\t\tthis.queuePreviewFrameReload();\n\t\t\t}\n\t\t}\n\n\t\tpublic get isFrameLoading(): boolean {\n\t\t\treturn this._isFrameLoading;\n\t\t}\n\n\t\tprivate queuePreviewFrameReload() {\n\t\t\tif (this.reloadWaitTimeoutId) {\n\t\t\t\treturn; //The frame will reload soon.\n\t\t\t}\n\n\t\t\tif (this.isFrameLoading) {\n\t\t\t\tthis.hasPendingPreviewReload = true;\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t//To avoid stressing the server, wait at least X ms after the last\n\t\t\t//load completes before reloading the frame.\n\t\t\tconst reloadWaitTime = 2000;\n\t\t\tconst now = new Date();\n\t\t\tconst timeSinceLastLoad = now.getTime() - this.lastPreviewLoadTimestamp.getTime();\n\t\t\tif (timeSinceLastLoad < reloadWaitTime) {\n\t\t\t\tthis.reloadWaitTimeoutId = window.setTimeout(() => {\n\t\t\t\t\tthis.reloadWaitTimeoutId = null;\n\t\t\t\t\tthis.queuePreviewFrameReload();\n\t\t\t\t}, reloadWaitTime - timeSinceLastLoad);\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t//Actually reload the frame.\n\t\t\tthis.navigatePreviewFrame(null, true);\n\t\t}\n\n\t\tonBindingsApplied(rootElement: HTMLElement) {\n\t\t\t//Navigate to the root section. In the current implementation this can't happen\n\t\t\t//until bindings have been applied, so it's not part of the constructor.\n\t\t\tthis.navigateToRootSection();\n\n\t\t\t//Initialize the action menu.\n\t\t\tthis.$extraActionButton = jQuery('#ame-ac-extra-actions-trigger', rootElement);\n\t\t\tthis.$extraActionMenu = jQuery('#ame-ac-extra-actions-menu', rootElement).menu();\n\n\t\t\t//Update menu states.\n\t\t\tthis.importActionEnabled.notifySubscribers(this.importActionEnabled());\n\t\t\tthis.discardChangesActionEnabled.notifySubscribers(this.discardChangesActionEnabled());\n\t\t\tthis.downloadThemeActionEnabled.notifySubscribers(this.downloadThemeActionEnabled());\n\n\t\t\t//Get the file picker.\n\t\t\tthis.$importFileInput = jQuery('#ame-ac-import-admin-theme-file', rootElement);\n\t\t}\n\n\t\tnavigateToRootSection() {\n\t\t\tthis.sectionNavigation.navigateToSection('ame-ac-section-structure-root');\n\t\t}\n\n\t\t// noinspection JSUnusedGlobalSymbols -- Used in at least one add-on.\n\t\t/**\n\t\t * Execute an RPC method in the preview frame.\n\t\t *\n\t\t * @param {string} methodName\n\t\t * @param {*} args\n\t\t */\n\t\texecuteRpcMethod(methodName: string, ...args: any): JQueryPromise<any> {\n\t\t\tif (!this.previewConnection || !this.previewConnection.isConnected) {\n\t\t\t\treturn $.Deferred().reject('The preview frame is not connected.').promise();\n\t\t\t}\n\t\t\treturn this.previewConnection.execute(methodName, ...args);\n\t\t}\n\n\t\tconfirmExit() {\n\t\t\tif (this.isExitPromptNeeded()) {\n\t\t\t\tif (window.confirm(this.exitPromptMessage)) {\n\t\t\t\t\t//Remove the confirmation prompt that appears when leaving the page.\n\t\t\t\t\t//We don't want to show two prompts.\n\t\t\t\t\t$(window).off('beforeunload.ame-ac-exit-confirm');\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\treturn true;\n\t\t}\n\n\t\tprivate isExitPromptNeeded(): boolean {\n\t\t\tconst changeset = this.settings.getCurrentChangeset();\n\n\t\t\t//No need to save anything if the changeset is empty.\n\t\t\tif (!changeset.isNonEmpty()) {\n\t\t\t\treturn false;\n\t\t\t}\n\n\t\t\tswitch (this.exitPromptMode) {\n\t\t\t\tcase ExitPromptMode.UnpublishedChanges:\n\t\t\t\t\treturn (\n\t\t\t\t\t\t!changeset.wasPublished()\n\t\t\t\t\t\t&& (changeset.status() !== 'trash') //Can't publish a trashed changeset.\n\t\t\t\t\t);\n\t\t\t\tcase ExitPromptMode.UnsavedChanges:\n\t\t\t\t\tconst unsavedChanges = this.settings.unsavedChanges;\n\t\t\t\t\treturn !_.isEmpty(unsavedChanges);\n\t\t\t\tdefault:\n\t\t\t\t\treturn false;\n\t\t\t}\n\n\t\t}\n\n\t\t// noinspection JSUnusedGlobalSymbols -- Used in the Knockout template.\n\t\ttoggleExtraActionMenu() {\n\t\t\tif (!this.$extraActionMenu) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tthis.$extraActionMenu.toggle();\n\n\t\t\tif (this.$extraActionMenu.is(':visible')) {\n\t\t\t\t//Position the menu below the button.\n\t\t\t\tconst $button = $('#ame-ac-extra-actions-trigger');\n\t\t\t\tthis.$extraActionMenu.position({\n\t\t\t\t\tmy: 'right top',\n\t\t\t\t\tat: 'right bottom',\n\t\t\t\t\tof: $button,\n\t\t\t\t\tcollision: 'flipfit'\n\t\t\t\t});\n\n\t\t\t\t//Hide the menu when the user clicks outside the menu or the button.\n\t\t\t\t$(document).on('mousedown.ameAcExtraMenuHide', this.handleClickOutsideActionMenu.bind(this));\n\t\t\t} else {\n\t\t\t\t//Remove the click listener if it's still active.\n\t\t\t\t$(document).off('mousedown.ameAcExtraMenuHide');\n\t\t\t}\n\t\t}\n\n\t\thandleClickOutsideActionMenu(event: JQueryEventObject) {\n\t\t\tif (\n\t\t\t\t!this.$extraActionMenu\n\t\t\t\t|| !this.$extraActionMenu.is(':visible')\n\t\t\t\t|| !this.$extraActionButton\n\t\t\t) {\n\t\t\t\t//The event listener should not be active if the menu is not visible.\n\t\t\t\t$(document).off('mousedown.ameAcExtraMenuHide');\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconst menuElement = this.$extraActionMenu.get(0);\n\t\t\tconst buttonElement = this.$extraActionButton.get(0);\n\t\t\tconst isClickOutsideMenu = !menuElement.contains(event.target);\n\t\t\tconst isClickOutsideButton = !buttonElement.contains(event.target);\n\n\t\t\tif (isClickOutsideMenu && isClickOutsideButton) {\n\t\t\t\tthis.hideExtraActionMenu();\n\t\t\t}\n\t\t}\n\n\t\tprivate hideExtraActionMenu() {\n\t\t\tif (!this.$extraActionMenu) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tthis.$extraActionMenu.hide();\n\t\t\t//Stop listening for clicks outside the menu.\n\t\t\t$(document).off('mousedown.ameAcExtraMenuHide');\n\t\t}\n\n\t\tprivate openMetadataDialog(mode: MetadataDialogMode) {\n\t\t\tthis.downloadThemeDialog.mode(mode);\n\t\t\tthis.downloadThemeDialog.isOpen(true);\n\t\t\tthis.isImportReportVisible(false);\n\t\t\tthis.hideExtraActionMenu();\n\t\t}\n\n\t\tactionOpenDownloadDialog() {\n\t\t\tif (!this.downloadThemeActionEnabled()) {\n\t\t\t\talert('Currently disabled because there are no changes to download.');\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tthis.openMetadataDialog(MetadataDialogMode.Download);\n\t\t}\n\n\t\t// noinspection JSUnusedGlobalSymbols -- Used in another plugin.\n\t\tactionEditOrDownloadTheme() {\n\t\t\tif (!this.downloadThemeActionEnabled()) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t//If the user hasn't confirmed the theme metadata yet, show the dialog.\n\t\t\tconst metadata = this.settings.adminThemeMetadata();\n\t\t\tif ((metadata === null) || !metadata.wasEverConfirmed) {\n\t\t\t\tthis.openMetadataDialog(MetadataDialogMode.Download);\n\t\t\t} else {\n\t\t\t\tthis.downloadThemeDialog.triggerDownloadWithErrorReporting(metadata);\n\t\t\t}\n\t\t}\n\n\t\t// noinspection JSUnusedGlobalSymbols -- Used in another plugin.\n\t\tactionOpenMetadataDialog() {\n\t\t\tthis.openMetadataDialog(MetadataDialogMode.Edit);\n\t\t}\n\n\t\tactionOpenImportDialog() {\n\t\t\tif (!this.importActionEnabled()) {\n\t\t\t\t//Can't import if there is no changeset or the changeset can't be edited.\n\t\t\t\t//The menu item should be disabled in this case, but we'll check anyway.\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tthis.hideExtraActionMenu();\n\n\t\t\t//Allow the default action to proceed, which will open the file picker.\n\t\t\treturn true;\n\t\t}\n\n\t\tactionDiscardChanges() {\n\t\t\tif (!this.discardChangesActionEnabled()) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tthis.hideExtraActionMenu();\n\n\t\t\tif (this.settings.isExclusiveOperationInProgress()) {\n\t\t\t\talert('Another operation is in progress. Please wait for it to complete before discarding changes.');\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tif (!confirm('Are you sure you want to discard your unsaved changes?')) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tthis.isImportReportVisible(false);\n\t\t\tthis.isDiscardingChanges(true);\n\n\t\t\tthis.settings.trashChangeset()\n\t\t\t\t.then(() => {\n\t\t\t\t\t//Reload the customizer with a new changeset.\n\t\t\t\t\tconst url = new URL(window.location.href);\n\t\t\t\t\tif (this.customBasePath) {\n\t\t\t\t\t\turl.pathname = this.customBasePath;\n\t\t\t\t\t\turl.search = '';\n\t\t\t\t\t} else {\n\t\t\t\t\t\t//To get the customizer's base URL, get the current URL\n\t\t\t\t\t\t//and remove all query parameters except \"page\".\n\t\t\t\t\t\tconst page = url.searchParams.get('page');\n\t\t\t\t\t\turl.search = '';\n\t\t\t\t\t\turl.searchParams.set('page', page || 'ame-admin-customizer');\n\t\t\t\t\t}\n\t\t\t\t\t//Notify the customizer that we definitely want a new changeset;\n\t\t\t\t\t//don't try to load a draft.\n\t\t\t\t\turl.searchParams.set('_ame-ac-new-changeset', '1');\n\n\t\t\t\t\t//Don't need the hash.\n\t\t\t\t\turl.hash = '';\n\n\t\t\t\t\t//Add a random parameter to force a reload.\n\t\t\t\t\turl.searchParams.set('_ame-ac-reload', Math.random().toString(36).substring(7));\n\n\t\t\t\t\t//Navigate to the new URL.\n\t\t\t\t\twindow.location.href = url.toString();\n\n\t\t\t\t\t//Note that the isDiscardingChanges flag is not reset here,\n\t\t\t\t\t//so the progress overlay will stay visible until the page reloads.\n\t\t\t\t})\n\t\t\t\t.fail((requestObject) => {\n\t\t\t\t\tlet message: string = requestObject.statusText || 'Unknown error.';\n\n\t\t\t\t\tif (typeof requestObject.responseJSON === 'object') {\n\t\t\t\t\t\tconst customMessage = _.get(requestObject.responseJSON, ['data', 'message']);\n\t\t\t\t\t\tif (typeof customMessage === 'string') {\n\t\t\t\t\t\t\tmessage = customMessage;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\talert('Error: ' + message);\n\t\t\t\t\tthis.isDiscardingChanges(false);\n\t\t\t\t});\n\t\t}\n\n\t\thandleImportFileSelection() {\n\t\t\tif (!this.$importFileInput) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tconst fileInput = this.$importFileInput.get(0) as HTMLInputElement;\n\t\t\tif (!fileInput || !fileInput.files || (fileInput.files.length < 1)) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t//Get the first file. Normally, there should only be one.\n\t\t\tconst selectedFile = fileInput.files.item(0);\n\t\t\tif (!selectedFile) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t//Limit the file size.\n\t\t\tif (selectedFile.size > this.maxImportFileSize) {\n\t\t\t\talert(\n\t\t\t\t\t'Error: The selected file is too large. The maximum file size is '\n\t\t\t\t\t+ Math.round(this.maxImportFileSize / 1024) + ' KiB'\n\t\t\t\t);\n\t\t\t\t//Clear the file input.\n\t\t\t\tthis.$importFileInput.val('');\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tthis.isImporting(true);\n\t\t\tthis.lastImportReport(null);\n\n\t\t\tJSZip.loadAsync(selectedFile).then(\n\t\t\t\t(zip) => {\n\t\t\t\t\tconst metadataFileRegex = /^([\\\\/]?[a-zA-Z0-9_-]+[\\\\/])metadata\\.json$/;\n\t\t\t\t\tconst foundMetadataFiles = zip.file(metadataFileRegex);\n\t\t\t\t\tif (!foundMetadataFiles || (foundMetadataFiles.length < 1)) {\n\t\t\t\t\t\tthrow new Error('The selected file is not an admin theme generated by this tool.');\n\t\t\t\t\t}\n\t\t\t\t\tconst metadataFile = foundMetadataFiles[0];\n\n\t\t\t\t\t//Get the directory name and separator from the metadata file path.\n\t\t\t\t\t//The prefix will usually be something like \"admin-theme-slug/\".\n\t\t\t\t\tconst matches = metadataFileRegex.exec(metadataFile.name);\n\t\t\t\t\tlet directoryPrefix: string;\n\t\t\t\t\tif (!matches || (matches.length < 2)) {\n\t\t\t\t\t\tthrow new Error('The directory structure of this ZIP file is not recognized.');\n\t\t\t\t\t} else {\n\t\t\t\t\t\tdirectoryPrefix = matches[1];\n\t\t\t\t\t}\n\n\t\t\t\t\tconst settingsFile = zip.file(directoryPrefix + 'settings.json');\n\t\t\t\t\tif (!settingsFile) {\n\t\t\t\t\t\tthrow new Error('The selected ZIP file is missing a settings.json file.');\n\t\t\t\t\t}\n\n\t\t\t\t\t//Read both files.\n\t\t\t\t\treturn Promise.all([\n\t\t\t\t\t\tmetadataFile.async('string'),\n\t\t\t\t\t\tsettingsFile.async('string')\n\t\t\t\t\t]);\n\t\t\t\t},\n\t\t\t\t(error) => {\n\t\t\t\t\tconst errorMessage = error.message || error;\n\t\t\t\t\tthrow new Error('Error reading \"' + selectedFile.name + '\": ' + errorMessage);\n\t\t\t\t}\n\t\t\t).then((fileContents) => {\n\t\t\t\tif (!fileContents) {\n\t\t\t\t\tthrow new Error('Failed to read settings and metadata from the ZIP file.');\n\t\t\t\t}\n\n\t\t\t\tconst metadata = this.parseImportedAdminThemeFile(\n\t\t\t\t\tfileContents[0],\n\t\t\t\t\t'metadata.json',\n\t\t\t\t\tAdminThemeMetadata\n\t\t\t\t);\n\t\t\t\tconst settings = this.parseImportedAdminThemeFile(\n\t\t\t\t\tfileContents[1],\n\t\t\t\t\t'settings.json',\n\t\t\t\t\tAdminThemeSettings\n\t\t\t\t);\n\t\t\t\tconst report = new AdminThemeImportReport(selectedFile.name, metadata);\n\n\t\t\t\t//Import metadata.\n\t\t\t\tthis.downloadThemeDialog.meta(new ObservableThemeMetadata(metadata));\n\n\t\t\t\t//Import settings.\n\t\t\t\tfor (const [settingId, value] of Object.entries(settings)) {\n\t\t\t\t\treport.totalSettings++;\n\n\t\t\t\t\tconst foundSetting = this.settings.get(settingId);\n\t\t\t\t\tfoundSetting.forEach((setting) => {\n\t\t\t\t\t\tconst oldValue = setting.value();\n\t\t\t\t\t\tconst errors = setting.tryUpdate(value);\n\t\t\t\t\t\tif (errors && errors.length) {\n\t\t\t\t\t\t\treport.invalidSettings++;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\treport.importedSettings++;\n\t\t\t\t\t\t\tif (oldValue != value) {\n\t\t\t\t\t\t\t\treport.differentImportedSettings++;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\n\t\t\t\t\tif (foundSetting.isEmpty()) {\n\t\t\t\t\t\treport.skippedSettings++;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tthis.lastImportReport(report);\n\t\t\t\tthis.isImportReportVisible(true);\n\n\t\t\t}).catch((error) => {\n\t\t\t\t//Error handling: Show the error message to the user.\n\t\t\t\tlet errorMessage: string;\n\t\t\t\tif (error instanceof Error) {\n\t\t\t\t\terrorMessage = error.message;\n\t\t\t\t} else {\n\t\t\t\t\terrorMessage = String(error);\n\t\t\t\t}\n\t\t\t\talert('Error: ' + errorMessage);\n\t\t\t}).finally(() => {\n\t\t\t\tthis.isImporting(false);\n\t\t\t\tthis.$importFileInput?.val('');\n\t\t\t});\n\t\t}\n\n\t\tprivate parseImportedAdminThemeFile<T extends ZodType>(\n\t\t\tcontent: string,\n\t\t\tname: string,\n\t\t\tschema: T\n\t\t): ReturnType<T['parse']> {\n\t\t\ttry {\n\t\t\t\tconst parsedJson = JSON.parse(content);\n\t\t\t\treturn schema.parse(parsedJson);\n\t\t\t} catch (error) {\n\t\t\t\tlet errorMessage: string;\n\t\t\t\tif (error instanceof ZodError) {\n\t\t\t\t\t//Convert issues to a newline-separated string.\n\t\t\t\t\terrorMessage = error.issues.map((issue) => {\n\t\t\t\t\t\treturn issue.path.join('.') + ': ' + issue.message;\n\t\t\t\t\t}).join('\\n');\n\t\t\t\t} else if (error instanceof Error) {\n\t\t\t\t\terrorMessage = error.message;\n\t\t\t\t} else {\n\t\t\t\t\terrorMessage = String(error);\n\t\t\t\t}\n\t\t\t\t//Add the file name to the error message.\n\t\t\t\tthrow new Error('Error parsing ' + name + ':\\n' + errorMessage);\n\t\t\t}\n\t\t}\n\n\t\tdismissImportReport(): void {\n\t\t\tthis.isImportReportVisible(false);\n\t\t}\n\n\t\tlog(message: any): void {\n\t\t\tif (this.consoleLoggingEnabled && console && console.log) {\n\t\t\t\tconsole.log(message);\n\t\t\t}\n\t\t}\n\t}\n}\n\ndeclare global {\n\tinterface Window {\n\t\twsAdminCustomizer: AmeAdminCustomizer.AdminCustomizer;\n\t}\n}\n\njQuery(function () {\n\t//Give other scripts a chance to load before we start.\n\t//Some of them also use jQuery to run when the DOM is ready.\n\tsetTimeout(() => {\n\t\twindow.wsAdminCustomizer = new AmeAdminCustomizer.AdminCustomizer(wsAmeAdminCustomizerData);\n\t\tconst rootElement = document.getElementById('ame-ac-admin-customizer');\n\t\tif (rootElement === null) {\n\t\t\tthrow new Error('The root element for the admin customizer was not found.');\n\t\t}\n\n\t\tko.applyBindings(window.wsAdminCustomizer, rootElement);\n\n\t\t//Notify the customizer that bindings have been applied. It needs to do some\n\t\t//additional setup that can't be done until the DOM structure is ready.\n\t\tsetTimeout(() => {\n\t\t\twindow.wsAdminCustomizer.onBindingsApplied(rootElement);\n\t\t}, 5); //Components are rendered asynchronously.\n\t}, 20);\n});", "'use strict';\r\nexport var AmeAdminCustomizerBase;\r\n(function (AmeAdminCustomizerBase) {\r\n    class AdminCustomizerBase {\r\n        constructor(scriptData) {\r\n            this.allowedCommOrigins = scriptData.allowedCommOrigins;\r\n            if (this.allowedCommOrigins.length === 0) {\r\n                this.allowedCommOrigins = [window.location.origin];\r\n            }\r\n            this.allowedPreviewUrls = scriptData.allowedPreviewUrls;\r\n            this.parsedAllowedUrls = this.allowedPreviewUrls.map(url => new URL(url));\r\n        }\r\n        isPreviewableUrl(url) {\r\n            if (typeof url === 'string') {\r\n                url = new URL(url);\r\n            }\r\n            if (typeof url.protocol === 'undefined') {\r\n                return false;\r\n            }\r\n            //Only HTTP(S) links are previewable.\r\n            if ((url.protocol !== 'http:') && (url.protocol !== 'https:')) {\r\n                return false;\r\n            }\r\n            //Check against the list of allowed URLs.\r\n            for (const allowedUrl of this.parsedAllowedUrls) {\r\n                //Protocol and host must match. The path must start with the path\r\n                //of the allowed URL (possibly without a trailing slash).\r\n                if ((url.protocol === allowedUrl.protocol) && (url.host === allowedUrl.host)) {\r\n                    const allowedPath = allowedUrl.pathname.replace(/\\/$/, '');\r\n                    if (url.pathname.indexOf(allowedPath) === 0) {\r\n                        return true;\r\n                    }\r\n                }\r\n            }\r\n            return false;\r\n        }\r\n    }\r\n    AmeAdminCustomizerBase.AdminCustomizerBase = AdminCustomizerBase;\r\n})(AmeAdminCustomizerBase || (AmeAdminCustomizerBase = {}));\r\n//# sourceMappingURL=admin-customizer-base.js.map", "import { AmeAcSection } from './ame-ac-section.js';\r\nimport { createComponentConfig } from '../../../pro-customizables/ko-components/control-base.js';\r\nclass AmeAcContentSection extends AmeAcSection {\r\n    constructor(params, $element) {\r\n        super(params, $element);\r\n        if ((typeof params.parentSectionLevel === 'function') && ko.isObservable(params.parentSectionLevel)) {\r\n            this.parentSectionLevel = params.parentSectionLevel;\r\n        }\r\n        else {\r\n            this.parentSectionLevel = null;\r\n        }\r\n        this.contentSectionLevel = ko.pureComputed(() => {\r\n            let parentLevel = 0;\r\n            if (this.parentSectionLevel !== null) {\r\n                parentLevel = this.parentSectionLevel();\r\n            }\r\n            return parentLevel + 1;\r\n        });\r\n        //Tell child sections about our section level.\r\n        this.childComponents().forEach((child) => {\r\n            if (child.name === 'ame-ac-content-section') {\r\n                child.params.parentSectionLevel = this.contentSectionLevel;\r\n            }\r\n        });\r\n        this.sectionLevelClass = ko.pureComputed(() => {\r\n            const level = this.contentSectionLevel();\r\n            return 'ame-ac-content-section-' + level;\r\n        });\r\n    }\r\n}\r\nexport default createComponentConfig(AmeAcContentSection, `\n\t<li class=\"ame-ac-control ame-ac-content-section\" data-bind=\"class: sectionLevelClass\">\n\t\t<h4 class=\"ame-ac-control-label ame-ac-content-section-title\" data-bind=\"text: title\"></h4>\t\n\t</li>\t\n\t<!-- ko foreach: childComponents -->\n\t\t<!-- ko component: $data --><!-- /ko -->\n\t<!-- /ko -->\t\n`);\r\n//# sourceMappingURL=ame-ac-content-section.js.map", "import { ComponentBindingOptions, createComponentConfig, KoContainerViewModel } from '../../../pro-customizables/ko-components/control-base.js';\r\nimport { AmeCustomizable } from '../../../pro-customizables/assets/customizable.js';\r\nvar ControlGroup = AmeCustomizable.ControlGroup;\r\nclass AmeAcControlGroup extends KoContainerViewModel {\r\n    constructor(params, $element) {\r\n        var _a, _b;\r\n        super(params, $element);\r\n        this.labelFor = (_b = ((_a = this.uiElement) === null || _a === void 0 ? void 0 : _a.labelFor)) !== null && _b !== void 0 ? _b : null;\r\n        this.titleDisabled = (typeof params.titleDisabled !== 'undefined') ? (!!params.titleDisabled) : false;\r\n    }\r\n    getExpectedUiElementType() {\r\n        return ControlGroup;\r\n    }\r\n    mapChildToComponentBinding(child) {\r\n        if (child.component) {\r\n            return ComponentBindingOptions.fromElement(child);\r\n        }\r\n        return super.mapChildToComponentBinding(child);\r\n    }\r\n}\r\nexport default createComponentConfig(AmeAcControlGroup, `\n\t<li class=\"ame-ac-control ame-ac-control-group\">\n\t\t<!-- ko if: title() && !titleDisabled -->\n\t\t\t<!-- ko if: labelFor -->\n\t\t\t<label class=\"ame-ac-control-label ame-ac-group-title\" \n\t\t\t\tdata-bind=\"text: title, attr: {for: labelFor}\"></label>\n\t\t\t<!-- /ko -->\n\t\t\t<!-- ko ifnot: labelFor -->\n\t\t\t<span class=\"ame-ac-control-label ame-ac-group-title\" \n\t\t\t\tdata-bind=\"text: title\"></span>\n\t\t\t<!-- /ko -->\n\t\t<!-- /ko -->\n\t\t<ul data-bind=\"foreach: childComponents\">\n\t\t\t<li class=\"ame-ac-control\">\n\t\t\t\t<!-- ko if: (\n\t\t\t\t\t$data.uiElement \n\t\t\t\t\t&& $data.uiElement.settingValidationErrors\n\t\t\t\t\t&& ($data.uiElement.settingValidationErrors().length > 0)\n\t\t\t\t) -->\n\t\t\t\t\t<ame-ac-validation-errors params='errors: $data.uiElement.settingValidationErrors'></ame-ac-validation-errors>\n\t\t\t\t<!-- /ko -->\n\t\t\t\t<!-- ko component: $data --><!-- /ko -->\n\t\t\t</li>\t\t\n\t\t</ul>\n\t</li>\n`);\r\n//# sourceMappingURL=ame-ac-control-group.js.map", "import { createComponentConfig, KoControlViewModel } from '../../../pro-customizables/ko-components/control-base.js';\r\nimport { AmeCustomizable } from '../../../pro-customizables/assets/customizable.js';\r\nvar Control = AmeCustomizable.Control;\r\nclass MissingComponentError extends Error {\r\n    constructor(uiElement) {\r\n        super(`The UI element \"${uiElement.label}\" [${uiElement.id}] is missing a component name.`);\r\n        this.uiElement = uiElement;\r\n    }\r\n}\r\nclass AmeAcControl extends KoControlViewModel {\r\n    constructor(params, $element) {\r\n        super(params, $element);\r\n        //uiElement is required for this component.\r\n        if (!this.uiElement) {\r\n            throw new Error('The uiElement parameter is required for AmeAcControl');\r\n        }\r\n        this.wrapperLabelEnabled = (this.uiElement.label !== '') && (!this.uiElement.includesOwnLabel);\r\n        this.labelForId = this.uiElement.labelTargetId;\r\n        if (!this.uiElement.component) {\r\n            throw new MissingComponentError(this.uiElement);\r\n        }\r\n    }\r\n    getExpectedUiElementType() {\r\n        return Control;\r\n    }\r\n}\r\nexport default createComponentConfig(AmeAcControl, `\n\t<li class=\"ame-ac-control\">\n\t\t<!-- ko if: wrapperLabelEnabled -->\n\t\t\t<!-- ko if: labelForId -->\n\t\t\t<label class=\"ame-ac-control-label\" data-bind=\"text: label, attr: {for: labelForId}\"></label>\n\t\t\t<!-- /ko -->\n\t\t\t<!-- ko ifnot: labelForId -->\n\t\t\t<span class=\"ame-ac-control-label\" data-bind=\"text: label\"></span>\n\t\t\t<!-- /ko -->\n\t\t<!-- /ko -->\n\t\t<!-- ko component: {name: uiElement.component, params: uiElement.getComponentParams()} --><!-- /ko -->\n\t</li>\n`);\r\n//# sourceMappingURL=ame-ac-control.js.map", "import { createComponentConfig, KoContainerViewModel } from '../../../pro-customizables/ko-components/control-base.js';\r\nimport { AmeCustomizable } from '../../../pro-customizables/assets/customizable.js';\r\nvar Section = AmeCustomizable.Section;\r\nimport { AmeAcSection } from './ame-ac-section.js';\r\nclass AmeAcSectionLink extends KoContainerViewModel {\r\n    constructor(params, $element) {\r\n        super(params, $element);\r\n        //uiElement is required for this component.\r\n        if (!this.uiElement) {\r\n            throw new Error('The uiElement parameter is required for AmeAcSectionLink');\r\n        }\r\n        this.targetElementId = AmeAcSection.getSectionElementId(this.uiElement);\r\n        this.elementId = AmeAcSection.getSectionLinkElementId(this.uiElement);\r\n    }\r\n    getExpectedUiElementType() {\r\n        return Section;\r\n    }\r\n}\r\nexport default createComponentConfig(AmeAcSectionLink, `\n\t<li class=\"ame-ac-section-link\" data-bind=\"attr: {'data-target-id' : targetElementId, 'id': elementId}\">\n\t\t<h3 class=\"ame-ac-section-title\" data-bind=\"text: title\"></h3>\n\t</li>\n`);\r\n//# sourceMappingURL=ame-ac-section-link.js.map", "import { ComponentBindingOptions, createComponentConfig, KoContainerViewModel } from '../../../pro-customizables/ko-components/control-base.js';\r\nimport { AmeCustomizable } from '../../../pro-customizables/assets/customizable.js';\r\nvar Section = AmeCustomizable.Section;\r\nvar Control = AmeCustomizable.Control;\r\nvar ControlGroup = AmeCustomizable.ControlGroup;\r\nexport class AmeAcSection extends KoContainerViewModel {\r\n    constructor(params, $element) {\r\n        super(params, $element);\r\n        //Must have an uiElement.\r\n        if (this.uiElement === null) {\r\n            throw new Error('AmeAcSection must have an uiElement.');\r\n        }\r\n        this.elementId = AmeAcSection.getSectionElementId(this.uiElement);\r\n        if ((typeof params.breadcrumbs !== 'undefined') && ko.isObservable(params.breadcrumbs)) {\r\n            this.breadcrumbs = params.breadcrumbs;\r\n        }\r\n        else {\r\n            this.breadcrumbs = null;\r\n        }\r\n        //To keep the header text alignment consistent when navigating between sections,\r\n        //let's show something even if there are no breadcrumbs.\r\n        const defaultEmptyBreadcrumbText = 'Admin Menu Editor Pro';\r\n        //Let other modules change the default text.\r\n        let filteredEmptyBreadcrumbText = null;\r\n        if (wp && wp.hooks && wp.hooks.applyFilters) {\r\n            filteredEmptyBreadcrumbText = wp.hooks.applyFilters('adminMenuEditor.ac.emptyBreadcrumbText', defaultEmptyBreadcrumbText);\r\n        }\r\n        const emptyBreadcrumbText = ((typeof filteredEmptyBreadcrumbText === 'string')\r\n            ? filteredEmptyBreadcrumbText\r\n            : defaultEmptyBreadcrumbText);\r\n        this.breadcrumbText = ko.pureComputed(() => {\r\n            if (this.breadcrumbs === null) {\r\n                return emptyBreadcrumbText;\r\n            }\r\n            const breadcrumbs = this.breadcrumbs();\r\n            if (breadcrumbs.length < 1) {\r\n                return emptyBreadcrumbText;\r\n            }\r\n            let titles = breadcrumbs.map(crumb => crumb.title);\r\n            //Show the root section differently, \"Admin Customizer\" is too long.\r\n            //Not sure about what text to use here, could matching the Theme Customizer be confusing?\r\n            //Alternatives: 🛠️🎨, use \\uFE0E to render the emoji without colors (only works for some).\r\n            //Alternatives: ⋯ and …\r\n            titles[0] = 'Customizing';\r\n            //Due to space constraints, show only the last 2 breadcrumbs.\r\n            if (titles.length > 2) {\r\n                titles = titles.slice(titles.length - 2);\r\n            }\r\n            return titles.join(' \\u25B8 ');\r\n        });\r\n    }\r\n    getExpectedUiElementType() {\r\n        return Section;\r\n    }\r\n    mapChildToComponentBinding(child) {\r\n        if (child instanceof Section) {\r\n            if (child.preferredRole === 'content') {\r\n                return ComponentBindingOptions.fromElement(child, 'ame-ac-content-section');\r\n            }\r\n            else {\r\n                return ComponentBindingOptions.fromElement(child, 'ame-ac-section-link');\r\n            }\r\n        }\r\n        else if (child instanceof ControlGroup) {\r\n            return ComponentBindingOptions.fromElement(child, 'ame-ac-control-group');\r\n        }\r\n        else if ((child instanceof Control)\r\n            && (['ame-ac-separator', 'ame-horizontal-separator'].indexOf(child.component) < 0)) {\r\n            //Wrap each control in a control group if it's not already in one.\r\n            //Separators are an exception because they're cosmetic and need different styling.\r\n            const controlGroup = child.createControlGroup();\r\n            return this.mapChildToComponentBinding(controlGroup);\r\n        }\r\n        else {\r\n            return ComponentBindingOptions.fromElement(child);\r\n        }\r\n    }\r\n    static getSectionElementId(section) {\r\n        return AmeAcSection.generateSectionElementId(section, 'ame-ac-section-');\r\n    }\r\n    static getSectionLinkElementId(section) {\r\n        return AmeAcSection.generateSectionElementId(section, 'ame-ac-slink-');\r\n    }\r\n    static generateSectionElementId(section, prefix) {\r\n        if (section.id) {\r\n            return prefix + section.id;\r\n        }\r\n        const slug = section.title.toLowerCase().replace(/[^a-z0-9]/g, '-');\r\n        if (slug !== '') {\r\n            return prefix + slug;\r\n        }\r\n        throw new Error('Cannot generate a section element ID because the section does not have an ID or a title.');\r\n    }\r\n    dispose() {\r\n        super.dispose();\r\n        this.childComponents.dispose();\r\n    }\r\n}\r\nexport default createComponentConfig(AmeAcSection, `\n\t<ul class=\"ame-ac-section\" data-bind=\"attr: {id: elementId}\">\n\t\t<li class=\"ame-ac-section-meta\">\n\t\t\t<div class=\"ame-ac-section-header\">\n\t\t\t\t<button class=\"ame-ac-section-back-button\">\n\t\t\t\t\t<span class=\"screen-reader-text\">Back</span>\n\t\t\t\t</button>\n\t\t\t\t<h3 class=\"ame-ac-section-title\" data-bind=\"css: {'ame-ac-has-breadcrumbs': (breadcrumbText() !== '')}\">\n\t\t\t\t    <!-- ko if: breadcrumbText -->\n\t\t\t\t\t\t<span class=\"ame-ac-section-breadcrumbs\" data-bind=\"text: breadcrumbText\"></span>\n\t\t\t\t\t<!-- /ko -->\n\t\t\t\t\t<span class=\"ame-ac-section-own-title\" data-bind=\"text: title\"></span>\t\t\t\t\n\t\t\t\t</h3>\n\t\t\t</div>\n\t\t</li>\n\t\t<!-- ko foreach: childComponents -->\n\t\t\t<!-- ko component: $data --><!-- /ko -->\n\t\t<!-- /ko -->\n\t</ul>\n`);\r\n//# sourceMappingURL=ame-ac-section.js.map", "import { createComponentConfig, KoStandaloneControl } from '../../../pro-customizables/ko-components/control-base.js';\r\nclass AmeAcSeparator extends KoStandaloneControl {\r\n    constructor(params, $element) {\r\n        super(params, $element);\r\n    }\r\n}\r\nexport default createComponentConfig(AmeAcSeparator, `\n\t<li class=\"ame-ac-control ame-ac-separator\"></li>\n`);\r\n//# sourceMappingURL=ame-ac-separator.js.map", "import { createRendererComponentConfig, KoRendererViewModel } from '../../../pro-customizables/ko-components/control-base.js';\r\nimport { AmeCustomizable } from '../../../pro-customizables/assets/customizable.js';\r\nvar Section = AmeCustomizable.Section;\r\nclass AmeAcStructure extends KoRendererViewModel {\r\n    constructor(params, $element) {\r\n        var _a;\r\n        super(params, $element);\r\n        this.allNavigationSections = [];\r\n        const rootSection = new Section({\r\n            t: 'section',\r\n            id: 'structure-root',\r\n            title: (_a = this.structure.title) !== null && _a !== void 0 ? _a : 'Root',\r\n        }, this.structure.children);\r\n        //Recursively collect all navigable sections. Don't include content\r\n        //sections: their parents will output them, not this component.\r\n        function collectChildSections(section, accumulator = []) {\r\n            if (section.preferredRole === 'navigation') {\r\n                accumulator.push(section);\r\n            }\r\n            for (const child of section.children) {\r\n                if (child instanceof Section) {\r\n                    collectChildSections(child, accumulator);\r\n                }\r\n            }\r\n            return accumulator;\r\n        }\r\n        this.allNavigationSections = collectChildSections(rootSection);\r\n        //Give the breadcrumb list to each section, if available.\r\n        if (typeof params.breadcrumbs !== 'undefined') {\r\n            for (const section of this.allNavigationSections) {\r\n                section.componentParams.breadcrumbs = params.breadcrumbs;\r\n            }\r\n        }\r\n    }\r\n}\r\nexport default createRendererComponentConfig(AmeAcStructure, `\n\t<!-- ko foreach: allNavigationSections -->\n\t\t<!-- ko component: {name: 'ame-ac-section', params: $data.getComponentParams()} --><!-- /ko -->\n\t<!-- /ko -->\n`);\r\n//# sourceMappingURL=ame-ac-structure.js.map", "import { createComponentConfig, KoStandaloneControl } from '../../../pro-customizables/ko-components/control-base.js';\r\nclass AmeAcValidationErrors extends KoStandaloneControl {\r\n    constructor(params, $element) {\r\n        super(params, $element);\r\n        if (typeof params.errors !== 'undefined') {\r\n            if (Array.isArray(params.errors)) {\r\n                this.errors = params.errors;\r\n            }\r\n            else if (ko.isObservable(params.errors)) {\r\n                this.errors = params.errors;\r\n            }\r\n            else {\r\n                throw new Error('The \"errors\" parameter must be an array or an observable array.');\r\n            }\r\n        }\r\n        else {\r\n            console.log('Params:', params);\r\n            throw new Error('The \"errors\" parameter is required for the AmeAcValidationErrors component.');\r\n        }\r\n    }\r\n}\r\nexport default createComponentConfig(AmeAcValidationErrors, `\n\t<ul class=\"ame-ac-ve-list\" data-bind=\"foreach: errors\">\n\t\t<li class=\"notice notice-error ame-ac-validation-error\">\n\t\t\t<span class=\"ame-ac-ve-message\" data-bind=\"text: $data[1].message, attr: {title: $data[1].code}\"></span>\n\t\t</li>\n\t</ul>\n`);\r\n//# sourceMappingURL=ame-ac-validation-errors.js.map", "'use strict';\r\nimport { createControlComponentConfig, KoStandaloneControl } from '../control-base.js';\r\nimport { LazyPopupSliderAdapter } from '../lazy-popup-slider-adapter.js';\r\nconst allDimensionKeys = [\r\n    'top', 'bottom', 'left', 'right',\r\n    'topLeft', 'topRight', 'bottomLeft', 'bottomRight'\r\n];\r\nfunction isDimensionKey(key) {\r\n    return allDimensionKeys.includes(key);\r\n}\r\nconst DefaultDimensionsInOrder = [\r\n    ['top', 'Top'],\r\n    ['bottom', 'Bottom'],\r\n    ['left', 'Left'],\r\n    ['right', 'Right'],\r\n];\r\nconst SideDimensions = ['top', 'bottom', 'left', 'right'];\r\nconst SymmetricDimensionMap = {\r\n    'vertical': ['top', 'bottom'],\r\n    'horizontal': ['left', 'right'],\r\n};\r\nfunction isSymmetricDimensionKey(key) {\r\n    return SymmetricDimensionMap.hasOwnProperty(key);\r\n}\r\nlet nextId = 0;\r\nclass AmeBoxDimensions extends KoStandaloneControl {\r\n    constructor(params, $element) {\r\n        super(params, $element);\r\n        this.inputIdPrefix = '_ame-box-dimensions-c-input-' + (nextId++);\r\n        this.unitElementId = '_ame-box-dimensions-c-unit-' + (nextId++);\r\n        this.wrapperAttributes = {};\r\n        if ((typeof params.id === 'string') && (params.id !== '')) {\r\n            this.wrapperAttributes['id'] = '_ame-box-dimensions-w-' + params.id;\r\n        }\r\n        if ((typeof params['dimensionNames'] !== 'undefined') && Array.isArray(params['dimensionNames'])) {\r\n            this.dimensionsInOrder = params['dimensionNames'];\r\n        }\r\n        else {\r\n            this.dimensionsInOrder = DefaultDimensionsInOrder;\r\n        }\r\n        //Make observable proxies for the individual dimension settings.\r\n        const temp = {};\r\n        for (const [dimensionKey, dimensionName] of this.dimensionsInOrder) {\r\n            const setting = this.settings['value.' + dimensionKey];\r\n            if (!setting || (typeof setting !== 'object')) {\r\n                throw new Error(`Missing setting for the \"${dimensionName}\" side.`);\r\n            }\r\n            temp[dimensionKey] = ko.computed({\r\n                read: () => {\r\n                    return setting.value();\r\n                },\r\n                write: (newValue) => {\r\n                    if (newValue === '') {\r\n                        newValue = null;\r\n                    }\r\n                    setting.value(newValue);\r\n                },\r\n                deferEvaluation: true,\r\n            }).extend({ 'ameNumericInput': true });\r\n        }\r\n        this.dimensions = temp;\r\n        //Similarly, make an observable for the unit setting.\r\n        const unitSetting = this.settings['value.unit'];\r\n        if (!unitSetting || (typeof unitSetting !== 'object')) {\r\n            throw new Error('Missing setting for the unit.');\r\n        }\r\n        this.unitSetting = unitSetting;\r\n        const defaultDropdownOptions = {\r\n            options: [],\r\n            optionsText: 'text',\r\n            optionsValue: 'value'\r\n        };\r\n        if (params.unitDropdownOptions && (typeof params.unitDropdownOptions === 'object')) {\r\n            const unitDropdownOptions = params.unitDropdownOptions;\r\n            this.unitDropdownOptions = {\r\n                options: unitDropdownOptions['options'] || defaultDropdownOptions.options,\r\n                optionsText: unitDropdownOptions['optionsText'] || defaultDropdownOptions.optionsText,\r\n                optionsValue: unitDropdownOptions['optionsValue'] || defaultDropdownOptions.optionsValue,\r\n            };\r\n        }\r\n        else {\r\n            this.unitDropdownOptions = defaultDropdownOptions;\r\n        }\r\n        this.isLinkActive = ko.observable(false);\r\n        //Enable the link button by default if all dimensions are equal. Exception: null values.\r\n        //Dimensions can have different defaults, so null doesn't necessarily mean that they\r\n        //are actually equal.\r\n        const firstKey = Object.keys(this.dimensions)[0];\r\n        const firstValue = this.dimensions[firstKey]();\r\n        if ((firstValue !== null) && (firstValue !== '')) {\r\n            let areAllDimensionsEqual = true;\r\n            for (const [dimensionKey] of this.dimensionsInOrder) {\r\n                if (this.dimensions[dimensionKey]() !== firstValue) {\r\n                    areAllDimensionsEqual = false;\r\n                    break;\r\n                }\r\n            }\r\n            this.isLinkActive(areAllDimensionsEqual);\r\n        }\r\n        //When \"link\" mode is enabled, keep all dimensions in sync.\r\n        let isUpdatingAllDimensions = false; //Prevent infinite loops.\r\n        const updateAllDimensions = (newValue) => {\r\n            if (!isUpdatingAllDimensions && this.isLinkActive()) {\r\n                isUpdatingAllDimensions = true;\r\n                newValue = this.normalizeValue(newValue);\r\n                for (const observable of Object.values(this.dimensions)) {\r\n                    observable(newValue);\r\n                }\r\n                isUpdatingAllDimensions = false;\r\n            }\r\n        };\r\n        for (const dimensionKey of Object.keys(this.dimensions)) {\r\n            this.dimensions[dimensionKey].subscribe(updateAllDimensions);\r\n        }\r\n        //In \"symmetric\" mode, the top/bottom and left/right dimensions are always equal.\r\n        //The control will only show \"vertical\" and \"horizontal\" inputs.\r\n        this.symmetricModeEnabled = ko.observable(this.decideSymmetricMode(params));\r\n        //Create computed observables for the \"vertical\" and \"horizontal\" dimensions.\r\n        this.symmetricValues = {};\r\n        for (const name in SymmetricDimensionMap) {\r\n            if (!isSymmetricDimensionKey(name) || !SymmetricDimensionMap.hasOwnProperty(name)) {\r\n                continue;\r\n            }\r\n            const sides = SymmetricDimensionMap[name];\r\n            this.symmetricValues[name] = ko.computed({\r\n                read: () => {\r\n                    if (this.symmetricModeEnabled()) {\r\n                        return this.dimensions[sides[0]]();\r\n                    }\r\n                    else {\r\n                        return null;\r\n                    }\r\n                },\r\n                write: (newValue) => {\r\n                    if (this.symmetricModeEnabled()) {\r\n                        newValue = this.normalizeValue(newValue);\r\n                        for (const side of sides) {\r\n                            this.dimensions[side](newValue);\r\n                        }\r\n                    }\r\n                },\r\n                deferEvaluation: true\r\n            }).extend({ 'ameNumericInput': true });\r\n        }\r\n        //The control displays a different set of inputs depending on the current mode.\r\n        this.inputsInOrder = ko.pureComputed(() => {\r\n            let result;\r\n            if (this.symmetricModeEnabled()) {\r\n                result = [\r\n                    ['vertical', 'Vertical'],\r\n                    ['horizontal', 'Horizontal'],\r\n                ];\r\n            }\r\n            else {\r\n                result = this.dimensionsInOrder;\r\n            }\r\n            return result;\r\n        });\r\n        let sliderOptions = {\r\n            'positionParentSelector': '.ame-single-box-dimension',\r\n            'verticalOffset': -2,\r\n        };\r\n        if (typeof params.popupSliderWithin === 'string') {\r\n            sliderOptions.positionWithinClosest = params.popupSliderWithin;\r\n        }\r\n        this.sliderAdapter = new LazyPopupSliderAdapter(params.sliderRanges ? params.sliderRanges : null, '.ame-box-dimensions-control', 'input.ame-box-dimensions-input', sliderOptions);\r\n    }\r\n    get classes() {\r\n        return ['ame-box-dimensions-control', ...super.classes];\r\n    }\r\n    //noinspection JSUnusedGlobalSymbols -- Used in the template.\r\n    /**\r\n     * Get an observable for a specific dimension or a pair of dimensions.\r\n     *\r\n     * Unfortunately, Knockout doesn't seem to support nested indexed accessors\r\n     * like \"dimensions[$data[0]]\", so we have to use a method instead.\r\n     */\r\n    getInputObservable(key) {\r\n        if (this.symmetricModeEnabled() && isSymmetricDimensionKey(key)) {\r\n            return this.symmetricValues[key];\r\n        }\r\n        if (isDimensionKey(key)) {\r\n            return this.dimensions[key];\r\n        }\r\n        throw new Error('Invalid input key for the current mode: ' + key);\r\n    }\r\n    getInputIdFor(key) {\r\n        return this.inputIdPrefix + '-' + key;\r\n    }\r\n    // noinspection JSUnusedGlobalSymbols -- Used in the template.\r\n    getInputAttributes(key) {\r\n        return {\r\n            id: this.getInputIdFor(key),\r\n            'data-unit-element-id': this.unitElementId,\r\n            'data-ame-box-dimension': key,\r\n        };\r\n    }\r\n    // noinspection JSUnusedGlobalSymbols -- Used in the template.\r\n    getSettingFor(key) {\r\n        const settingName = 'value.' + key;\r\n        if (settingName in this.settings) {\r\n            return this.settings[settingName];\r\n        }\r\n        if (this.symmetricModeEnabled() && isSymmetricDimensionKey(key)) {\r\n            for (const dimension of SymmetricDimensionMap[key]) {\r\n                //Since both symmetric dimensions are always equal, we can use\r\n                //either of the two settings.\r\n                const settingName = 'value.' + dimension;\r\n                if (settingName in this.settings) {\r\n                    return this.settings[dimension];\r\n                }\r\n            }\r\n        }\r\n        return null;\r\n    }\r\n    // noinspection JSUnusedGlobalSymbols -- Actually used in the template.\r\n    toggleLink() {\r\n        this.isLinkActive(!this.isLinkActive());\r\n        //When enabling \"link\" mode, fill all inputs with the same value.\r\n        //Use the first non-empty value.\r\n        if (this.isLinkActive()) {\r\n            let firstValue = null;\r\n            for (const dimensionObservable of Object.values(this.dimensions)) {\r\n                const value = dimensionObservable();\r\n                if ((value !== null) && (value !== '')) {\r\n                    firstValue = value;\r\n                    break;\r\n                }\r\n            }\r\n            if (firstValue !== null) {\r\n                firstValue = this.normalizeValue(firstValue);\r\n                for (const dimensionObservable of Object.values(this.dimensions)) {\r\n                    dimensionObservable(firstValue);\r\n                }\r\n            }\r\n        }\r\n    }\r\n    normalizeValue(value) {\r\n        if (value === null) {\r\n            return null;\r\n        }\r\n        //Convert strings to numbers, and invalid strings to null.\r\n        if (typeof value === 'string') {\r\n            value = parseFloat(value);\r\n            if (isNaN(value)) {\r\n                return null;\r\n            }\r\n        }\r\n        return value;\r\n    }\r\n    /**\r\n     * Determine whether the control should be in \"symmetric\" mode.\r\n     */\r\n    decideSymmetricMode(componentParams) {\r\n        //This mode is off by default and can be enabled by setting the \"symmetricMode\" parameter.\r\n        let enableMode = (typeof componentParams['symmetricMode'] !== 'undefined')\r\n            ? (!!componentParams['symmetricMode'])\r\n            : false;\r\n        if (!enableMode) {\r\n            return false;\r\n        }\r\n        //Symmetric mode can't be enabled if the control doesn't have all side dimensions.\r\n        const hasAllSideDimensions = SideDimensions.every((key) => {\r\n            return (key in this.dimensions);\r\n        });\r\n        if (!hasAllSideDimensions) {\r\n            return false;\r\n        }\r\n        //It also can only be enabled if top/bottom and left/right dimensions are equal.\r\n        return ((this.dimensions['top']() === this.dimensions['bottom']())\r\n            && (this.dimensions['left']() === this.dimensions['right']()));\r\n    }\r\n}\r\nexport default createControlComponentConfig(AmeBoxDimensions, `\n\t<fieldset data-bind=\"class: classString, enable: isEnabled, style: styles, attr: wrapperAttributes\"\n\t          data-ame-is-component=\"1\">\n\t\t<!-- ko foreach: inputsInOrder -->\n\t\t\t<div data-bind=\"class: ('ame-single-box-dimension ame-box-dimension-' + $data[0])\">\n\t\t\t\t<input type=\"text\" inputmode=\"numeric\" maxlength=\"20\" pattern=\"\\\\s*-?[0-9]+(?:[.,]\\\\d*)?\\\\s*\" \n\t\t\t\t\tdata-bind=\"value: $parent.getInputObservable($data[0]), valueUpdate: 'input',\n\t\t\t\t\tattr: $component.getInputAttributes($data[0]),\n\t\t\t\t\tclass: ('ame-small-number-input ame-box-dimensions-input ame-box-dimensions-input-' + $data[0]),\n\t\t\t\t\tenable: $component.isEnabled,\n\t\t\t\t\tclick: $component.sliderAdapter.handleKoClickEvent,\n\t\t\t\t\tameValidationErrorClass: $component.getSettingFor($data[0])\" />\t\t\t\t\n\t\t\t\t<label data-bind=\"attr: {'for': $component.getInputIdFor($data[0])}\" \n\t\t\t\t\tclass=\"ame-box-dimension-label\"><span\n\t\t\t\t\tdata-bind=\"text: $data[1]\" class=\"ame-box-dimension-label-text\"></span></label>\n\t\t\t</div>\n\t\t<!-- /ko -->\n\t\t<ame-unit-dropdown params=\"optionData: unitDropdownOptions, settings: {value: unitSetting},\n\t\t\tclasses: ['ame-box-dimensions-unit-selector'],\n\t\t\tid: unitElementId\"></ame-unit-dropdown>\n\t\t<button class=\"button button-secondary ame-box-dimensions-link-button hide-if-no-js\"\n\t\t\ttitle=\"Link values\" data-bind=\"enable: isEnabled, css: {'active': isLinkActive}, \n\t\t\t\tclick: $component.toggleLink.bind($component)\"><span class=\"dashicons dashicons-admin-links\"></span></button>\n\t</fieldset>\n`);\r\n//# sourceMappingURL=ame-box-dimensions.js.map", "import { KoStandaloneControl } from '../control-base.js';\r\nexport class ChoiceControlOption {\r\n    constructor(data) {\r\n        this.value = data.value;\r\n        this.label = data.label;\r\n        this.description = data.description || '';\r\n        this.enabled = (typeof data.enabled === 'undefined') || data.enabled;\r\n        this.icon = data.icon || '';\r\n    }\r\n}\r\nexport class AmeChoiceControl extends KoStandaloneControl {\r\n    constructor(params, $element) {\r\n        super(params, $element);\r\n        this.options = ko.observableArray([]);\r\n        if ((typeof params['options'] !== 'undefined') && Array.isArray(params.options)) {\r\n            this.options(params.options.map((optionData) => new ChoiceControlOption(optionData)));\r\n        }\r\n    }\r\n}\r\n//# sourceMappingURL=ame-choice-control.js.map", "'use strict';\r\nimport { createControlComponentConfig, KoStandaloneControl } from '../control-base.js';\r\n/**\r\n * Code editor control with syntax highlighting.\r\n *\r\n * This control uses the custom Knockout binding \"ameCodeMirror\" to do the heavy\r\n * lifting. The binding is defined in ko-extensions.ts.\r\n *\r\n * Note: The user can disable syntax highlighting. In that case, this control\r\n * should behave like a normal textarea.\r\n */\r\nclass AmeCodeEditor extends KoStandaloneControl {\r\n    constructor(params, $element) {\r\n        super(params, $element);\r\n        if ((typeof params.editorSettings === 'object') && (params.editorSettings !== null)) {\r\n            this.editorSettings = params.editorSettings;\r\n        }\r\n        else {\r\n            this.editorSettings = false;\r\n        }\r\n    }\r\n}\r\nexport default createControlComponentConfig(AmeCodeEditor, `\n\t<div class=\"ame-code-editor-control-wrap\">  \n\t\t<textarea data-bind=\"attr: inputAttributes, value: valueProxy, \n\t\t\tclass: inputClassString, ameCodeMirror: editorSettings\" \n\t\t\tclass=\"large-text\" cols=\"50\" rows=\"10\"></textarea>\n\t</div>\n\t<!-- ko if: (description) -->\n\t\t<!-- ko component: {name: 'ame-sibling-description', params: {description: description}} --><!-- /ko -->\n\t<!-- /ko -->\n`);\r\n//# sourceMappingURL=ame-code-editor.js.map", "import { createControlComponentConfig, KoStandaloneControl } from '../control-base.js';\r\n/**\r\n * A wrapper for the WordPress color picker.\r\n *\r\n * Note that the custom 'ameColorPicker' binding must be available when this component\r\n * is used. You must enqueue the 'ame-ko-extensions' script for this to work.\r\n */\r\nclass AmeColorPicker extends KoStandaloneControl {\r\n    constructor(params, $element) {\r\n        super(params, $element);\r\n    }\r\n    koDescendantsComplete(node) {\r\n        //Make the color picker input visible. Its visibility is set to hidden by default.\r\n        if (node.nodeType === Node.COMMENT_NODE) {\r\n            //The component was bound to a comment node. The real element\r\n            //should be the next non-comment sibling.\r\n            let nextElement;\r\n            do {\r\n                nextElement = node.nextElementSibling;\r\n            } while (nextElement && (nextElement.nodeType === Node.COMMENT_NODE));\r\n            if (!nextElement) {\r\n                return; //This should never happen.\r\n            }\r\n            node = nextElement;\r\n        }\r\n        if (!node || (node.nodeType !== Node.ELEMENT_NODE)) {\r\n            return; //This should never happen.\r\n        }\r\n        const $picker = jQuery(node);\r\n        //This should be a .wp-picker-container element that contains an input.\r\n        const $input = $picker.find('input.ame-color-picker');\r\n        if ($input.length > 0) {\r\n            $input.css('visibility', 'visible');\r\n        }\r\n    }\r\n    get classes() {\r\n        return ['ame-color-picker', 'ame-color-picker-component', ...super.classes];\r\n    }\r\n}\r\nexport default createControlComponentConfig(AmeColorPicker, `\n\t<input type=\"text\" style=\"visibility: hidden\" data-bind=\"ameColorPicker: valueProxy, \n\t\tclass: classString, attr: inputAttributes\">\n`);\r\n//# sourceMappingURL=ame-color-picker.js.map", "/*\r\n * This utility module imports all the base Knockout components and exports\r\n * a function that can be used to register the components with Knockout.\r\n */\r\nimport ameBoxDimensions from './ame-box-dimensions/ame-box-dimensions.js';\r\nimport ameColorPicker from './ame-color-picker/ame-color-picker.js';\r\nimport ameFontStylePicker from './ame-font-style-picker/ame-font-style-picker.js';\r\nimport ameImageSelector from './ame-image-selector/ame-image-selector.js';\r\nimport ameNumberInput from './ame-number-input/ame-number-input.js';\r\nimport ameNestedDescription from './ame-nested-description/ame-nested-description.js';\r\nimport ameRadioButtonBar from './ame-radio-button-bar/ame-radio-button-bar.js';\r\nimport ameRadioGroup from './ame-radio-group/ame-radio-group.js';\r\nimport ameSelectBox from './ame-select-box/ame-select-box.js';\r\nimport ameSiblingDescription from './ame-sibling-description/ame-sibling-description.js';\r\nimport ameStaticHtml from './ame-static-html/ame-static-html.js';\r\nimport ameTextInput from './ame-text-input/ame-text-input.js';\r\nimport ameToggleCheckbox from './ame-toggle-checkbox/ame-toggle-checkbox.js';\r\nimport ameUnitDropdown from './ame-unit-dropdown/ame-unit-dropdown.js';\r\nimport ameWpEditor from './ame-wp-editor/ame-wp-editor.js';\r\nimport ameHorizontalSeparator from './ame-horizontal-separator/ame-horizontal-separator.js';\r\nimport ameCodeEditor from './ame-code-editor/ame-code-editor.js';\r\nlet componentsRegistered = false;\r\n/**\r\n * Register the base Knockout components that are part of AME.\r\n *\r\n * It's safe to call this function multiple times. It will only register the components once.\r\n */\r\nexport function registerBaseComponents() {\r\n    if (componentsRegistered) {\r\n        return;\r\n    }\r\n    ko.components.register('ame-box-dimensions', ameBoxDimensions);\r\n    ko.components.register('ame-color-picker', ameColorPicker);\r\n    ko.components.register('ame-font-style-picker', ameFontStylePicker);\r\n    ko.components.register('ame-image-selector', ameImageSelector);\r\n    ko.components.register('ame-number-input', ameNumberInput);\r\n    ko.components.register('ame-nested-description', ameNestedDescription);\r\n    ko.components.register('ame-radio-button-bar', ameRadioButtonBar);\r\n    ko.components.register('ame-radio-group', ameRadioGroup);\r\n    ko.components.register('ame-select-box', ameSelectBox);\r\n    ko.components.register('ame-sibling-description', ameSiblingDescription);\r\n    ko.components.register('ame-static-html', ameStaticHtml);\r\n    ko.components.register('ame-text-input', ameTextInput);\r\n    ko.components.register('ame-toggle-checkbox', ameToggleCheckbox);\r\n    ko.components.register('ame-unit-dropdown', ameUnitDropdown);\r\n    ko.components.register('ame-wp-editor', ameWpEditor);\r\n    ko.components.register('ame-horizontal-separator', ameHorizontalSeparator);\r\n    ko.components.register('ame-code-editor', ameCodeEditor);\r\n    componentsRegistered = true;\r\n}\r\n//# sourceMappingURL=ame-components.js.map", "import { KoComponentViewModel } from '../control-base.js';\r\n/**\r\n * Base class for description components.\r\n */\r\nexport class AmeDescriptionComponent extends KoComponentViewModel {\r\n    constructor(params, $element) {\r\n        super(params, $element);\r\n        this.description = params.description || '';\r\n    }\r\n}\r\n//# sourceMappingURL=ame-description.js.map", "import { createControlComponentConfig, KoStandaloneControl } from '../control-base.js';\r\n//Note: Font style picker CSS is already included in the main 'controls.scss' file\r\n//and won't be duplicated or included here. Instead, load that stylesheet when\r\n//using any control components.\r\n/**\r\n * Font style options that can be selected in the picker component.\r\n *\r\n * Regrettably, these are duplicated from the PHP version of the control.\r\n * Once browsers support importing JSON files, we can move this to a separate\r\n * file and use that file in both places.\r\n */\r\nconst fontStyleOptions = {\r\n    \"font-style\": [\r\n        {\r\n            \"value\": null,\r\n            \"text\": \"Default font style\",\r\n            \"label\": \"&mdash;\"\r\n        },\r\n        {\r\n            \"value\": \"italic\",\r\n            \"text\": \"Italic\",\r\n            \"label\": \"<span class=\\\"dashicons dashicons-editor-italic\\\"></span>\"\r\n        }\r\n    ],\r\n    \"text-transform\": [\r\n        {\r\n            \"value\": null,\r\n            \"text\": \"Default letter case\",\r\n            \"label\": \"&mdash;\"\r\n        },\r\n        {\r\n            \"value\": \"uppercase\",\r\n            \"text\": \"Uppercase\",\r\n            \"label\": {\r\n                'text-transform': 'uppercase'\r\n            }\r\n        },\r\n        {\r\n            \"value\": \"lowercase\",\r\n            \"text\": \"Lowercase\",\r\n            \"label\": {\r\n                'text-transform': 'lowercase'\r\n            }\r\n        },\r\n        {\r\n            \"value\": \"capitalize\",\r\n            \"text\": \"Capitalize each word\",\r\n            \"label\": {\r\n                'text-transform': 'capitalize'\r\n            }\r\n        }\r\n    ],\r\n    \"font-variant\": [\r\n        {\r\n            \"value\": null,\r\n            \"text\": \"Default font variant\",\r\n            \"label\": \"&mdash;\"\r\n        },\r\n        {\r\n            \"value\": \"small-caps\",\r\n            \"text\": \"Small caps\",\r\n            \"label\": {\r\n                'font-variant': 'small-caps'\r\n            }\r\n        }\r\n    ],\r\n    \"text-decoration\": [\r\n        {\r\n            \"value\": null,\r\n            \"text\": \"Default text decoration\",\r\n            \"label\": \"&mdash;\"\r\n        },\r\n        {\r\n            \"value\": \"underline\",\r\n            \"text\": \"Underline\",\r\n            \"label\": \"<span class=\\\"dashicons dashicons-editor-underline\\\"></span>\"\r\n        },\r\n        {\r\n            \"value\": \"line-through\",\r\n            \"text\": \"Strikethrough\",\r\n            \"label\": \"<span class=\\\"dashicons dashicons-editor-strikethrough\\\"></span>\"\r\n        }\r\n    ]\r\n};\r\n//Generate label HTML for options that don't have it yet.\r\nfunction makeFontSample(styles) {\r\n    let styleString = '';\r\n    for (const [property, value] of Object.entries(styles)) {\r\n        styleString += `${property}: ${value};`;\r\n    }\r\n    return `<span class=\"ame-font-sample\" style=\"${styleString}\">ab</span>`;\r\n}\r\nlet flattenedOptions = [];\r\nfor (const [property, options] of Object.entries(fontStyleOptions)) {\r\n    options.forEach((option) => {\r\n        //Skip null values. They're used to indicate the default option,\r\n        //and we don't need those in the Knockout version of this control.\r\n        if (option.value === null) {\r\n            return;\r\n        }\r\n        let labelString;\r\n        if (typeof option.label === 'object') {\r\n            labelString = makeFontSample(option.label);\r\n        }\r\n        else {\r\n            labelString = option.label;\r\n        }\r\n        flattenedOptions.push({\r\n            'value': option.value,\r\n            'text': option.text || '',\r\n            'property': property,\r\n            'label': labelString\r\n        });\r\n    });\r\n}\r\nclass AmeFontStylePicker extends KoStandaloneControl {\r\n    constructor(params, $element) {\r\n        super(params, $element);\r\n        this.options = flattenedOptions;\r\n    }\r\n    get classes() {\r\n        return ['ame-font-style-control', ...super.classes];\r\n    }\r\n    // noinspection JSUnusedGlobalSymbols -- Used in the template, below.\r\n    isOptionSelected(property, value) {\r\n        if (this.settings.hasOwnProperty(property)) {\r\n            return (this.settings[property].value() === value);\r\n        }\r\n        return false;\r\n    }\r\n    // noinspection JSUnusedGlobalSymbols -- Used in the template.\r\n    toggleOption(property, value) {\r\n        if (!this.settings.hasOwnProperty(property)) {\r\n            return;\r\n        }\r\n        const targetSetting = this.settings[property];\r\n        if (targetSetting.value() === value) {\r\n            //When the user clicks on the currently selected option, reset it to the default.\r\n            targetSetting.tryUpdate(null);\r\n        }\r\n        else {\r\n            //Otherwise, set the new value.\r\n            targetSetting.tryUpdate(value);\r\n        }\r\n    }\r\n}\r\n//Note: This weird spacing in the template string is intentional. It's used to\r\n//remove whitespace nodes from the DOM, which would otherwise slightly change\r\n//the layout of the control.\r\nexport default createControlComponentConfig(AmeFontStylePicker, `\n\t<fieldset data-ame-is-component=\"1\" data-bind=\"class: classString, style: styles\">\n\t\t<!-- \n\t\tko foreach: options \n\t\t--><label class=\"ame-font-style-control-choice\" data-bind=\"attr: {title: (text || '')}\"><!-- \n\t\t\tko if: text \n\t\t\t--><span class=\"screen-reader-text\" data-bind=\"text: text\"></span><!-- \n\t\t\t/ko \n\t\t--><span class=\"button button-secondary ame-font-style-control-choice-label\" \n\t\t\t\tdata-bind=\"html: label, css: { 'active': $component.isOptionSelected(property, value) },\n\t\t\t\tclick: $component.toggleOption.bind($component, property, value)\"></span></label><!-- \n\t\t/ko -->\n\t</fieldset>\n`);\r\n//# sourceMappingURL=ame-font-style-picker.js.map", "import { createControlComponentConfig, KoStandaloneControl } from '../control-base.js';\r\nclass AmeHorizontalSeparator extends KoStandaloneControl {\r\n    constructor(params, $element) {\r\n        super(params, $element);\r\n    }\r\n}\r\nexport default createControlComponentConfig(AmeHorizontalSeparator, `\n\t<div class=\"ame-horizontal-separator\"></div>\n`);\r\n//# sourceMappingURL=ame-horizontal-separator.js.map", "'use strict';\r\nimport { createControlComponentConfig, KoStandaloneControl } from '../control-base.js';\r\n/**\r\n * Image selector control.\r\n *\r\n * This implementation hands off the work to the existing AmeImageSelectorApi.ImageSelector\r\n * class to avoid duplicating the effort. That class is not a module because it is also\r\n * used for the more progressive-enhancement-y PHP-rendered controls, so we can't import it.\r\n */\r\nclass AmeImageSelector extends KoStandaloneControl {\r\n    constructor(params, $element) {\r\n        super(params, $element);\r\n        this.selectorInstance = null;\r\n        //Verify that our dependencies are available.\r\n        if (typeof AmeImageSelectorApi === 'undefined') {\r\n            throw new Error('AmeImageSelectorApi is not available. Remember to enqueue \"ame-image-selector-control-v2\".');\r\n        }\r\n        if (typeof AmeImageSelectorApi.ImageSelector === 'undefined') {\r\n            throw new Error('AmeImageSelectorApi.ImageSelector is not available. This is probably a bug.');\r\n        }\r\n        this.externalUrlsAllowed = !!params.externalUrlsAllowed;\r\n        this.canSelectMedia = !!params.canSelectMedia;\r\n        this.imageProxy = this.settings.value.value;\r\n    }\r\n    get classes() {\r\n        return [\r\n            'ame-image-selector-v2',\r\n            ...super.classes,\r\n        ];\r\n    }\r\n    koDescendantsComplete() {\r\n        const $container = this.findChild('.ame-image-selector-v2');\r\n        if ($container.length === 0) {\r\n            return;\r\n        }\r\n        this.selectorInstance = new AmeImageSelectorApi.ImageSelector($container, {\r\n            externalUrlsAllowed: this.externalUrlsAllowed,\r\n            canSelectMedia: this.canSelectMedia,\r\n        }, this.imageProxy());\r\n    }\r\n}\r\nexport default createControlComponentConfig(AmeImageSelector, `\n\t<div class=\"ame-image-selector-v2\" data-ame-is-component=\"1\" \n\t\tdata-bind=\"class: classString, ameObservableChangeEvents: { observable: imageProxy }\">\n\t\t<!-- The contents should be generated by the image selector API. -->\n\t</div>\n`);\r\n//# sourceMappingURL=ame-image-selector.js.map", "import { createComponentConfig } from '../control-base.js';\r\nimport { AmeDescriptionComponent } from '../ame-description/ame-description.js';\r\n/**\r\n * A simple component that displays the description of a UI element.\r\n *\r\n * Like AmeSiblingDescription, but intended to be rendered inside\r\n * the parent control or container, not as a sibling.\r\n */\r\nclass AmeNestedDescription extends AmeDescriptionComponent {\r\n}\r\nexport default createComponentConfig(AmeNestedDescription, `\n\t<br><span class=\"description\" data-bind=\"html: description\"></span>\t\n`);\r\n//# sourceMappingURL=ame-nested-description.js.map", "/// <reference path=\"../../../../customizables/assets/popup-slider.d.ts\" />\r\nimport { createControlComponentConfig, KoDependentControl } from '../control-base.js';\r\nimport { AmeCustomizable } from '../../assets/customizable.js';\r\nvar Control = AmeCustomizable.Control;\r\nexport class AmeNumberInput extends KoDependentControl {\r\n    constructor(params, $element) {\r\n        super(params, $element);\r\n        this.sliderRanges = null;\r\n        this.slider = null;\r\n        this.numericValue = this.valueProxy.extend({ 'ameNumericInput': true });\r\n        this.unitText = params.unitText || '';\r\n        this.hasUnitDropdown = params.hasUnitDropdown || false;\r\n        this.unitElementId = params.unitElementId || '';\r\n        if (this.hasUnitDropdown && params.unitDropdownOptions) {\r\n            this.unitDropdownOptions = {\r\n                options: params.unitDropdownOptions.options || [],\r\n                optionsText: params.unitDropdownOptions.optionsText || 'text',\r\n                optionsValue: params.unitDropdownOptions.optionsValue || 'value'\r\n            };\r\n        }\r\n        else {\r\n            this.unitDropdownOptions = null;\r\n        }\r\n        this.min = params.min || null;\r\n        this.max = params.max || null;\r\n        this.step = params.step || null;\r\n        if (params.sliderRanges) {\r\n            this.sliderRanges = params.sliderRanges;\r\n        }\r\n        this.popupSliderWithin = (typeof params.popupSliderWithin === 'string') ? params.popupSliderWithin : null;\r\n        this.inputClasses.unshift('ame-input-with-popup-slider', 'ame-number-input');\r\n    }\r\n    get classes() {\r\n        const classes = ['ame-number-input-control'];\r\n        if (this.sliderRanges !== null) {\r\n            classes.push('ame-container-with-popup-slider');\r\n        }\r\n        classes.push(...super.classes);\r\n        return classes;\r\n    }\r\n    get inputClasses() {\r\n        const classes = ['ame-input-with-popup-slider', 'ame-number-input'];\r\n        classes.push(...super.inputClasses);\r\n        return classes;\r\n    }\r\n    getAdditionalInputAttributes() {\r\n        let attributes = super.getAdditionalInputAttributes();\r\n        if (this.min !== null) {\r\n            attributes['min'] = this.min.toString();\r\n        }\r\n        if (this.max !== null) {\r\n            attributes['max'] = this.max.toString();\r\n        }\r\n        if (this.step !== null) {\r\n            attributes['step'] = this.step.toString();\r\n        }\r\n        if (this.unitElementId) {\r\n            attributes['data-unit-element-id'] = this.unitElementId;\r\n        }\r\n        return attributes;\r\n    }\r\n    // noinspection JSUnusedGlobalSymbols -- Used in the Knockout template in this same file.\r\n    showPopupSlider($data, event) {\r\n        if ((this.sliderRanges === null) || (typeof AmePopupSlider === 'undefined')) {\r\n            return;\r\n        }\r\n        //Some sanity checks.\r\n        if (!event.target) {\r\n            return;\r\n        }\r\n        const $input = jQuery(event.target);\r\n        if ($input.is(':disabled') || !$input.is('input')) {\r\n            return;\r\n        }\r\n        const $container = $input.closest('.ame-container-with-popup-slider');\r\n        if ($container.length < 1) {\r\n            return;\r\n        }\r\n        //Initialize the slider if it's not already initialized.\r\n        if (!this.slider) {\r\n            let sliderOptions = {};\r\n            if (this.popupSliderWithin) {\r\n                sliderOptions.positionWithinClosest = this.popupSliderWithin;\r\n            }\r\n            //In HTML, we would pass the range data as a \"data-slider-ranges\" attribute,\r\n            //but here we can just set the data directly.\r\n            $input.data('slider-ranges', this.sliderRanges);\r\n            this.slider = AmePopupSlider.createSlider($container, sliderOptions);\r\n        }\r\n        this.slider.showForInput($input);\r\n    }\r\n    getExpectedUiElementType() {\r\n        return Control;\r\n    }\r\n}\r\nexport default createControlComponentConfig(AmeNumberInput, `\n\t<fieldset data-bind=\"class: classString, enable: isEnabled\">\n\t\t<div data-bind=\"class: (hasUnitDropdown ? 'ame-input-group' : '')\">\n\t\t\t<input type=\"text\" inputmode=\"numeric\" maxlength=\"20\" pattern=\"\\\\s*-?[0-9]+(?:[.,]\\\\d*)?\\\\s*\"\n\t\t\t\t   data-bind=\"attr: inputAttributes, value: numericValue, valueUpdate: 'input', \n\t\t\t\t   class: inputClassString, enable: isEnabled, click: showPopupSlider.bind($component),\n\t\t\t\t   ameValidationErrorClass: settings.value\">\n\t\t\t\n\t\t\t<!-- ko if: hasUnitDropdown -->\n\t\t\t\t<ame-unit-dropdown params=\"optionData: unitDropdownOptions, settings: {value: settings.unit},\n\t\t\t\t\tclasses: ['ame-input-group-secondary', 'ame-number-input-unit'],\n\t\t\t\t\tid: unitElementId\"></ame-unit-dropdown>\n\t\t\t<!-- /ko -->\n\t\t\t<!-- ko if: (!hasUnitDropdown && unitText) -->\n\t\t\t\t<span class=\"ame-number-input-unit\" \n\t\t\t\t\t  data-bind=\"text: unitText, attr: {id: unitElementId, 'data-number-unit': unitText}\"></span>\n\t\t\t<!-- /ko -->\n\t\t</div>\n\t</fieldset>\t\n`);\r\n//# sourceMappingURL=ame-number-input.js.map", "'use strict';\r\nimport { createControlComponentConfig } from '../control-base.js';\r\nimport { AmeChoiceControl } from '../ame-choice-control/ame-choice-control.js';\r\nclass AmeRadioButtonBar extends AmeChoiceControl {\r\n    constructor(params, $element) {\r\n        super(params, $element);\r\n    }\r\n    get classes() {\r\n        return ['ame-radio-button-bar-control', ...super.classes];\r\n    }\r\n}\r\nexport default createControlComponentConfig(AmeRadioButtonBar, `\n\t<fieldset data-bind=\"class: classString, enable: isEnabled, style: styles\" data-ame-is-component=\"1\">\n\t\t<!-- ko foreach: options -->\n\t\t<label data-bind=\"attr: {title: description}\" class=\"ame-radio-bar-item\">\n\t\t\t<input type=\"radio\" data-bind=\"class: $component.inputClassString,\n\t\t\t\tchecked: $component.valueProxy, checkedValue: value, enable: $component.isEnabled,\n\t\t\t\tameObservableChangeEvents: true\">\n\t\t\t<span class=\"button ame-radio-bar-button\" data-bind=\"css: {'ame-rb-has-label' : label}\">\n\t\t\t\t<!-- ko if: (icon && (icon.indexOf('dashicons-') >= 0)) -->\n\t\t\t\t\t<span data-bind=\"class: 'dashicons ' + icon\"></span>\n\t\t\t\t<!-- /ko -->\n\t\t\t\t<!-- ko if: label -->\n\t\t\t\t\t<span class=\"ame-rb-label\" data-bind=\"text: label\"></span>\n\t\t\t\t<!-- /ko -->\n\t\t\t</span>\n\t\t</label>\n\t\t<!-- /ko -->\n\t</fieldset>\n`);\r\n//# sourceMappingURL=ame-radio-button-bar.js.map", "'use strict';\r\nimport { createControlComponentConfig } from '../control-base.js';\r\nimport { AmeChoiceControl } from '../ame-choice-control/ame-choice-control.js';\r\n// noinspection JSUnusedGlobalSymbols -- Enum keys like \"Paragraph\" are used when serializing wrapStyle in PHP.\r\nvar WrapStyle;\r\n(function (WrapStyle) {\r\n    WrapStyle[\"LineBreak\"] = \"br\";\r\n    WrapStyle[\"Paragraph\"] = \"p\";\r\n    WrapStyle[\"None\"] = \"\";\r\n})(WrapStyle || (WrapStyle = {}));\r\nfunction isWrapStyle(value) {\r\n    if (typeof value !== 'string') {\r\n        return false;\r\n    }\r\n    return (typeof WrapStyle[value] === 'string');\r\n}\r\nlet nextRadioGroupId = 1;\r\nclass AmeRadioGroup extends AmeChoiceControl {\r\n    constructor(params, $element) {\r\n        super(params, $element);\r\n        this.wrapStyle = WrapStyle.None;\r\n        this.childByValue = new Map();\r\n        if ((typeof params['valueChildIndexes'] === 'object') && Array.isArray(params.valueChildIndexes)) {\r\n            const children = ko.unwrap(this.inputChildren);\r\n            for (const [value, index] of params.valueChildIndexes) {\r\n                if (!children || !children[index]) {\r\n                    throw new Error('The \"' + this.label + '\" radio group has no children, but its valueChildIndexes'\r\n                        + ' requires child #' + index + ' to be associated with value \"' + value + '\".');\r\n                }\r\n                this.childByValue.set(value, children[index]);\r\n            }\r\n        }\r\n        this.wrapStyle = isWrapStyle(params.wrapStyle) ? WrapStyle[params.wrapStyle] : WrapStyle.None;\r\n        if (this.childByValue.size > 0) {\r\n            this.wrapStyle = WrapStyle.None;\r\n        }\r\n        this.radioInputPrefix = (typeof params.radioInputPrefix === 'string')\r\n            ? params.radioInputPrefix\r\n            : ('ame-rg-input-' + nextRadioGroupId++ + '-');\r\n    }\r\n    get classes() {\r\n        const result = ['ame-radio-group-component', ...super.classes];\r\n        if (this.childByValue.size > 0) {\r\n            result.push('ame-rg-has-nested-controls');\r\n        }\r\n        return result;\r\n    }\r\n    // noinspection JSUnusedGlobalSymbols -- Used in the template below.\r\n    getChoiceChild(value) {\r\n        return this.childByValue.get(value) || null;\r\n    }\r\n    // noinspection JSUnusedGlobalSymbols -- Used in the template.\r\n    /**\r\n     * Get the ID attribute for a radio input.\r\n     *\r\n     * Note: This must match the algorithm used by the PHP version of this control\r\n     * to work correctly with the BorderStyleSelector control that adds style samples\r\n     * to each choice and uses the ID to link them to the inputs (so that clicking\r\n     * the sample selects the option).\r\n     */\r\n    getRadioInputId(choice) {\r\n        let sanitizedValue = (choice.value !== null) ? choice.value.toString() : '';\r\n        //Emulate the sanitize_key() function from WordPress core.\r\n        sanitizedValue = sanitizedValue.toLowerCase().replace(/[^a-z0-9_\\-]/gi, '');\r\n        return this.radioInputPrefix + sanitizedValue;\r\n    }\r\n}\r\nconst choiceTemplate = `\n\t<label data-bind=\"class: 'ame-rg-option-label',\n\t\tcss: {'ame-rg-has-choice-child' : ($component.getChoiceChild(value) !== null)}\">\n\t\t<input type=\"radio\" data-bind=\"class: $component.inputClassString, \n\t\t\tchecked: $component.valueProxy, checkedValue: value, enable: $component.isEnabled,\n\t\t\tattr: {id: $component.getRadioInputId($data)}\">\n\t\t<span data-bind=\"html: label\"></span>\n\t\t<!-- ko if: description -->\n\t\t\t<!-- ko component: {name: 'ame-nested-description', params: {description: description}} --><!-- /ko -->\n\t\t<!-- /ko -->\n\t</label>\n`;\r\nexport default createControlComponentConfig(AmeRadioGroup, `\n\t<fieldset data-bind=\"class: classString, enable: isEnabled, style: styles\">\n\t\t<!-- ko foreach: options -->\n\t\t\t<!-- ko if: $component.wrapStyle === 'br' -->\n\t\t\t\t${choiceTemplate} <br>\n\t\t\t<!-- /ko -->\n\t\t\t<!-- ko if: $component.wrapStyle === 'p' -->\n\t\t\t\t<p>${choiceTemplate}</p>\n\t\t\t<!-- /ko -->\n\t\t\t<!-- ko if: $component.wrapStyle === '' -->\n\t\t\t\t${choiceTemplate}\n\t\t\t<!-- /ko -->\n\t\t\t<!-- ko with: $component.getChoiceChild(value) -->\n\t\t\t<span class=\"ame-rg-nested-control\" \n\t\t\t\tdata-bind=\"component: {name: component, params: getComponentParams()}\"></span>\n\t\t\t<!-- /ko -->\n\t\t<!-- /ko -->\n\t</fieldset>\n`);\r\n//# sourceMappingURL=ame-radio-group.js.map", "import { AmeChoiceControl } from '../ame-choice-control/ame-choice-control.js';\r\nimport { createControlComponentConfig } from '../control-base.js';\r\nclass AmeSelectBox extends AmeChoiceControl {\r\n    constructor(params, $element) {\r\n        super(params, $element);\r\n    }\r\n    get classes() {\r\n        return ['ame-select-box-control', ...super.classes];\r\n    }\r\n}\r\nexport default createControlComponentConfig(AmeSelectBox, `\n\t<select data-bind=\"class: classString, value: valueProxy, options: options,\n\t\toptionsValue: 'value', optionsText: 'label', enable: isEnabled, attr: inputAttributes\"></select>\n\t<!-- ko if: (description) -->\n\t\t<!-- ko component: {name: 'ame-sibling-description', params: {description: description}} --><!-- /ko -->\n\t<!-- /ko -->\t\n`);\r\n//# sourceMappingURL=ame-select-box.js.map", "import { createComponentConfig } from '../control-base.js';\r\nimport { AmeDescriptionComponent } from '../ame-description/ame-description.js';\r\n/**\r\n * A simple component that displays the description of a UI element.\r\n *\r\n * This should be rendered as a sibling of the UI element's component,\r\n * typically immediately after it.\r\n *\r\n * Caution: HTML is allowed in the description.\r\n */\r\nclass AmeSiblingDescription extends AmeDescriptionComponent {\r\n}\r\nexport default createComponentConfig(AmeSiblingDescription, `\n\t<p class=\"description\" data-bind=\"html: description\"></p>\t\n`);\r\n//# sourceMappingURL=ame-sibling-description.js.map", "import { createControlComponentConfig, KoStandaloneControl } from '../control-base.js';\r\nclass AmeStaticHtml extends KoStandaloneControl {\r\n    constructor(params, $element) {\r\n        super(params, $element);\r\n        this.containerType = 'span';\r\n        this.htmlContent = (typeof params.html === 'string') ? params.html : '';\r\n        if (typeof params.container === 'string') {\r\n            this.containerType = params.container;\r\n        }\r\n    }\r\n}\r\n//Note: The HTML content has to be in a container element because Knockout doesn't allow\r\n//using the \"html\" binding with virtual elements.\r\nexport default createControlComponentConfig(AmeStaticHtml, `\n\t<!-- ko if: containerType === 'div' -->\n\t\t<div data-bind=\"html: htmlContent\"></div>\n\t<!-- /ko -->\n\t<!-- ko if: containerType === 'span' -->\n\t\t<span data-bind=\"html: htmlContent\"></span>\n\t<!-- /ko -->\n`);\r\n//# sourceMappingURL=ame-static-html.js.map", "import { createControlComponentConfig, KoDependentControl } from '../control-base.js';\r\nexport class AmeTextInput extends KoDependentControl {\r\n    constructor(params, $element) {\r\n        super(params, $element);\r\n        this.inputType = 'text';\r\n        this.isCode = params.isCode || false;\r\n        this.inputType = params.inputType || 'text';\r\n    }\r\n    get inputClasses() {\r\n        const classes = ['regular-text'];\r\n        if (this.isCode) {\r\n            classes.push('code');\r\n        }\r\n        classes.push('ame-text-input-control', ...super.inputClasses);\r\n        return classes;\r\n    }\r\n    getAdditionalInputAttributes() {\r\n        return Object.assign({ 'type': this.inputType }, super.getAdditionalInputAttributes());\r\n    }\r\n}\r\nexport default createControlComponentConfig(AmeTextInput, `\n\t<input data-bind=\"value: valueProxy, attr: inputAttributes, class: inputClassString\">\n\t<!-- ko if: (description) -->\n\t\t<!-- ko component: {name: 'ame-sibling-description', params: {description: description}} --><!-- /ko -->\n\t<!-- /ko -->\t\n`);\r\n//# sourceMappingURL=ame-text-input.js.map", "import { createControlComponentConfig, KoStandaloneControl } from '../control-base.js';\r\nclass AmeToggleCheckbox extends KoStandaloneControl {\r\n    constructor(params, $element) {\r\n        super(params, $element);\r\n        this.onValue = (typeof params.onValue !== 'undefined') ? params.onValue : true;\r\n        this.offValue = (typeof params.offValue !== 'undefined') ? params.offValue : false;\r\n        if (typeof this.settings['value'] === 'undefined') {\r\n            this.isChecked = ko.pureComputed(() => false);\r\n        }\r\n        else {\r\n            this.isChecked = ko.computed({\r\n                read: () => {\r\n                    return this.settings.value.value() === ko.unwrap(this.onValue);\r\n                },\r\n                write: (newValue) => {\r\n                    this.settings.value.value(ko.unwrap(newValue ? this.onValue : this.offValue));\r\n                },\r\n                deferEvaluation: true\r\n            });\r\n        }\r\n    }\r\n    get classes() {\r\n        return ['ame-toggle-checkbox-control', ...super.classes];\r\n    }\r\n}\r\n//Unlike the HTML version of this control, the Knockout version doesn't have\r\n//a second, hidden checkbox. This is because the component is entirely JS-based\r\n//and doesn't need to be submitted as part of a form.\r\nexport default createControlComponentConfig(AmeToggleCheckbox, `\n\t<label data-bind=\"class: classString\">\n\t\t<input type=\"checkbox\" data-bind=\"checked: isChecked, attr: inputAttributes, \n\t\t\tclass: inputClassString, enable: isEnabled\">\n\t\t<span data-bind=\"text: label\"></span>\n\t\t<!-- ko if: (description) -->\n\t\t\t<!-- ko component: {name: 'ame-nested-description', params: {description: description}} --><!-- /ko -->\n\t\t<!-- /ko -->\n\t</label>\t\n`);\r\n//# sourceMappingURL=ame-toggle-checkbox.js.map", "import { createControlComponentConfig, KoStandaloneControl } from '../control-base.js';\r\nexport class AmeUnitDropdown extends KoStandaloneControl {\r\n    constructor(params, $element) {\r\n        super(params, $element);\r\n        this.dropdownData = params.optionData || {\r\n            options: [],\r\n            optionsText: 'text',\r\n            optionsValue: 'value'\r\n        };\r\n        this.selectId = params.id || '';\r\n    }\r\n}\r\nexport default createControlComponentConfig(AmeUnitDropdown, `\n\t<select data-bind=\"options: dropdownData.options, optionsText: dropdownData.optionsText, \n\t\toptionsValue: dropdownData.optionsValue, value: valueProxy, class: classString,\n\t\tattr: {id: selectId}\"></select>\n`);\r\n//# sourceMappingURL=ame-unit-dropdown.js.map", "'use strict';\r\nimport { createControlComponentConfig, KoStandaloneControl } from '../control-base.js';\r\n//Note: Requires Lodash, but does not explicitly import it because this plugin\r\n//already uses Lodash as a global variable (wsAmeLodash) in many places. Code\r\n//that uses this component should make sure that Lodash is loaded.\r\nlet autoAssignedIdCounter = 0;\r\n/**\r\n * List of visual editor buttons that are visible in the \"teeny\" mode.\r\n *\r\n * Found in /wp-includes/class-wp-editor.php, the editor_settings() method.\r\n * The relevant code is around line #601 (as of WP 6.1.1).\r\n */\r\nconst TeenyButtons = [\r\n    'bold',\r\n    'italic',\r\n    'underline',\r\n    'blockquote',\r\n    'strikethrough',\r\n    'bullist',\r\n    'numlist',\r\n    'alignleft',\r\n    'aligncenter',\r\n    'alignright',\r\n    'undo',\r\n    'redo',\r\n    'link',\r\n    'fullscreen'\r\n];\r\n/**\r\n * List of Quicktags editor buttons that are visible by default.\r\n *\r\n * The default list of text editor buttons used by wp.editor.initialize()\r\n * doesn't match the defaults used by wp_editor() in PHP. Let's copy the list\r\n * from /includes/class-wp-editor.php.\r\n */\r\nconst DefaultQuicktagsButtons = [\r\n    'strong', 'em', 'link', 'block', 'del', 'ins', 'img', 'ul', 'ol', 'li', 'code', 'more', 'close'\r\n];\r\nclass AmeWpEditor extends KoStandaloneControl {\r\n    constructor(params, $element) {\r\n        super(params, $element);\r\n        this.editorId = null;\r\n        this.isWpEditorInitialized = false;\r\n        const textSetting = this.settings.value;\r\n        if (typeof textSetting === 'undefined') {\r\n            throw new Error('Visual Editor control is missing the required setting');\r\n        }\r\n        this.rows = params.rows || 6;\r\n        this.isTeeny = !!params.teeny;\r\n    }\r\n    getAdditionalInputAttributes() {\r\n        return Object.assign({ rows: this.rows.toString() }, super.getAdditionalInputAttributes());\r\n    }\r\n    koDescendantsComplete() {\r\n        const $textArea = this.findChild('textarea.ame-wp-editor-textarea');\r\n        if ($textArea.length === 0) {\r\n            return;\r\n        }\r\n        const currentValue = this.valueProxy();\r\n        $textArea.val((currentValue === null) ? '' : currentValue.toString());\r\n        //The textarea must have an ID for wp.editor.initialize() to work.\r\n        {\r\n            let editorId = $textArea.attr('id');\r\n            if (!editorId) {\r\n                editorId = 'ws-ame-wp-editor-aid-' + (autoAssignedIdCounter++);\r\n                $textArea.attr('id', editorId);\r\n            }\r\n            this.editorId = editorId;\r\n        }\r\n        //Update the setting when the contents of the underlying textarea change.\r\n        //This happens when the user selects the \"Text\" tab in the editor, or when\r\n        //TinyMCE is unavailable (e.g. if the \"Disable the visual editor when writing\"\r\n        //option is checked in the user's profile).\r\n        $textArea.on('change input', this.throttleUpdates(() => $textArea.val()));\r\n        let editorSettings = {\r\n            tinymce: {\r\n                wpautop: true\r\n            },\r\n            quicktags: {\r\n                //The default list of text editor buttons used by wp.editor.initialize()\r\n                //doesn't match the defaults used by wp_editor() in PHP. Let's copy the list\r\n                //from /includes/class-wp-editor.php.\r\n                buttons: DefaultQuicktagsButtons.join(','),\r\n            },\r\n            //Include the \"Add Media\" button.\r\n            mediaButtons: true,\r\n        };\r\n        if (typeof window['tinymce'] === 'undefined') {\r\n            //TinyMCE is disabled or not available.\r\n            editorSettings.tinymce = false;\r\n        }\r\n        if (this.isTeeny && (typeof editorSettings.tinymce === 'object')) {\r\n            editorSettings.tinymce.toolbar1 = TeenyButtons.join(',');\r\n            editorSettings.tinymce.toolbar2 = '';\r\n        }\r\n        const $document = jQuery(document);\r\n        const self = this;\r\n        //After the editor finishes initializing, add an event listener to update\r\n        //the setting when the contents of the visual editor change.\r\n        $document.on('tinymce-editor-init', function addMceChangeListener(event, editor) {\r\n            if (editor.id !== self.editorId) {\r\n                return; //Not our editor.\r\n            }\r\n            //According to the TinyMCE documentation, the \"Change\" event is fired\r\n            //when \"changes [...] cause an undo level to be added\". This could be\r\n            //too frequent for our purposes, so we'll throttle the callback.\r\n            editor.on('Change', self.throttleUpdates(() => editor.getContent()));\r\n            $document.off('tinymce-editor-init', addMceChangeListener);\r\n        });\r\n        //Unfortunately, as of WP 6.2-beta, wp.editor.initialize() doesn't add\r\n        //the \"wp-editor-container\" wrapper when only the Quicktags editor is used.\r\n        //This means the editor won't be styled correctly. Let's fix that.\r\n        $document.on('quicktags-init', function maybeAddEditorWrapper(event, editor) {\r\n            if (!editor || (editor.id !== self.editorId)) {\r\n                return;\r\n            }\r\n            if (editor.canvas) {\r\n                const $textarea = jQuery(editor.canvas);\r\n                const $wrapper = $textarea.closest('.wp-editor-container');\r\n                if ($wrapper.length === 0) {\r\n                    //Also include the toolbar in the wrapper.\r\n                    const $toolbar = $textarea.prevAll('.quicktags-toolbar').first();\r\n                    $textarea.add($toolbar).wrapAll('<div class=\"wp-editor-container\"></div>');\r\n                }\r\n            }\r\n            $document.off('quicktags-init', maybeAddEditorWrapper);\r\n        });\r\n        //Finally, initialize the editor.\r\n        wp.editor.initialize($textArea.attr('id'), editorSettings);\r\n        this.isWpEditorInitialized = true;\r\n    }\r\n    /**\r\n     * Create a throttled function that updates the setting.\r\n     *\r\n     * There are multiple ways to get the contents of the editor (e.g. TinyMCE mode\r\n     * vs a plain textarea), so using a utility function helps avoid code duplication.\r\n     *\r\n     * @param valueGetter\r\n     * @protected\r\n     */\r\n    throttleUpdates(valueGetter) {\r\n        const textSetting = this.settings.value;\r\n        return wsAmeLodash.throttle(function () {\r\n            textSetting.value(valueGetter());\r\n            return void 0;\r\n        }, 1000, { leading: true, trailing: true });\r\n    }\r\n    dispose() {\r\n        //Destroy the editor. It's not clear whether this is necessary, but it's\r\n        //probably a good idea to give WP a chance to clean up.\r\n        if (this.isWpEditorInitialized && (this.editorId !== null)) {\r\n            wp.editor.remove(this.editorId);\r\n            this.isWpEditorInitialized = false;\r\n        }\r\n        super.dispose();\r\n    }\r\n}\r\n//Note: The class of the textarea element is set directly instead of using a binding\r\n//because it must always have the \"wp-editor-area\" class for it to render correctly\r\n//(apparently, wp.editor.initialize() does not automatically add that class).\r\n//Knockout should not be able to remove the class.\r\nexport default createControlComponentConfig(AmeWpEditor, `\n\t<textarea data-bind=\"attr: inputAttributes\" class=\"wp-editor-area ame-wp-editor-textarea\" cols=\"40\"></textarea>\t\n`);\r\n//# sourceMappingURL=ame-wp-editor.js.map", "import { AmeCustomizable } from '../assets/customizable.js';\r\nvar Setting = AmeCustomizable.Setting;\r\nvar InterfaceStructure = AmeCustomizable.InterfaceStructure;\r\nvar Control = AmeCustomizable.Control;\r\nexport class KoComponentViewModel {\r\n    constructor(params, $element) {\r\n        var _a;\r\n        this.params = params;\r\n        this.$element = $element;\r\n        this.isBoundToComment = ($element[0]) && ($element[0].nodeType === Node.COMMENT_NODE);\r\n        this.uiElement = null;\r\n        const expectedType = this.getExpectedUiElementType();\r\n        if (expectedType !== null) {\r\n            if ((typeof params.uiElement !== 'undefined')\r\n                && (params.uiElement instanceof expectedType)) {\r\n                this.uiElement = params.uiElement;\r\n            }\r\n            else {\r\n                throw new Error('uiElement is not a ' + expectedType.name + ' instance.');\r\n            }\r\n        }\r\n        else if ((typeof params.uiElement !== 'undefined') && !(this instanceof KoStandaloneControl)) {\r\n            console.warn('Unexpected \"uiElement\" parameter for ' + this.constructor.name\r\n                + ' that did not expect an UI element. Did you forget to override getExpectedUiElementType() ?', params.uiElement);\r\n        }\r\n        if (typeof params.children !== 'undefined') {\r\n            if (Array.isArray(params.children) || this.isObservableArray(params.children)) {\r\n                this.inputChildren = params.children;\r\n            }\r\n            else {\r\n                throw new Error('Invalid \"children\" parameter: expected an array or an observable array.');\r\n            }\r\n        }\r\n        else {\r\n            this.inputChildren = [];\r\n        }\r\n        this.customClasses = ((typeof params.classes === 'object') && Array.isArray(params.classes)) ? params.classes : [];\r\n        this.customStyles = ((typeof params.styles === 'object') && (params.styles !== null)) ? params.styles : {};\r\n        if (typeof params.enabled !== 'undefined') {\r\n            if (ko.isObservable(params.enabled)) {\r\n                this.isEnabled = params.enabled;\r\n            }\r\n            else {\r\n                this.isEnabled = ko.pureComputed(() => !!params.enabled);\r\n            }\r\n        }\r\n        else {\r\n            this.isEnabled = ko.pureComputed(() => true);\r\n        }\r\n        //Get the description either from the \"description\" parameter or from the UI element.\r\n        this.description = params.description\r\n            ? ko.unwrap(params.description.toString())\r\n            : (((_a = this.uiElement) === null || _a === void 0 ? void 0 : _a.description) || '');\r\n    }\r\n    dispose() {\r\n        //Does nothing by default.\r\n    }\r\n    getExpectedUiElementType() {\r\n        return null;\r\n    }\r\n    get classes() {\r\n        return [].concat(this.customClasses);\r\n    }\r\n    // noinspection JSUnusedGlobalSymbols -- Used in Knockout templates.\r\n    get classString() {\r\n        return this.classes.join(' ');\r\n    }\r\n    get styles() {\r\n        return Object.assign({}, this.customStyles);\r\n    }\r\n    findChild(selector, allowSiblingSearch = null) {\r\n        if (allowSiblingSearch === null) {\r\n            //Enable only if the component is bound to a comment (i.e. \"<!-- ko component: ... -->\").\r\n            allowSiblingSearch = this.isBoundToComment;\r\n        }\r\n        if (this.isBoundToComment) {\r\n            if (allowSiblingSearch) {\r\n                return this.$element.nextAll(selector).first();\r\n            }\r\n            else {\r\n                //We would never find anything because a comment node has no children.\r\n                return jQuery();\r\n            }\r\n        }\r\n        return this.$element.find(selector);\r\n    }\r\n    isObservableArray(value) {\r\n        return (typeof value === 'object')\r\n            && (value !== null)\r\n            && (typeof value.slice === 'function')\r\n            && (typeof value.indexOf === 'function')\r\n            && (ko.isObservable(value));\r\n    }\r\n}\r\nfunction makeCreateVmFunctionForComponent(ctor) {\r\n    return function (params, componentInfo) {\r\n        const $element = jQuery(componentInfo.element);\r\n        return new ctor(params, $element);\r\n    };\r\n}\r\nexport function createComponentConfig(ctor, templateString) {\r\n    return {\r\n        viewModel: {\r\n            createViewModel: makeCreateVmFunctionForComponent(ctor),\r\n        },\r\n        template: templateString,\r\n    };\r\n}\r\n//endregion\r\n//region Container\r\nexport class ComponentBindingOptions {\r\n    // noinspection JSUnusedGlobalSymbols -- the uiElement property is used in the KO template of AC control groups.\r\n    constructor(name, params, uiElement) {\r\n        this.name = name;\r\n        this.params = params;\r\n        this.uiElement = uiElement;\r\n        if (name === '') {\r\n            throw new Error('Component name cannot be empty.');\r\n        }\r\n    }\r\n    static fromElement(element, overrideComponentName = null) {\r\n        if (!element.component && (overrideComponentName === null)) {\r\n            throw new Error(`Cannot create component binding options for UI element \"${element.id}\" without a component name.`);\r\n        }\r\n        return new ComponentBindingOptions(overrideComponentName || element.component, element.getComponentParams(), element);\r\n    }\r\n}\r\nexport class KoContainerViewModel extends KoComponentViewModel {\r\n    constructor(params, $element) {\r\n        if (typeof params.children === 'undefined') {\r\n            throw new Error('Missing \"children\" parameter.');\r\n        }\r\n        super(params, $element);\r\n        this.title = ko.pureComputed(() => {\r\n            if (typeof params.title !== 'undefined') {\r\n                let title = ko.unwrap(params.title);\r\n                if ((title !== null) && (typeof title !== 'undefined')) {\r\n                    return title.toString();\r\n                }\r\n            }\r\n            if (this.uiElement) {\r\n                return this.uiElement.title;\r\n            }\r\n            return '';\r\n        });\r\n        this.childComponents = ko.pureComputed(() => {\r\n            const result = ko.unwrap(this.inputChildren)\r\n                .map(child => this.mapChildToComponentBinding(child))\r\n                .filter(binding => binding !== null);\r\n            //TypeScript does not recognize that the filter() call above removes\r\n            //all null values, so we need an explicit cast.\r\n            return result;\r\n        });\r\n    }\r\n    mapChildToComponentBinding(child) {\r\n        //Does not map any children by default.\r\n        return null;\r\n    }\r\n    dispose() {\r\n        super.dispose();\r\n        this.childComponents.dispose();\r\n    }\r\n}\r\n//endregion\r\n//region Control\r\nexport class KoControlViewModel extends KoComponentViewModel {\r\n    constructor(params, $element) {\r\n        var _a;\r\n        super(params, $element);\r\n        this.settings =\r\n            ((typeof params.settings === 'object') && isSettingMap(params.settings))\r\n                ? params.settings\r\n                : {};\r\n        if (typeof this.settings.value !== 'undefined') {\r\n            this.valueProxy = this.settings.value.value;\r\n        }\r\n        else {\r\n            this.valueProxy = ko.pureComputed(() => {\r\n                console.error('Missing \"value\" setting for a control component.', this.settings, params);\r\n                return '';\r\n            });\r\n        }\r\n        //Input ID will be provided by the server if applicable.\r\n        this.primaryInputId = (typeof params.primaryInputId === 'string') ? params.primaryInputId : null;\r\n        this.inputAttributes = ko.pureComputed(() => {\r\n            var _a;\r\n            const attributes = ((_a = this.uiElement) === null || _a === void 0 ? void 0 : _a.inputAttributes) || {};\r\n            const inputId = this.getPrimaryInputId();\r\n            if ((inputId !== null) && (inputId !== '')) {\r\n                attributes.id = inputId;\r\n            }\r\n            //Note: The \"name\" field is not used because these controls are entirely JS-driven.\r\n            const additionalAttributes = this.getAdditionalInputAttributes();\r\n            for (const key in additionalAttributes) {\r\n                if (!additionalAttributes.hasOwnProperty(key)) {\r\n                    continue;\r\n                }\r\n                attributes[key] = additionalAttributes[key];\r\n            }\r\n            return attributes;\r\n        });\r\n        if ((typeof params.label !== 'undefined') && (params.label !== null)) {\r\n            const unwrappedLabel = ko.unwrap(params.label);\r\n            this.label = (typeof unwrappedLabel === 'undefined') ? '' : unwrappedLabel.toString();\r\n        }\r\n        else {\r\n            this.label = ((_a = this.uiElement) === null || _a === void 0 ? void 0 : _a.label) || '';\r\n        }\r\n    }\r\n    get inputClasses() {\r\n        var _a;\r\n        return ((_a = this.uiElement) === null || _a === void 0 ? void 0 : _a.inputClasses) || [];\r\n    }\r\n    // noinspection JSUnusedGlobalSymbols -- Used in Knockout templates.\r\n    get inputClassString() {\r\n        return this.inputClasses.join(' ');\r\n    }\r\n    getAdditionalInputAttributes() {\r\n        return {};\r\n    }\r\n    getPrimaryInputId() {\r\n        return this.primaryInputId;\r\n    }\r\n}\r\nfunction isSettingMap(value) {\r\n    if (value === null) {\r\n        return false;\r\n    }\r\n    if (typeof value !== 'object') {\r\n        return false;\r\n    }\r\n    const valueAsRecord = value;\r\n    for (const key in valueAsRecord) {\r\n        if (!valueAsRecord.hasOwnProperty(key)) {\r\n            continue;\r\n        }\r\n        if (!(valueAsRecord[key] instanceof Setting)) {\r\n            return false;\r\n        }\r\n    }\r\n    return true;\r\n}\r\n/**\r\n * A control that doesn't use or need a UI element instance, but can still have\r\n * settings and other parameters typically associated with controls.\r\n */\r\nexport class KoStandaloneControl extends KoControlViewModel {\r\n}\r\n/**\r\n * A control that requires a UI element of the \"Control\" class.\r\n */\r\nexport class KoDependentControl extends KoControlViewModel {\r\n    getExpectedUiElementType() {\r\n        return Control;\r\n    }\r\n}\r\nexport function createControlComponentConfig(ctor, templateString) {\r\n    return {\r\n        viewModel: {\r\n            createViewModel: makeCreateVmFunctionForComponent(ctor),\r\n        },\r\n        template: templateString,\r\n    };\r\n}\r\n//endregion\r\n//region Renderer\r\nexport class KoRendererViewModel extends KoComponentViewModel {\r\n    constructor(params, $element) {\r\n        super(params, $element);\r\n        if ((typeof params.structure !== 'object') || !(params.structure instanceof InterfaceStructure)) {\r\n            throw new Error('Invalid interface structure for a renderer component.');\r\n        }\r\n        this.structure = params.structure;\r\n    }\r\n}\r\nexport function createRendererComponentConfig(ctor, templateString) {\r\n    return {\r\n        viewModel: {\r\n            createViewModel: makeCreateVmFunctionForComponent(ctor),\r\n        },\r\n        template: templateString,\r\n    };\r\n}\r\n//endregion\r\n//# sourceMappingURL=control-base.js.map", "/// <reference path=\"../../../customizables/assets/popup-slider.d.ts\" />\r\n/**\r\n * This is a wrapper for the popup slider that initializes the slider on first use.\r\n * It's useful for Knockout components.\r\n */\r\nexport class LazyPopupSliderAdapter {\r\n    constructor(sliderRanges, containerSelector = '.ame-container-with-popup-slider', inputSelector = 'input', sliderOptions = {}) {\r\n        this.sliderRanges = sliderRanges;\r\n        this.containerSelector = containerSelector;\r\n        this.inputSelector = inputSelector;\r\n        this.sliderOptions = sliderOptions;\r\n        this.slider = null;\r\n        if (!sliderOptions.hasOwnProperty('ranges')) {\r\n            sliderOptions.ranges = sliderRanges;\r\n        }\r\n        this.handleKoClickEvent = ($data, event) => {\r\n            //Verify that this is one of the inputs we're interested in.\r\n            //Also, disabled inputs should not trigger the slider.\r\n            if (event.target === null) {\r\n                return;\r\n            }\r\n            const $input = jQuery(event.target);\r\n            if ($input.is(':disabled') || !$input.is(this.inputSelector)) {\r\n                return;\r\n            }\r\n            //Short-circuit if the slider is already initialized.\r\n            if (this.slider) {\r\n                this.slider.showForInput($input);\r\n                return;\r\n            }\r\n            //Some sanity checks.\r\n            if (typeof AmePopupSlider === 'undefined') {\r\n                return;\r\n            }\r\n            const $container = $input.closest(this.containerSelector);\r\n            if ($container.length < 1) {\r\n                return;\r\n            }\r\n            this.initSlider($container);\r\n            if (this.slider !== null) {\r\n                //TS doesn't realize that this.initSlider() will initialize the slider.\r\n                this.slider.showForInput($input);\r\n            }\r\n        };\r\n    }\r\n    /**\r\n     * Initialize the slider if it's not already initialized.\r\n     */\r\n    initSlider($container) {\r\n        if (this.slider) {\r\n            return;\r\n        }\r\n        //In HTML, we would pass the range data as a \"data-slider-ranges\" attribute,\r\n        //but here they are passed via the \"ranges\" option (see the constructor).\r\n        this.slider = AmePopupSlider.createSlider($container, this.sliderOptions);\r\n    }\r\n}\r\n//# sourceMappingURL=lazy-popup-slider-adapter.js.map"], "names": [], "sourceRoot": ""}