{"version": 3, "file": "menu-styler-ui.bundle.js", "mappings": ";;;;;;;;;;;;AAAA,8CAA8C;AAC9C,sDAAsD;AAEqB;AACa;AASxF,MAAM,CAAC,UAAU,CAAe;IAC/B,MAAM,CAAC,GAAG,WAAW,CAAC;IAEtB,MAAM,cAAc,GAAG,aAAa,CAAC;IAErC;;;;;OAKG;IACH,MAAM,iBAAiB;QAAvB;YACkB,cAAS,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC;YACxB,eAAU,GAAG,CAAC,CAAC,YAAY,CAAC,CAAC;YAEtC,mBAAc,GAAkB,IAAI,CAAC;YACrC,kBAAa,GAAkB,IAAI,CAAC;YACpC,sBAAiB,GAAkB,IAAI,CAAC;YAE/B,uBAAkB,GAAG,CAAC,CAAC,QAAQ,CAAC,GAAG,EAAE;gBACrD,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC;gBACjD,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC;gBAC/C,MAAM,SAAS,GAAG,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,EAAE,CAAC,CAAC;gBAElE,IACC,CAAC,UAAU,KAAK,IAAI,CAAC,cAAc,CAAC;uBACjC,CAAC,SAAS,KAAK,IAAI,CAAC,aAAa,CAAC;uBAClC,CAAC,SAAS,KAAK,IAAI,CAAC,iBAAiB,CAAC,EACxC;oBACD,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC;oBACjC,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC;oBAC/B,IAAI,CAAC,iBAAiB,GAAG,SAAS,CAAC;oBAEnC,mEAAmE;oBACnE,+EAA+E;oBAC/E,iEAAiE;oBACjE,MAAM,CAAC,qBAAqB,CAAC,GAAG,EAAE;wBACjC,uFAAuF;wBACvF,uFAAuF;wBACvF,mFAAmF;wBACnF,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;oBAC9C,CAAC,CAAC,CAAC;iBACH;YACF,CAAC,EAAE,IAAI,EAAE,EAAC,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC;QAK3C,CAAC;QAHO,WAAW;YACjB,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC3B,CAAC;KACD;IAED,MAAM,mBAAoB,SAAQ,wGAAiC;QASlE;YACC;;;;;eAKG;YACH,MAAM,qBAAqB,GAAG,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;YAEnD,KAAK,CAAC,qBAAqB,CAAC,CAAC;YAhBtB,gBAAW,GAAG,IAAI,CAAC;YACnB,YAAO,GAAkB,IAAI,CAAC;YAI9B,sBAAiB,GAAsB,IAAI,iBAAiB,EAAE,CAAC;YAatE,IAAI,CAAC,oBAAoB,GAAG,qBAAqB,CAAC;YAClD,IAAI,CAAC,iBAAiB,GAAG,IAAI,qBAAqB,CAAC,kBAAkB,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;YAEjF,kEAAkE;YAClE,2EAA2E;YAC3E,MAAM,oBAAoB,GAAG,CAAC,SAAiB,EAAE,YAAiB,EAAO,EAAE;gBAC1E,MAAM,IAAI,GAAG,YAAY,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;gBAC1E,IAAI,IAAI,KAAK,IAAI,EAAE;oBAClB,OAAO,YAAY,CAAC;iBACpB;gBAED,MAAM,KAAK,GAAG,YAAY,CAAC,iBAAiB,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAC1E,IAAI,KAAK,KAAK,IAAI,CAAC,QAAQ,EAAE;oBAC5B,OAAO,KAAK,CAAC;iBACb;qBAAM,IAAI,mBAAmB,CAAC,QAAQ,CAAC,cAAc,CAAC,SAAS,CAAC,EAAE;oBAClE,OAAO,mBAAmB,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;iBAC/C;qBAAM;oBACN,MAAM,IAAI,KAAK,CAAC,iCAAiC,GAAG,SAAS,CAAC,CAAC;iBAC/D;YACF,CAAC,CAAC;YACF,KAAK,MAAM,SAAS,IAAI,YAAY,CAAC,iBAAiB,CAAC,gBAAgB,EAAE,EAAE;gBAC1E,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,EAAE,SAAS,CAAC,CAAC;aAC5D;YAED,KAAK,MAAM,aAAa,IAAI,mBAAmB,CAAC,mBAAmB,EAAE;gBACpE,MAAM,eAAe,GAAG,IAAI,gHAA+C,CAAC,aAAa,CAAC,CAAC;gBAC3F,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAC,wBAAwB,EAAE,EAAE,eAAe,CAAC,CAAC;aACzF;YAED,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,sCAAsC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;QACrE,CAAC;QAED,WAAW;YACV,MAAM,YAAY,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAEhD,sFAAsF;YACtF,sFAAsF;YACtF,2FAA2F;YAC3F,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC5C,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;gBACvB,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,MAAM,EAAE;oBAC1B,OAAO,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC;iBAC3B;gBACD,OAAO,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;YAC3B,CAAC,CAAC,CAAC;YAEH,0EAA0E;YAC1E,2EAA2E;YAC3E,MAAM,aAAa,GAAwB,EAAE,CAAC;YAC9C,KAAK,MAAM,SAAS,IAAI,SAAS,EAAE;gBAClC,MAAM,IAAI,GAAG,YAAY,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;gBAC1E,IAAI,IAAI,KAAK,IAAI,EAAE;oBAClB,SAAS;iBACT;gBAED,MAAM,KAAK,GAAG,YAAY,CAAC,SAAS,CAAC,CAAC;gBACtC,yEAAyE;gBACzE,2DAA2D;gBAC3D,IAAI,KAAK,KAAK,IAAI,EAAE;oBACnB,SAAS;iBACT;gBACD,CAAC,CAAC,GAAG,CAAC,aAAa,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;aAClC;YAED,8DAA8D;YAC9D,CAAC,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC,cAAc,EAAE,eAAe,CAAC,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC;YAEpF,sCAAsC;YACtC,MAAM,YAAY,GAAwB,CAAC,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC,eAAe,CAAC,EAAE,EAAE,CAAC,CAAC;YACtF,KAAK,MAAM,UAAU,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE;gBACnD,gEAAgE;gBAChE,mEAAmE;gBACnE,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC;gBACzE,kCAAkC;gBAClC,IAAI,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,EAAE;oBACxC,OAAO,YAAY,CAAC,UAAU,CAAC,CAAC;iBAChC;aACD;YAED,oEAAoE;YACpE,KAAK,MAAM,GAAG,IAAI,aAAa,EAAE;gBAChC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;oBACvC,SAAS;iBACT;gBACD,MAAM,KAAK,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC;gBACjC,YAAY,CAAC,iBAAiB,CAAC,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;aACnD;YAED,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,mCAAmC,CAAC,CAAC;QAC1D,CAAC;QAES,YAAY,CAAC,WAA2B,IAAI;YACrD,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;gBAC/B,OAAO,KAAK,CAAC;aACb;YAED,IAAI,QAAQ,KAAK,IAAI,EAAE;gBACtB,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;gBACpC,OAAO,QAAQ,CAAC;aAChB;YACD,OAAO,IAAI,CAAC,oBAAoB,EAAE,CAAC;QACpC,CAAC;QAES,qBAAqB;YAC9B,8CAA8C;YAC9C,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE;gBACzB,OAAO,KAAK,CAAC;aACb;YACD,OAAO,KAAK,CAAC,qBAAqB,EAAE,CAAC;QACtC,CAAC;QAED,aAAa,CAAC,UAAoB;YACjC,KAAK,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;YAChC,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,CAAC;QACtC,CAAC;QAED,SAAS,CAAC,OAAe;YACxB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;YAEvB,IAAI,QAAQ,GAAkB,IAAI,CAAC;YAEnC,OAAO,CAAC,EAAE,CAAC,YAAY,EAAE,GAAG,EAAE;gBAC7B,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;gBACxB,IAAI,CAAC,YAAY,EAAE,CAAC;gBAEpB,4DAA4D;gBAC5D,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC,KAAK,EAAE,CAAC;gBAC/E,QAAQ,CAAC,QAAQ,CAAC,uBAAuB,CAAC,CAAC;YAC5C,CAAC,CAAC,CAAC;YACH,OAAO,CAAC,EAAE,CAAC,aAAa,EAAE,GAAG,EAAE;gBAC9B,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;gBAEzB,IAAI,QAAQ,EAAE;oBACb,QAAQ,CAAC,WAAW,CAAC,uBAAuB,CAAC,CAAC;oBAC9C,QAAQ,GAAG,IAAI,CAAC;iBAChB;YACF,CAAC,CAAC,CAAC;QACJ,CAAC;QAES,YAAY;YACrB,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;gBACtB,IAAI,CAAC,iBAAiB,EAAE,CAAC;aACzB;YAED,IAAI,IAAI,CAAC,WAAW,EAAE;gBACrB,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;gBACzB,uCAAuC;gBACvC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;aACnE;QACF,CAAC;QAED,iEAAiE;QACjE,eAAe;YACd,qCAAqC;YACrC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC;YAEtD,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,IAAI,CAAC,WAAW,EAAE,CAAC;QACpB,CAAC;QAED,cAAc;YACb,IAAI,CAAC,WAAW,EAAE,CAAC;QACpB,CAAC;QAES,WAAW;YACpB,IAAI,IAAI,CAAC,OAAO,KAAK,IAAI,EAAE;gBAC1B,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;aAC7B;QACF,CAAC;KACD;IAED,MAAM,YAAY,GAAG,CAAC,CAAC,6BAA6B,CAAC,CAAC;IACtD,IAAI,mBAAmB,GAAG,KAAK,CAAC;IAEhC,SAAS,gBAAgB;QACxB,YAAY,CAAC,MAAM,CAAC;YACnB,QAAQ,EAAE,KAAK;YACf,SAAS,EAAE,GAAG;YACd,SAAS,EAAE,KAAK;YAChB,KAAK,EAAE,IAAI;YACX,gDAAgD;YAChD,QAAQ,EAAE,GAAG;YACb,MAAM,EAAE,GAAG;YACX,OAAO,EAAE;gBACR,WAAW,EAAE,wCAAwC;aACrD;SACD,CAAC,CAAC;QAEH,mBAAmB,GAAG,IAAI,CAAC;QAE3B,MAAM,EAAE,GAAG,IAAI,mBAAmB,EAAE,CAAC;QACpC,MAAc,CAAC,iBAAiB,CAAC,GAAG,EAAE,CAAC;QAExC,EAAE,CAAC,aAAa,CAAC,EAAE,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;QACtC,EAAE,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;IAC5B,CAAC;IAED,wDAAwD;IACxD,CAAC,CAAC,sBAAsB,CAAC,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;QAC1C,yDAAyD;QACzD,IAAI,CAAC,mBAAmB,EAAE;YACzB,gBAAgB,EAAE,CAAC;SACnB;QAED,oDAAoD;QACpD,YAAY,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QAElD,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IAC7B,CAAC,CAAC,CAAC;AACJ,CAAC,CAAC,CAAC;;;;;;;;;;;;;;;ACpSI;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,IAAI;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B,IAAI,IAAI,MAAM,IAAI,KAAK,IAAI,QAAQ;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sDAAsD;AACtD,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0CAA0C;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,WAAW,KAAK,SAAS,KAAK,KAAK,KAAK,OAAO;AAC9E;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC,sBAAsB;AACzD,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC,SAAS,IAAI,MAAM,EAAE,MAAM;AAChE;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,sDAAsD,kCAAkC;AACxF,SAAS;AACT;AACA;AACA;AACA,sDAAsD,iCAAiC;AACvF,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yDAAyD,SAAS;AAClE;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wCAAwC;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2CAA2C;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6EAA6E,gBAAgB;AAC7F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,WAAW,gBAAgB;AAC5C;AACA;AACA;AACA;AACA,uBAAuB,iBAAiB;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,WAAW,gBAAgB;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sDAAsD,gBAAgB,EAAE,SAAS;AACjF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kDAAkD,wCAAwC;AAC1F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iDAAiD,kCAAkC;AACnF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA,iBAAiB,WAAW,gBAAgB;AAC5C;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK,wEAAwE;AAC7E,CAAC,8CAA8C;AAC/C", "sources": ["webpack:///./extras/modules/menu-styler/menu-styler-ui.ts", "webpack:///./extras/style-generator/style-generator.js"], "sourcesContent": ["///<reference path=\"../../../js/common.d.ts\"/>\n///<reference path=\"../../../js/jquery.biscuit.d.ts\"/>\n\nimport {AmeStyleGenerator} from '../../style-generator/style-generator.js';\nimport {AmeCustomizableViewModel} from '../../pro-customizables/assets/customizable.js';\n\ndeclare var wsAmeLodash: _.LoDashStatic;\n\ndeclare const ameMenuStylerConfig: {\n\tdefaults: Record<string, unknown>,\n\tstylePreviewConfigs: AmeStyleGenerator.Preview.StyleGeneratorPreviewConfig[]\n};\n\njQuery(function ($: JQueryStatic) {\n\tconst _ = wsAmeLodash;\n\n\tconst styleConfigKey = 'menu_styles';\n\n\t/**\n\t * Utility class that tells WordPress to pin or unpin the admin menu as needed\n\t * when the menu dimensions or the top margin change.\n\t *\n\t * Uses throttling to avoid excessive updates.\n\t */\n\tclass StickyMenuUpdater {\n\t\tprivate readonly $document = $(document);\n\t\tprivate readonly $adminmenu = $('#adminmenu');\n\n\t\tprivate previousHeight: number | null = null;\n\t\tprivate previousWidth: number | null = null;\n\t\tprivate previousTopMargin: number | null = null;\n\n\t\tprivate readonly updateMenuPinState = _.throttle(() => {\n\t\t\tconst menuHeight = this.$adminmenu.outerHeight();\n\t\t\tconst menuWidth = this.$adminmenu.outerWidth();\n\t\t\tconst topMargin = parseInt(this.$adminmenu.css('margin-top'), 10);\n\n\t\t\tif (\n\t\t\t\t(menuHeight !== this.previousHeight)\n\t\t\t\t|| (menuWidth !== this.previousWidth)\n\t\t\t\t|| (topMargin !== this.previousTopMargin)\n\t\t\t) {\n\t\t\t\tthis.previousHeight = menuHeight;\n\t\t\t\tthis.previousWidth = menuWidth;\n\t\t\t\tthis.previousTopMargin = topMargin;\n\n\t\t\t\t//In practice, this update doesn't always work if done immediately.\n\t\t\t\t//Not sure why, maybe menu dimensions don't change instantly when, for example,\n\t\t\t\t//the user adds a logo image. Adding a small delay seems to help.\n\t\t\t\twindow.requestAnimationFrame(() => {\n\t\t\t\t\t//The custom \"wp-pin-menu\" event was added to WP core in 2015. It can be used to update\n\t\t\t\t\t//the menu \"sticky\" state. I'm using triggerHandler() instead of trigger() because this\n\t\t\t\t\t//is what /wp-admin/js/widgets.js does. Hopefully, that will improve compatibility.\n\t\t\t\t\tthis.$document.triggerHandler('wp-pin-menu');\n\t\t\t\t});\n\t\t\t}\n\t\t}, 1000, {leading: true, trailing: true});\n\n\t\tpublic queueUpdate(): void {\n\t\t\tthis.updateMenuPinState();\n\t\t}\n\t}\n\n\tclass MenuStylerViewModel extends AmeCustomizableViewModel.SimpleVm {\n\t\tprivate readonly dialogOpenObservable: KnockoutObservable<boolean>;\n\t\tprivate isFirstOpen = true;\n\t\tprivate $dialog: JQuery | null = null;\n\n\t\tprivate previewPreference: WsAmePreferenceCookie;\n\n\t\tprivate stickyMenuUpdater: StickyMenuUpdater = new StickyMenuUpdater();\n\n\t\tconstructor() {\n\t\t\t/**\n\t\t\t * This observable is initially stored in a local variable because TypeScript doesn't\n\t\t\t * allow accessing `this` in the constructor before calling super(), but we still\n\t\t\t * want to establish a dependency on the dialog open state so that preview gets enabled\n\t\t\t * when the dialog is open. The observable will get updated later.\n\t\t\t */\n\t\t\tconst extraPreviewCondition = ko.observable(false);\n\n\t\t\tsuper(extraPreviewCondition);\n\n\t\t\tthis.dialogOpenObservable = extraPreviewCondition;\n\t\t\tthis.previewPreference = new WsAmePreferenceCookie('MsPreviewEnabled', 90, true);\n\n\t\t\t//Read settings from the currently loaded admin menu configuration\n\t\t\t//using the aux-data API. Setting ID prefixes should already be registered.\n\t\t\tconst auxDataSettingReader = (settingId: string, defaultValue: any): any => {\n\t\t\t\tconst path = AmeEditorApi.configDataAdapter.mapSettingIdToPath(settingId);\n\t\t\t\tif (path === null) {\n\t\t\t\t\treturn defaultValue;\n\t\t\t\t}\n\n\t\t\t\tconst value = AmeEditorApi.configDataAdapter.getPath(path, this.notFound);\n\t\t\t\tif (value !== this.notFound) {\n\t\t\t\t\treturn value;\n\t\t\t\t} else if (ameMenuStylerConfig.defaults.hasOwnProperty(settingId)) {\n\t\t\t\t\treturn ameMenuStylerConfig.defaults[settingId];\n\t\t\t\t} else {\n\t\t\t\t\tthrow new Error('Unknown aux config setting ID: ' + settingId);\n\t\t\t\t}\n\t\t\t};\n\t\t\tfor (const auxPrefix of AmeEditorApi.configDataAdapter.getKnownPrefixes()) {\n\t\t\t\tthis.registerSettingReader(auxDataSettingReader, auxPrefix);\n\t\t\t}\n\n\t\t\tfor (const previewConfig of ameMenuStylerConfig.stylePreviewConfigs) {\n\t\t\t\tconst previewInstance = new AmeStyleGenerator.Preview.StyleGeneratorPreview(previewConfig);\n\t\t\t\tthis.registerPreviewUpdater(previewInstance.getPreviewableSettingIDs(), previewInstance);\n\t\t\t}\n\n\t\t\t$(document).trigger('adminMenuEditor:menuStylerUiRegister', [this]);\n\t\t}\n\n\t\tsaveChanges() {\n\t\t\tconst settingsById = this.getAllSettingValues();\n\n\t\t\t//Sort by length of the setting ID and then by the ID itself to ensure parent settings\n\t\t\t//are updated before their children. For example, this matters for color presets where\n\t\t\t//the \"activePreset\" setting maps to the \"[global]\" property of the \"colorPresets\" setting.\n\t\t\tconst sortedIds = Object.keys(settingsById);\n\t\t\tsortedIds.sort((a, b) => {\n\t\t\t\tif (a.length !== b.length) {\n\t\t\t\t\treturn a.length - b.length;\n\t\t\t\t}\n\t\t\t\treturn a.localeCompare(b);\n\t\t\t});\n\n\t\t\t//Write all settings into a new object, then save the top-level properties\n\t\t\t//of that. This way stale and empty settings will automatically be removed.\n\t\t\tconst updatedConfig: Record<string, any> = {};\n\t\t\tfor (const settingId of sortedIds) {\n\t\t\t\tconst path = AmeEditorApi.configDataAdapter.mapSettingIdToPath(settingId);\n\t\t\t\tif (path === null) {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\tconst value = settingsById[settingId];\n\t\t\t\t//To save space, don't store null values. This could be extended by using\n\t\t\t\t//the \"deleteWhenBlank\" property of the setting definition.\n\t\t\t\tif (value === null) {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\t_.set(updatedConfig, path, value);\n\t\t\t}\n\n\t\t\t//Special: Update the last modified timestamp for menu styles.\n\t\t\t_.set(updatedConfig, [styleConfigKey, '_lastModified'], (new Date()).toISOString());\n\n\t\t\t//Special: Remove empty color presets.\n\t\t\tconst colorPresets: Record<string, any> = _.get(updatedConfig, ['color_presets'], {});\n\t\t\tfor (const presetName of Object.keys(colorPresets)) {\n\t\t\t\t//Remove empty string values (i.e. no color selected). This also\n\t\t\t\t//covers nulls and empty arrays/objects, but that shouldn't happen.\n\t\t\t\tcolorPresets[presetName] = _.omitBy(colorPresets[presetName], _.isEmpty);\n\t\t\t\t//Remove the preset if it's empty.\n\t\t\t\tif (_.isEmpty(colorPresets[presetName])) {\n\t\t\t\t\tdelete colorPresets[presetName];\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t//Finally, write the top-level properties to the menu configuration.\n\t\t\tfor (const key in updatedConfig) {\n\t\t\t\tif (!updatedConfig.hasOwnProperty(key)) {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\tconst value = updatedConfig[key];\n\t\t\t\tAmeEditorApi.configDataAdapter.setPath(key, value);\n\t\t\t}\n\n\t\t\t$(document).trigger('adminMenuEditor:menuConfigChanged');\n\t\t}\n\n\t\tprotected isDialogOpen(newValue: boolean | null = null): boolean {\n\t\t\tif (!this.dialogOpenObservable) {\n\t\t\t\treturn false;\n\t\t\t}\n\n\t\t\tif (newValue !== null) {\n\t\t\t\tthis.dialogOpenObservable(newValue);\n\t\t\t\treturn newValue;\n\t\t\t}\n\t\t\treturn this.dialogOpenObservable();\n\t\t}\n\n\t\tprotected getPreviewActiveState(): boolean {\n\t\t\t//Disable preview when the dialog is not open.\n\t\t\tif (!this.isDialogOpen()) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\treturn super.getPreviewActiveState();\n\t\t}\n\n\t\tupdatePreview(settingIds: string[]) {\n\t\t\tsuper.updatePreview(settingIds);\n\t\t\tthis.stickyMenuUpdater.queueUpdate();\n\t\t}\n\n\t\tsetDialog($dialog: JQuery) {\n\t\t\tthis.$dialog = $dialog;\n\n\t\t\tlet $overlay: JQuery | null = null;\n\n\t\t\t$dialog.on('dialogopen', () => {\n\t\t\t\tthis.isDialogOpen(true);\n\t\t\t\tthis.onOpenDialog();\n\n\t\t\t\t//Add a custom class to the overlay so that we can style it.\n\t\t\t\t$overlay = $dialog.closest('.ui-dialog').nextAll('.ui-widget-overlay').first();\n\t\t\t\t$overlay.addClass('ame-ms-dialog-overlay');\n\t\t\t});\n\t\t\t$dialog.on('dialogclose', () => {\n\t\t\t\tthis.isDialogOpen(false);\n\n\t\t\t\tif ($overlay) {\n\t\t\t\t\t$overlay.removeClass('ame-ms-dialog-overlay');\n\t\t\t\t\t$overlay = null;\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\n\t\tprotected onOpenDialog() {\n\t\t\tif (!this.isFirstOpen) {\n\t\t\t\tthis.reloadAllSettings();\n\t\t\t}\n\n\t\t\tif (this.isFirstOpen) {\n\t\t\t\tthis.isFirstOpen = false;\n\t\t\t\t//Load the preview state from a cookie.\n\t\t\t\tthis.isPreviewEnabled(this.previewPreference.readAndRefresh(true));\n\t\t\t}\n\t\t}\n\n\t\t// noinspection JSUnusedGlobalSymbols -- Used in the KO template.\n\t\tonConfirmDialog() {\n\t\t\t//Save the preview state in a cookie.\n\t\t\tthis.previewPreference.write(this.isPreviewEnabled());\n\n\t\t\tthis.saveChanges();\n\t\t\tthis.closeDialog();\n\t\t}\n\n\t\tonCancelDialog() {\n\t\t\tthis.closeDialog();\n\t\t}\n\n\t\tprotected closeDialog() {\n\t\t\tif (this.$dialog !== null) {\n\t\t\t\tthis.$dialog.dialog('close');\n\t\t\t}\n\t\t}\n\t}\n\n\tconst $styleDialog = $('#ws-ame-menu-style-settings');\n\tlet isDialogInitialized = false;\n\n\tfunction initializeDialog() {\n\t\t$styleDialog.dialog({\n\t\t\tautoOpen: false,\n\t\t\tcloseText: ' ',\n\t\t\tdraggable: false,\n\t\t\tmodal: true,\n\t\t\t//Dialog dimensions and position are set in CSS.\n\t\t\tminWidth: 300,\n\t\t\theight: 400,\n\t\t\tclasses: {\n\t\t\t\t'ui-dialog': 'ui-corner-all ws-ame-menu-style-dialog',\n\t\t\t}\n\t\t});\n\n\t\tisDialogInitialized = true;\n\n\t\tconst vm = new MenuStylerViewModel();\n\t\t(window as any)['ameMenuStylerVm'] = vm;\n\n\t\tko.applyBindings(vm, $styleDialog[0]);\n\t\tvm.setDialog($styleDialog);\n\t}\n\n\t//Open the dialog when the user clicks the style button.\n\t$('#ws_edit_menu_styles').on('click', () => {\n\t\t//Optimization: Initialize the dialog on the first click.\n\t\tif (!isDialogInitialized) {\n\t\t\tinitializeDialog();\n\t\t}\n\n\t\t//Reset the scroll position of the tab content area.\n\t\t$styleDialog.find('.ame-tp-content').scrollTop(0);\n\n\t\t$styleDialog.dialog('open');\n\t});\n});", "export var AmeStyleGenerator;\r\n(function (AmeStyleGenerator) {\r\n    const $ = jQuery;\r\n    class ValueDescriptor {\r\n    }\r\n    class ConstantValue extends ValueDescriptor {\r\n        constructor(value) {\r\n            super();\r\n            this.value = value;\r\n        }\r\n        getValue() {\r\n            return this.value;\r\n        }\r\n    }\r\n    class ArrayValue extends ValueDescriptor {\r\n        constructor(items) {\r\n            super();\r\n            this.items = items;\r\n        }\r\n        getValue() {\r\n            return this.items.map(item => item.getValue());\r\n        }\r\n        getItemDescriptors() {\r\n            return this.items;\r\n        }\r\n    }\r\n    class SettingReference extends ValueDescriptor {\r\n        constructor(settingId, valueGetter) {\r\n            super();\r\n            this.settingId = settingId;\r\n            this.valueGetter = valueGetter;\r\n        }\r\n        getValue() {\r\n            return this.valueGetter(this.settingId);\r\n        }\r\n    }\r\n    class VariableReference extends ValueDescriptor {\r\n        constructor(name, valueGetter) {\r\n            super();\r\n            this.name = name;\r\n            this.valueGetter = valueGetter;\r\n        }\r\n        getValue() {\r\n            return this.valueGetter(this.name);\r\n        }\r\n    }\r\n    class FunctionCall extends ValueDescriptor {\r\n        constructor(args, callback) {\r\n            super();\r\n            this.args = args;\r\n            this.callback = callback;\r\n        }\r\n        getValue() {\r\n            return this.callback(this.resolveArgs(this.args));\r\n        }\r\n        resolveArgs(args) {\r\n            if (Array.isArray(args)) {\r\n                return args.map(arg => arg.getValue());\r\n            }\r\n            return Object.keys(args).reduce((result, key) => {\r\n                result[key] = args[key].getValue();\r\n                return result;\r\n            }, {});\r\n        }\r\n    }\r\n    //endregion\r\n    function isEmptyCssValue(value) {\r\n        return (typeof value === 'undefined') || (value === '') || (value === null);\r\n    }\r\n    function convertToRgba(color, opacity = 1.0) {\r\n        color = color.trim();\r\n        if (color === '') {\r\n            return 'transparent';\r\n        }\r\n        //Strip the leading hash, if any.\r\n        if (color[0] === '#') {\r\n            color = color.substring(1);\r\n        }\r\n        //If the color is in the shorthand format, expand it.\r\n        if (color.length === 3) {\r\n            color = color[0] + color[0] + color[1] + color[1] + color[2] + color[2];\r\n        }\r\n        //The color should now be in the full 6-digit format. Convert it to RGBA.\r\n        if (color.length === 6) {\r\n            const red = parseInt(color.substring(0, 2), 16);\r\n            const green = parseInt(color.substring(2, 4), 16);\r\n            const blue = parseInt(color.substring(4, 6), 16);\r\n            return `rgba(${red}, ${green}, ${blue}, ${opacity})`;\r\n        }\r\n        //The color may be invalid, or it's not in a hex format we recognize.\r\n        return color;\r\n    }\r\n    function uniqueArrayValues(array) {\r\n        return array.filter((value, index) => array.indexOf(value) === index);\r\n    }\r\n    function constrain(value, min, max) {\r\n        return Math.min(Math.max(value, min), max);\r\n    }\r\n    function modifyHexColorAsHsl(args, operation) {\r\n        const color = args.color || '';\r\n        if (isEmptyCssValue(color)) {\r\n            return '';\r\n        }\r\n        const hue = args.hue || null;\r\n        const saturation = args.saturation || null;\r\n        const lightness = args.lightness || null;\r\n        if ((hue === null) && (saturation === null) && (lightness === null)) {\r\n            return color;\r\n        }\r\n        let output = $.Color(color);\r\n        output = operation(output, hue, saturation, lightness);\r\n        return output.toHexString();\r\n    }\r\n    // noinspection JSUnusedGlobalSymbols -- Used dynamically by declaration generators received from the server.\r\n    const builtinFunctions = {\r\n        simpleProperty: function (args) {\r\n            if (isEmptyCssValue(args.value)) {\r\n                return [];\r\n            }\r\n            return [args.name + ': ' + args.value + ';'];\r\n        },\r\n        formatLength: function (args) {\r\n            if (isEmptyCssValue(args.value)) {\r\n                return '';\r\n            }\r\n            //Normalize numeric values. For example, while JS accepts \"1.\" as a number,\r\n            //\"1.px\" is not a valid CSS length value, so it should be converted to \"1px\".\r\n            const numericValue = parseFloat(String(args.value));\r\n            if (isNaN(numericValue)) {\r\n                return '';\r\n            }\r\n            return '' + numericValue + (args.unit || '');\r\n        },\r\n        shadow: function (args) {\r\n            const mode = args.mode || 'default';\r\n            const color = args.color || '';\r\n            if (mode === 'default') {\r\n                return [];\r\n            }\r\n            if ((mode === 'none') || (color === '') || (color === null) || (color === 'transparent')) {\r\n                return ['box-shadow: none;'];\r\n            }\r\n            if (mode !== 'custom') {\r\n                return [];\r\n            }\r\n            const components = [];\r\n            if (args.inset) {\r\n                components.push('inset');\r\n            }\r\n            const horizontal = args['offset-x'] || 0;\r\n            const vertical = args['offset-y'] || 0;\r\n            const blur = args.blur || 0;\r\n            const spread = args.spread || 0;\r\n            components.push(`${horizontal}px ${vertical}px ${blur}px ${spread}px`);\r\n            const colorOpacity = args.colorOpacity || 1.0;\r\n            if (colorOpacity < 1.0) {\r\n                components.push(convertToRgba(color, colorOpacity));\r\n            }\r\n            else {\r\n                components.push(color);\r\n            }\r\n            return [`box-shadow: ${components.join(' ')};`];\r\n        },\r\n        boxSides: function (args) {\r\n            if (typeof args.cssPropertyPrefix !== 'string') {\r\n                throw new Error('Invalid config for the boxSides generator: missing cssPropertyPrefix');\r\n            }\r\n            const compositeValue = args.value || {};\r\n            const unit = compositeValue.unit || '';\r\n            const declarations = [];\r\n            for (const side of ['top', 'right', 'bottom', 'left']) {\r\n                const value = compositeValue[side];\r\n                if (isEmptyCssValue(value)) {\r\n                    continue;\r\n                }\r\n                const property = args.cssPropertyPrefix + side;\r\n                declarations.push(`${property}: ${value}${unit};`);\r\n            }\r\n            return declarations;\r\n        },\r\n        firstNonEmpty(args) {\r\n            for (const arg of args) {\r\n                if (!isEmptyCssValue(arg)) {\r\n                    return arg;\r\n                }\r\n            }\r\n            return null;\r\n        },\r\n        /**\r\n         * Take a HEX color, convert it to HSL to edit its components,\r\n         * then convert back to HEX.\r\n         *\r\n         * @param args\r\n         */\r\n        editHexAsHsl: function (args) {\r\n            return modifyHexColorAsHsl(args, (color, hue, saturation, lightness) => {\r\n                if (hue !== null) {\r\n                    color = color.hue(hue);\r\n                }\r\n                if (saturation !== null) {\r\n                    color = color.saturation(saturation);\r\n                }\r\n                if (lightness !== null) {\r\n                    color = color.lightness(lightness);\r\n                }\r\n                return color;\r\n            });\r\n        },\r\n        adjustHexAsHsl: function (args) {\r\n            return modifyHexColorAsHsl(args, (color, hue, saturation, lightness) => {\r\n                if (hue !== null) {\r\n                    color = color.hue(constrain(color.hue() + hue, 0, 360));\r\n                }\r\n                if (saturation !== null) {\r\n                    color = color.saturation(constrain(color.saturation() + saturation, 0, 1.0));\r\n                }\r\n                if (lightness !== null) {\r\n                    color = color.lightness(constrain(color.lightness() + lightness, 0, 1.0));\r\n                }\r\n                return color;\r\n            });\r\n        },\r\n        mixColors: function (args) {\r\n            const color1 = args.color1 || '';\r\n            const color2 = args.color2 || '';\r\n            if (isEmptyCssValue(color1) || isEmptyCssValue(color2)) {\r\n                return '';\r\n            }\r\n            const weight = args.weight || 50;\r\n            if (weight <= 0) {\r\n                return color2;\r\n            }\r\n            else if (weight >= 100) {\r\n                return color1;\r\n            }\r\n            return $.Color(color2).transition($.Color(color1), weight / 100).toHexString();\r\n        },\r\n        changeLightness: function (args) {\r\n            const color = args.color || '';\r\n            if (isEmptyCssValue(color)) {\r\n                return '';\r\n            }\r\n            const amount = args.amount || 0;\r\n            if (amount === 0) {\r\n                return color;\r\n            }\r\n            let output = $.Color(color);\r\n            //Amount is a number between 0 and 100, while lightness is between 0.0 and 1.0.\r\n            let newLightness = output.lightness() + (amount / 100);\r\n            //Clamp to 0.0 - 1.0.\r\n            newLightness = constrain(newLightness, 0.0, 1.0);\r\n            return output.lightness(newLightness).toHexString();\r\n        },\r\n        darken: function (args) {\r\n            const color = args.color || '';\r\n            const amount = args.amount || 0;\r\n            return builtinFunctions.changeLightness({ color, amount: -Math.abs(amount) });\r\n        },\r\n        lighten: function (args) {\r\n            const color = args.color || '';\r\n            const amount = args.amount || 0;\r\n            return builtinFunctions.changeLightness({ color, amount: Math.abs(amount) });\r\n        },\r\n        compare: function (args) {\r\n            const value1 = args.value1;\r\n            const value2 = args.value2;\r\n            const operator = args.op;\r\n            const thenResult = (typeof args.thenResult !== 'undefined') ? args.thenResult : true;\r\n            const elseResult = (typeof args.elseResult !== 'undefined') ? args.elseResult : null;\r\n            let result;\r\n            switch (operator) {\r\n                case '==':\r\n                    result = value1 == value2;\r\n                    break;\r\n                case '!=':\r\n                    result = value1 != value2;\r\n                    break;\r\n                case '>':\r\n                    result = value1 > value2;\r\n                    break;\r\n                case '>=':\r\n                    result = value1 >= value2;\r\n                    break;\r\n                case '<':\r\n                    result = value1 < value2;\r\n                    break;\r\n                case '<=':\r\n                    result = value1 <= value2;\r\n                    break;\r\n                default:\r\n                    throw new Error(`Unknown operator: ${operator}`);\r\n            }\r\n            return result ? thenResult : elseResult;\r\n        },\r\n        ifTruthy: function (args) {\r\n            const value = args.value;\r\n            const thenResult = (typeof args.thenResult !== 'undefined') ? args.thenResult : true;\r\n            const elseResult = (typeof args.elseResult !== 'undefined') ? args.elseResult : null;\r\n            return value ? thenResult : elseResult;\r\n        },\r\n        ifSome: function (args) {\r\n            const values = args.values;\r\n            const thenResult = args.thenResult;\r\n            const elseResult = (typeof args.elseResult !== 'undefined') ? args.elseResult : null;\r\n            for (const value of values) {\r\n                if (!!value) {\r\n                    return thenResult;\r\n                }\r\n            }\r\n            return elseResult;\r\n        },\r\n        ifAll: function (args) {\r\n            const values = args.values;\r\n            const thenResult = args.thenResult;\r\n            const elseResult = args.elseResult !== undefined ? args.elseResult : null;\r\n            if (!values || (values.length === 0)) {\r\n                return elseResult;\r\n            }\r\n            for (const value of values) {\r\n                if (!value) {\r\n                    return elseResult;\r\n                }\r\n            }\r\n            return thenResult;\r\n        },\r\n        ifImageSettingContainsImage: function (args) {\r\n            const thenResult = args.thenResult !== undefined ? args.thenResult : true;\r\n            const elseResult = args.elseResult !== undefined ? args.elseResult : null;\r\n            if ((typeof args.value !== 'object') || !args.value) {\r\n                return elseResult;\r\n            }\r\n            const image = args.value;\r\n            const hasAttachment = !!image.attachmentId;\r\n            const hasExternalUrl = !!image.externalUrl;\r\n            const hasImage = hasAttachment || hasExternalUrl;\r\n            return hasImage ? thenResult : elseResult;\r\n        }\r\n    };\r\n    let Preview;\r\n    (function (Preview) {\r\n        const $ = jQuery;\r\n        function isConditionalAtRuleConfig(config) {\r\n            if ((typeof config !== 'object') || (config === null)) {\r\n                return false;\r\n            }\r\n            const configAsRecord = config;\r\n            return ((typeof configAsRecord['t'] === 'string')\r\n                && (configAsRecord['t'] === 'conditionalAtRule')\r\n                && (typeof configAsRecord['identifier'] === 'string'));\r\n        }\r\n        function isRuleSetConfig(config) {\r\n            return ((config !== null)\r\n                && (Array.isArray(config['selectors']))\r\n                && (Array.isArray(config['generators'])));\r\n        }\r\n        const inactiveSettingMarker = { '_ame_inactive_setting': true };\r\n        class PreviewSession {\r\n            constructor(config) {\r\n                this.settings = {};\r\n                this.valueReaders = new Set();\r\n                this.notFound = {};\r\n                this.variables = {};\r\n                this.styleBlocks = [];\r\n                this.stylesheetsToDisable = [];\r\n                this.stylesheetWasEnabled = {};\r\n                /**\r\n                 * Whether this is the first time the preview is being updated.\r\n                 * This is set to false after preview() is called for the first time.\r\n                 */\r\n                this._isBeforeFirstUpdate = true;\r\n                //Optimization: Create bound getters once instead of every time we need\r\n                //to create a setting or variable reference.\r\n                this.settingValueGetter = this.getSettingPreviewValue.bind(this);\r\n                this.variableValueGetter = (variableName) => {\r\n                    if (variableName in this.variables) {\r\n                        return this.variables[variableName].getValue();\r\n                    }\r\n                    return null;\r\n                };\r\n                //Optionally, disable already generated custom stylesheets while the preview\r\n                //is active to prevent old settings from interfering with the preview of new settings.\r\n                if (Array.isArray(config.stylesheetsToDisable)) {\r\n                    this.stylesheetsToDisable = config.stylesheetsToDisable;\r\n                }\r\n                //Variables\r\n                for (const variableName in config.variables) {\r\n                    if (!config.variables.hasOwnProperty(variableName)) {\r\n                        continue;\r\n                    }\r\n                    this.variables[variableName] = this.createValueDescriptor(config.variables[variableName], true);\r\n                }\r\n                //CSS statement groups\r\n                for (const conditionConfig of config.statementGroups) {\r\n                    const statements = this.createCssStatements(conditionConfig.statements);\r\n                    if (statements.length < 1) {\r\n                        continue;\r\n                    }\r\n                    const condition = this.createValueDescriptor(conditionConfig.expression, true);\r\n                    const usedSettingIds = this.getSettingIdsUsedBy(condition);\r\n                    const conditionCallback = () => {\r\n                        //For performance, conditions that reference settings should\r\n                        //only be checked when at least one setting is active.\r\n                        if (usedSettingIds.length > 0) {\r\n                            if (!usedSettingIds.some((id) => this.isSettingActive(id))) {\r\n                                return false;\r\n                            }\r\n                        }\r\n                        const isTruthy = condition.getValue();\r\n                        return !!isTruthy; //Convert to boolean.\r\n                    };\r\n                    this.styleBlocks.push(new PreviewStyleBlock(statements, conditionCallback));\r\n                }\r\n            }\r\n            createValueDescriptor(data, allowUnknownVariables = false) {\r\n                switch (data.t) {\r\n                    case 'constant':\r\n                        return new ConstantValue(data.value);\r\n                    case 'array':\r\n                        return new ArrayValue(data.items.map((valueData) => this.createValueDescriptor(valueData, allowUnknownVariables)));\r\n                    case 'setting':\r\n                        this.registerPreviewableSettingId(data.id);\r\n                        return new SettingReference(data.id, this.settingValueGetter);\r\n                    case 'var':\r\n                        if (!this.variables.hasOwnProperty(data.name) && !allowUnknownVariables) {\r\n                            throw new Error('Unknown variable: ' + data.name);\r\n                        }\r\n                        return new VariableReference(data.name, this.variableValueGetter);\r\n                    case 'funcCall':\r\n                        let functionName;\r\n                        if (data.name in builtinFunctions) {\r\n                            functionName = data.name;\r\n                        }\r\n                        else {\r\n                            throw new Error('Unknown function: ' + data.name);\r\n                        }\r\n                        const func = builtinFunctions[functionName];\r\n                        //Initialize the function arguments.\r\n                        let args;\r\n                        if (Array.isArray(data.args)) {\r\n                            args = data.args.map(arg => this.createValueDescriptor(arg, allowUnknownVariables));\r\n                        }\r\n                        else {\r\n                            args = {};\r\n                            for (const argName in data.args) {\r\n                                if (!data.args.hasOwnProperty(argName)) {\r\n                                    continue;\r\n                                }\r\n                                args[argName] = this.createValueDescriptor(data.args[argName], allowUnknownVariables);\r\n                            }\r\n                        }\r\n                        // @ts-ignore - Can't really statically check this since the values come from the server.\r\n                        return new FunctionCall(args, func);\r\n                }\r\n            }\r\n            /**\r\n             * Get the IDs of all settings that are referenced by the given descriptor.\r\n             *\r\n             * @param descriptor\r\n             * @private\r\n             */\r\n            getSettingIdsUsedBy(descriptor) {\r\n                if (descriptor instanceof SettingReference) {\r\n                    return [descriptor.settingId];\r\n                }\r\n                if (descriptor instanceof ArrayValue) {\r\n                    let result = [];\r\n                    for (const item of descriptor.getItemDescriptors()) {\r\n                        result = result.concat(this.getSettingIdsUsedBy(item));\r\n                    }\r\n                    return uniqueArrayValues(result);\r\n                }\r\n                if (descriptor instanceof FunctionCall) {\r\n                    let result = [];\r\n                    const args = descriptor.args;\r\n                    if (Array.isArray(args)) {\r\n                        for (const arg of args) {\r\n                            result = result.concat(this.getSettingIdsUsedBy(arg));\r\n                        }\r\n                    }\r\n                    else {\r\n                        for (const argName in args) {\r\n                            if (args.hasOwnProperty(argName)) {\r\n                                result = result.concat(this.getSettingIdsUsedBy(args[argName]));\r\n                            }\r\n                        }\r\n                    }\r\n                    return uniqueArrayValues(result);\r\n                }\r\n                if (descriptor instanceof VariableReference) {\r\n                    const varDef = this.getVariableDefinition(descriptor.name);\r\n                    if (varDef === null) {\r\n                        return [];\r\n                    }\r\n                    return this.getSettingIdsUsedBy(varDef);\r\n                }\r\n                return [];\r\n            }\r\n            getVariableDefinition(variableName) {\r\n                if (!this.variables.hasOwnProperty(variableName)) {\r\n                    return null;\r\n                }\r\n                return this.variables[variableName];\r\n            }\r\n            createCssStatements(configs) {\r\n                let results = [];\r\n                for (const config of configs) {\r\n                    if (isRuleSetConfig(config)) {\r\n                        results.push(this.createRuleSetFromConfig(config));\r\n                    }\r\n                    else if (isConditionalAtRuleConfig(config)) {\r\n                        results.push(new ConditionalAtRule(config.identifier, config.condition, (typeof config.nestedStatements === 'undefined')\r\n                            ? []\r\n                            : this.createCssStatements(config.nestedStatements)));\r\n                    }\r\n                    else {\r\n                        console.error('Unknown CSS statement type: ', config);\r\n                    }\r\n                }\r\n                return results;\r\n            }\r\n            createRuleSetFromConfig(config, parent = null) {\r\n                const generatorWrappers = this.makeGeneratorWrappers(config.generators);\r\n                const ruleSet = new CssRuleSet(config.selectors, generatorWrappers, parent);\r\n                const nestedRuleSets = this.createNestedRuleSets(config.nestedStatements, ruleSet);\r\n                ruleSet.setNestedRuleSets(nestedRuleSets);\r\n                return ruleSet;\r\n            }\r\n            createNestedRuleSets(configs, parent = null) {\r\n                let results = [];\r\n                if (!configs) {\r\n                    return results;\r\n                }\r\n                for (const config of configs) {\r\n                    if (!isRuleSetConfig(config)) {\r\n                        throw new Error('A CSS rule set can only contain other rule sets, not other types of statements.');\r\n                    }\r\n                    results.push(this.createRuleSetFromConfig(config, parent));\r\n                }\r\n                return results;\r\n            }\r\n            getPreviewableSettingIDs() {\r\n                return Object.keys(this.settings);\r\n            }\r\n            preview(settingId, value, otherSettingReader) {\r\n                if (this._isBeforeFirstUpdate) {\r\n                    this._isBeforeFirstUpdate = false;\r\n                    this.disableAssociatedStylesheets();\r\n                }\r\n                this.valueReaders.add(otherSettingReader);\r\n                if (!this.settings.hasOwnProperty(settingId)) {\r\n                    this.settings[settingId] = ko.observable(value);\r\n                }\r\n                else {\r\n                    this.settings[settingId](value);\r\n                }\r\n            }\r\n            dispose() {\r\n                //Dispose of all style blocks.\r\n                for (const block of this.styleBlocks) {\r\n                    block.dispose();\r\n                }\r\n                this.reEnableAssociatedStylesheets();\r\n            }\r\n            disableAssociatedStylesheets() {\r\n                for (const stylesheetSelector of this.stylesheetsToDisable) {\r\n                    const $link = $(stylesheetSelector);\r\n                    if ($link.length > 0) {\r\n                        this.stylesheetWasEnabled[stylesheetSelector] = $link.prop('disabled');\r\n                        $link.prop('disabled', true);\r\n                    }\r\n                }\r\n            }\r\n            reEnableAssociatedStylesheets() {\r\n                for (const stylesheetSelector of this.stylesheetsToDisable) {\r\n                    const $link = $(stylesheetSelector);\r\n                    if (($link.length > 0) && this.stylesheetWasEnabled.hasOwnProperty(stylesheetSelector)) {\r\n                        $link.prop('disabled', this.stylesheetWasEnabled[stylesheetSelector]);\r\n                    }\r\n                }\r\n            }\r\n            isSettingActive(settingId) {\r\n                if (this.settings.hasOwnProperty(settingId)) {\r\n                    return this.settings[settingId]() !== inactiveSettingMarker;\r\n                }\r\n                return false;\r\n            }\r\n            getSettingPreviewValue(settingId) {\r\n                if (!this.settings.hasOwnProperty(settingId)) {\r\n                    const value = this.getSettingFromReaders(settingId);\r\n                    this.settings[settingId] = ko.observable(value).extend({ deferred: true });\r\n                }\r\n                const observable = this.settings[settingId];\r\n                let value = observable();\r\n                if (value === inactiveSettingMarker) {\r\n                    value = this.getSettingFromReaders(settingId);\r\n                    observable(value);\r\n                }\r\n                return value;\r\n            }\r\n            getSettingFromReaders(settingId) {\r\n                for (const reader of this.valueReaders) {\r\n                    const value = reader(settingId, this.notFound);\r\n                    if (value !== this.notFound) {\r\n                        return value;\r\n                    }\r\n                }\r\n                throw new Error('Setting not found for preview: ' + settingId);\r\n            }\r\n            makeGeneratorWrappers(generatorConfigs) {\r\n                let generatorWrappers = [];\r\n                for (const generatorConfig of generatorConfigs) {\r\n                    const wrapper = this.makeDeclarationGeneratorWrapper(generatorConfig);\r\n                    if (wrapper !== null) {\r\n                        generatorWrappers.push(wrapper);\r\n                    }\r\n                }\r\n                return generatorWrappers;\r\n            }\r\n            makeDeclarationGeneratorWrapper(config) {\r\n                const generator = this.createValueDescriptor(config);\r\n                return new DeclarationGeneratorWrapper(generator, this);\r\n            }\r\n            registerPreviewableSettingId(settingId) {\r\n                if (!this.settings.hasOwnProperty(settingId)) {\r\n                    this.settings[settingId] = ko.observable(inactiveSettingMarker);\r\n                }\r\n            }\r\n            get isBeforeFirstUpdate() {\r\n                return this._isBeforeFirstUpdate;\r\n            }\r\n        }\r\n        /**\r\n         * Preview manager for the style generator.\r\n         *\r\n         * This is a thin wrapper around the PreviewSession class. It initializes the session\r\n         * as needed and destroys it when the preview is cleared. This makes it simpler to manage\r\n         * active settings, style blocks, and CSS rule-sets: instead of having to carefully\r\n         * track dependencies and deactivate/reactivate them in the right order whenever the preview\r\n         * is disabled/enabled, we can just destroy the session and start over.\r\n         */\r\n        class StyleGeneratorPreview {\r\n            constructor(config) {\r\n                this.config = config;\r\n                this.currentSession = null;\r\n            }\r\n            getOrCreateSession() {\r\n                if (this.currentSession === null) {\r\n                    this.currentSession = new PreviewSession(this.config);\r\n                }\r\n                return this.currentSession;\r\n            }\r\n            getPreviewableSettingIDs() {\r\n                return this.getOrCreateSession().getPreviewableSettingIDs();\r\n            }\r\n            preview(settingId, value, otherSettingReader) {\r\n                const session = this.getOrCreateSession();\r\n                const shouldPreviewAll = (this.config.previewAllOnFirstUpdate && session.isBeforeFirstUpdate);\r\n                session.preview(settingId, value, otherSettingReader);\r\n                if (shouldPreviewAll) {\r\n                    //Preview all registered settings the first time the preview is updated.\r\n                    const notFound = {};\r\n                    for (const otherId of session.getPreviewableSettingIDs()) {\r\n                        const otherValue = otherSettingReader(otherId, notFound);\r\n                        if ((otherId !== settingId) && (otherValue !== notFound)) {\r\n                            session.preview(otherId, otherValue, otherSettingReader);\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n            clearPreview() {\r\n                if (this.currentSession !== null) {\r\n                    this.currentSession.dispose();\r\n                    this.currentSession = null;\r\n                }\r\n            }\r\n        }\r\n        Preview.StyleGeneratorPreview = StyleGeneratorPreview;\r\n        class DeclarationGeneratorWrapper {\r\n            constructor(generator, settingSource) {\r\n                this.generator = generator;\r\n                this.settingSource = settingSource;\r\n                //Introspect the generator and see which settings it uses.\r\n                //This will be useful to determine if the generator is active.\r\n                this.usedSettingIds = DeclarationGeneratorWrapper.findReferencedSettingIds(generator, settingSource);\r\n                this.cssDeclarations = ko.computed({\r\n                    read: () => this.getDeclarations(),\r\n                    deferEvaluation: true,\r\n                }).extend({ deferred: true });\r\n            }\r\n            /**\r\n             * Recursively find all settings used by a value descriptor (such as a function call).\r\n             *\r\n             * @param {ValueDescriptor} thing\r\n             * @param variableSource Needed to get variable definitions and not just the final values.\r\n             */\r\n            static findReferencedSettingIds(thing, variableSource) {\r\n                let settingIds = [];\r\n                if (thing instanceof SettingReference) {\r\n                    settingIds.push(thing.settingId);\r\n                }\r\n                else if (thing instanceof FunctionCall) {\r\n                    if (Array.isArray(thing.args)) {\r\n                        for (const arg of thing.args) {\r\n                            settingIds = settingIds.concat(DeclarationGeneratorWrapper.findReferencedSettingIds(arg, variableSource));\r\n                        }\r\n                    }\r\n                    else {\r\n                        for (const key in thing.args) {\r\n                            settingIds = settingIds.concat(DeclarationGeneratorWrapper.findReferencedSettingIds(thing.args[key], variableSource));\r\n                        }\r\n                    }\r\n                }\r\n                else if (thing instanceof VariableReference) {\r\n                    const value = variableSource.getVariableDefinition(thing.name);\r\n                    if (value !== null) {\r\n                        settingIds = settingIds.concat(DeclarationGeneratorWrapper.findReferencedSettingIds(value, variableSource));\r\n                    }\r\n                }\r\n                return settingIds;\r\n            }\r\n            isActive() {\r\n                //Check if any of the input settings are active.\r\n                let hasSettingLookups = false;\r\n                for (const settingId of this.usedSettingIds) {\r\n                    hasSettingLookups = true;\r\n                    if (this.settingSource.isSettingActive(settingId)) {\r\n                        return true;\r\n                    }\r\n                }\r\n                //If there are no input settings, the generator is always active: it just\r\n                //generates a fixed declaration.\r\n                return !hasSettingLookups;\r\n            }\r\n            getDeclarations() {\r\n                return this.generator.getValue();\r\n            }\r\n            dispose() {\r\n                this.cssDeclarations.dispose();\r\n            }\r\n        }\r\n        class CssStatement {\r\n            constructor() {\r\n                this.cssText = ko.computed({\r\n                    read: () => this.generateCss(),\r\n                    deferEvaluation: true,\r\n                }).extend({ deferred: true });\r\n            }\r\n            dispose() {\r\n                //Dispose the CSS text observable.\r\n                this.cssText.dispose();\r\n            }\r\n        }\r\n        class CssRuleSet extends CssStatement {\r\n            constructor(selectors, declarationSources, parent = null) {\r\n                super();\r\n                this.declarationSources = declarationSources;\r\n                this.nestedRuleSets = ko.observableArray([]);\r\n                if (parent === null) {\r\n                    this.effectiveSelectors = selectors;\r\n                }\r\n                else {\r\n                    this.effectiveSelectors = CssRuleSet.combineSelectors(selectors, parent.effectiveSelectors);\r\n                }\r\n                this.selectorText = this.effectiveSelectors.join(', ');\r\n            }\r\n            static combineSelectors(selectors, parentSelectors) {\r\n                const combinedSelectors = [];\r\n                for (const selector of selectors) {\r\n                    if (selector === '') {\r\n                        continue;\r\n                    }\r\n                    if (selector.includes('&')) {\r\n                        //Insert the parent selectors into the current selector at the position of the \"&\".\r\n                        for (const parentSelector of parentSelectors) {\r\n                            combinedSelectors.push(selector.replace('&', parentSelector.trim()));\r\n                        }\r\n                    }\r\n                    else {\r\n                        //Just append the current selector to the parent selectors.\r\n                        for (const parentSelector of parentSelectors) {\r\n                            combinedSelectors.push(`${parentSelector} ${selector}`);\r\n                        }\r\n                    }\r\n                }\r\n                return combinedSelectors;\r\n            }\r\n            setNestedRuleSets(ruleSets) {\r\n                //Dispose the old rule sets that are not part of the new list.\r\n                for (const oldRuleSet of this.nestedRuleSets()) {\r\n                    if (ruleSets.indexOf(oldRuleSet) === -1) {\r\n                        oldRuleSet.dispose();\r\n                    }\r\n                }\r\n                this.nestedRuleSets(ruleSets);\r\n            }\r\n            generateCss() {\r\n                const declarations = this.getDeclarations();\r\n                const nestedRuleSetParts = [];\r\n                for (const ruleSet of this.nestedRuleSets()) {\r\n                    if (ruleSet.isActive()) {\r\n                        nestedRuleSetParts.push(ruleSet.cssText());\r\n                    }\r\n                }\r\n                let css = '';\r\n                if (declarations.length > 0) {\r\n                    css += this.selectorText + ' {\\n\\t' + declarations.join('\\n\\t') + '\\n}\\n';\r\n                }\r\n                if (nestedRuleSetParts.length > 0) {\r\n                    css += nestedRuleSetParts.join('\\n');\r\n                }\r\n                return css;\r\n            }\r\n            isActive() {\r\n                for (const source of this.declarationSources) {\r\n                    if (source.isActive()) {\r\n                        return true;\r\n                    }\r\n                }\r\n                for (const ruleSet of this.nestedRuleSets()) {\r\n                    if (ruleSet.isActive()) {\r\n                        return true;\r\n                    }\r\n                }\r\n                return false;\r\n            }\r\n            getDeclarations() {\r\n                const declarations = [];\r\n                for (const source of this.declarationSources) {\r\n                    if (source.isActive()) {\r\n                        declarations.push(...source.cssDeclarations());\r\n                    }\r\n                }\r\n                return declarations;\r\n            }\r\n            dispose() {\r\n                //Dispose declaration sources.\r\n                for (const source of this.declarationSources) {\r\n                    source.dispose();\r\n                }\r\n                //Dispose nested rule sets.\r\n                for (const ruleSet of this.nestedRuleSets()) {\r\n                    ruleSet.dispose();\r\n                }\r\n                super.dispose();\r\n            }\r\n        }\r\n        class ConditionalAtRule extends CssStatement {\r\n            constructor(identifier, condition, nestedStatements) {\r\n                super();\r\n                this.identifier = identifier;\r\n                this.condition = condition;\r\n                this.nestedStatements = nestedStatements;\r\n            }\r\n            generateCss() {\r\n                const pieces = [];\r\n                for (const statement of this.nestedStatements) {\r\n                    const css = statement.cssText();\r\n                    if (css !== '') {\r\n                        pieces.push(css);\r\n                    }\r\n                }\r\n                if (pieces.length === 0) {\r\n                    return '';\r\n                }\r\n                return this.getAtRuleText() + ' {\\n\\t' + pieces.join('\\n\\t') + '\\n}';\r\n            }\r\n            getAtRuleText() {\r\n                return '@' + this.identifier + ' ' + this.condition;\r\n            }\r\n            isActive() {\r\n                for (const statement of this.nestedStatements) {\r\n                    if (statement.isActive()) {\r\n                        return true;\r\n                    }\r\n                }\r\n                return false;\r\n            }\r\n            dispose() {\r\n                //Dispose nested statements.\r\n                for (const statement of this.nestedStatements) {\r\n                    statement.dispose();\r\n                }\r\n                super.dispose();\r\n            }\r\n        }\r\n        class PreviewStyleBlock {\r\n            constructor(statements, condition = null) {\r\n                this.statements = statements;\r\n                this.condition = condition;\r\n                this.$styleElement = null;\r\n                this.cssText = ko.computed({\r\n                    read: () => {\r\n                        if ((condition !== null) && !condition()) {\r\n                            return '';\r\n                        }\r\n                        let pieces = [];\r\n                        for (const statement of this.statements) {\r\n                            if (statement.isActive()) {\r\n                                const css = statement.cssText();\r\n                                if (css !== '') {\r\n                                    pieces.push(css);\r\n                                }\r\n                            }\r\n                        }\r\n                        if (pieces.length === 0) {\r\n                            return '';\r\n                        }\r\n                        return pieces.join('\\n');\r\n                    },\r\n                    deferEvaluation: true,\r\n                }).extend({ deferred: true });\r\n                this.updateStyleElement(this.cssText());\r\n                this.cssChangeSubscription = this.cssText.subscribe((cssText) => {\r\n                    this.updateStyleElement(cssText);\r\n                });\r\n            }\r\n            updateStyleElement(cssText) {\r\n                if (cssText === '') {\r\n                    if (this.$styleElement) {\r\n                        this.$styleElement.remove();\r\n                        this.$styleElement = null;\r\n                    }\r\n                    return;\r\n                }\r\n                if (!this.$styleElement) {\r\n                    this.$styleElement = $('<style></style>').appendTo('head');\r\n                }\r\n                this.$styleElement.text(cssText);\r\n            }\r\n            clear() {\r\n                if (this.$styleElement) {\r\n                    this.$styleElement.remove();\r\n                    this.$styleElement = null;\r\n                }\r\n            }\r\n            dispose() {\r\n                //Stop listening for CSS changes.\r\n                this.cssChangeSubscription.dispose();\r\n                this.cssText.dispose();\r\n                //Dispose rule sets.\r\n                for (const ruleset of this.statements) {\r\n                    ruleset.dispose();\r\n                }\r\n                //Remove the style element.\r\n                this.clear();\r\n            }\r\n        }\r\n    })(Preview = AmeStyleGenerator.Preview || (AmeStyleGenerator.Preview = {}));\r\n})(AmeStyleGenerator || (AmeStyleGenerator = {}));\r\n//# sourceMappingURL=style-generator.js.map"], "names": [], "sourceRoot": ""}