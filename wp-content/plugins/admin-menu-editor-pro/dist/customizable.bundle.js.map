{"version": 3, "file": "customizable.bundle.js", "mappings": ";;;;;;;;;;;;;;AAAa;AACN;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA,kDAAkD,YAAY;AAC9D;AACA,qBAAqB;AACrB;AACA;AACA;AACA,kDAAkD,YAAY;AAC9D;AACA,qBAAqB;AACrB;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2DAA2D,SAAS;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oDAAoD,eAAe;AACnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iDAAiD,2BAA2B,kIAAkI;AAC9M;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iDAAiD,iCAAiC,uBAAuB;AACzG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uCAAuC;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,iDAAiD,iCAAiC,wGAAwG;AAC1L;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC,kBAAkB;AACvD;AACA;AACA;AACA;AACA;AACA,yBAAyB,mBAAmB;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,0CAA0C;AACpC;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,4DAA4D;AAC7D", "sources": ["webpack:///./extras/pro-customizables/assets/customizable.js"], "sourcesContent": ["'use strict';\r\nexport var AmeCustomizable;\r\n(function (AmeCustomizable) {\r\n    var some = AmeMiniFunc.some;\r\n    var none = AmeMiniFunc.none;\r\n    var Either = AmeMiniFunc.Either;\r\n    const _ = wsAmeLodash;\r\n    class Setting {\r\n        constructor(id, value = null, defaultValue = null, supportsPostMessage = false, groupTitle = null, validator = null) {\r\n            this.validator = validator;\r\n            this.groupTitle = null;\r\n            /**\r\n             * The last value that was tried to be set. This is used to ignore server-side\r\n             * validation errors when the input value has changed since the request was sent.\r\n             *\r\n             * Displayed validation errors should be relevant to what the user tried\r\n             * to enter, not the currently stored setting value.\r\n             */\r\n            this.lastTriedNewValue = null;\r\n            this.id = id;\r\n            this.underlyingValue = ko.observable(value);\r\n            this.defaultValue = defaultValue;\r\n            this.supportsPostMessage = supportsPostMessage;\r\n            this.groupTitle = groupTitle;\r\n            this.lastTriedNewValue = value;\r\n            this.value = ko.computed({\r\n                read: () => this.underlyingValue(),\r\n                write: (newValue) => {\r\n                    const errors = this.tryUpdate(newValue);\r\n                    if (errors && (errors.length > 0)) {\r\n                        /*\r\n                        We could revert to the previous value here, but there are some cases where\r\n                        that would interfere with the user's input. For example, if the user is\r\n                        manually typing in a URL, the value will be temporarily invalid until they\r\n                        finish entering the protocol and domain name. If we revert to the previous\r\n                        value, the user will have to start over.\r\n\r\n                        Instead, let's leave the invalid value in place and let the user fix it.\r\n                        */\r\n                    }\r\n                },\r\n                owner: this\r\n            });\r\n            this.validationErrors = ko.observableArray();\r\n            this.isValid = ko.computed(() => {\r\n                return (this.validationErrors().length === 0);\r\n            });\r\n        }\r\n        tryUpdate(newValue) {\r\n            this.lastTriedNewValue = newValue;\r\n            const oldValue = this.underlyingValue();\r\n            //Clear validation errors.\r\n            this.validationErrors.removeAll();\r\n            //Validate and sanitize the new value.\r\n            const [sanitizedValue, errors] = this.validate(newValue);\r\n            this.validationErrors.push(...errors);\r\n            if (errors.length > 0) {\r\n                return errors;\r\n            }\r\n            //Remember the last validation subject so that server-side validation results\r\n            //can be ignored if the value has changed since the request was sent.\r\n            this.lastTriedNewValue = sanitizedValue;\r\n            //Only update the underlying value if it has changed.\r\n            if (sanitizedValue !== oldValue) {\r\n                this.underlyingValue(sanitizedValue);\r\n            }\r\n            return [];\r\n        }\r\n        validate(newValue) {\r\n            if (this.validator !== null) {\r\n                const result = this.validator.check(newValue);\r\n                if (result.isLeft()) {\r\n                    return [newValue, [result.value]];\r\n                }\r\n                else if (result.isRight()) {\r\n                    newValue = result.value;\r\n                }\r\n            }\r\n            return [newValue, []];\r\n        }\r\n        /**\r\n         * Add validation errors to the setting if the current value still\r\n         * matches the given value.\r\n         *\r\n         * This is intended as a way to add validation errors that were produced\r\n         * asynchronously, such as by sending the value to the server for validation.\r\n         * The setting's value can change while the validation is in progress,\r\n         * so we need to check that the validated value matches the current one.\r\n         *\r\n         * @param subjectValue\r\n         * @param errors\r\n         */\r\n        addValidationErrorsForValue(subjectValue, errors) {\r\n            if (this.lastTriedNewValue !== subjectValue) {\r\n                return;\r\n            }\r\n            //Add the error(s) only if there is no existing error with the same code.\r\n            const existingCodes = _.keyBy(this.validationErrors(), 'code');\r\n            for (const error of errors) {\r\n                if ((typeof error.code === 'undefined') || !existingCodes.hasOwnProperty(error.code)) {\r\n                    this.validationErrors.push(error);\r\n                }\r\n            }\r\n        }\r\n        clearValidationErrorsForValue(subjectValue) {\r\n            if (this.lastTriedNewValue !== subjectValue) {\r\n                return;\r\n            }\r\n            this.validationErrors.removeAll();\r\n        }\r\n    }\r\n    AmeCustomizable.Setting = Setting;\r\n    function unserializeSettingMap(settings) {\r\n        const collection = new SettingCollection();\r\n        for (const settingId in settings) {\r\n            if (!settings.hasOwnProperty(settingId)) {\r\n                continue;\r\n            }\r\n            const definition = settings[settingId];\r\n            collection.add(unserializeSetting(settingId, definition));\r\n        }\r\n        return collection;\r\n    }\r\n    AmeCustomizable.unserializeSettingMap = unserializeSettingMap;\r\n    function unserializeSetting(settingId, definition) {\r\n        return new Setting(settingId, (typeof definition.value !== 'undefined') ? definition.value : null, (typeof definition.defaultValue !== 'undefined') ? definition.defaultValue : null, (typeof definition.supportsPostMessage !== 'undefined') ? definition.supportsPostMessage : false, (typeof definition.groupTitle !== 'undefined') ? definition.groupTitle : null, (typeof definition.validation !== 'undefined') ? (new Validator(definition.validation)) : null);\r\n    }\r\n    AmeCustomizable.unserializeSetting = unserializeSetting;\r\n    const BuiltinParsers = {\r\n        'numeric': (value, config) => {\r\n            //In some UI controls the observable value is updated as the user types,\r\n            //so this parser/validator should be tolerant and accept partial values.\r\n            let parsed;\r\n            let sanitized;\r\n            if (typeof value === 'number') {\r\n                parsed = sanitized = value;\r\n            }\r\n            else {\r\n                sanitized = (typeof value === 'string') ? value : String(value);\r\n                sanitized = AmeMiniFunc.sanitizeNumericString(sanitized);\r\n                parsed = parseFloat(sanitized);\r\n                if (isNaN(parsed)) {\r\n                    return Either.left({\r\n                        message: 'Value must be a number.',\r\n                        code: 'invalid_number'\r\n                    });\r\n                }\r\n            }\r\n            if (config) {\r\n                if ((typeof config.min !== 'undefined') && parsed < config.min) {\r\n                    return Either.left({\r\n                        message: `Value must be ${config.min} or greater`,\r\n                        code: 'min_value'\r\n                    });\r\n                }\r\n                if (typeof config.max !== 'undefined' && parsed > config.max) {\r\n                    return Either.left({\r\n                        message: `Value must be ${config.max} or lower`,\r\n                        code: 'max_value'\r\n                    });\r\n                }\r\n            }\r\n            return Either.right(sanitized);\r\n        },\r\n        'int': (value) => {\r\n            let parsed = (typeof value === 'number') ? value : parseInt(String(value), 10);\r\n            if (isNaN(parsed)) {\r\n                return Either.left({\r\n                    message: 'Value must be a number.',\r\n                    code: 'invalid_type'\r\n                });\r\n            }\r\n            parsed = Math.floor(parsed);\r\n            return Either.right(parsed);\r\n        }\r\n    };\r\n    class Validator {\r\n        constructor(config) {\r\n            this.config = config;\r\n            this.parsers = [];\r\n            //Converting to null is only allowed if the setting is nullable.\r\n            if (config.convertEsToNull && !config.isNullable) {\r\n                throw new Error('convertEsToNull is only allowed if the setting is nullable.');\r\n            }\r\n            if (config.parsers) {\r\n                for (const [parserId, parserConfig] of config.parsers) {\r\n                    if (!BuiltinParsers.hasOwnProperty(parserId)) {\r\n                        throw new Error(`Unknown parser: ${parserId}`);\r\n                    }\r\n                    this.parsers.push([BuiltinParsers[parserId], parserConfig]);\r\n                }\r\n            }\r\n        }\r\n        check(value) {\r\n            if (value === null) {\r\n                if (this.config.isNullable) {\r\n                    return Either.right(value);\r\n                }\r\n                else {\r\n                    return Either.left({\r\n                        message: 'This setting cannot be null.'\r\n                    });\r\n                }\r\n            }\r\n            if (typeof value === 'string') {\r\n                if (this.config.convertEsToNull && (value === '')) {\r\n                    return Either.right(null);\r\n                }\r\n            }\r\n            for (const [parser, parserConfig] of this.parsers) {\r\n                const result = parser(value, (parserConfig === null) ? undefined : parserConfig);\r\n                if (result.isLeft()) {\r\n                    return result;\r\n                }\r\n                else if (result.isRight()) {\r\n                    value = result.value;\r\n                }\r\n            }\r\n            return Either.right(value);\r\n        }\r\n    }\r\n    class SettingCollection {\r\n        constructor() {\r\n            this.settings = {};\r\n            /**\r\n             * Adding settings to an observable array makes it easier to automatically\r\n             * update computed values like \"are any settings invalid?\".\r\n             */\r\n            this.observableSettings = ko.observableArray();\r\n            const self = this;\r\n            this.hasValidationErrors = ko.pureComputed(() => {\r\n                return _.some(self.observableSettings(), (setting) => {\r\n                    return !setting.isValid();\r\n                });\r\n            });\r\n            this.changeListeners = new Map();\r\n        }\r\n        get(id) {\r\n            if (this.settings.hasOwnProperty(id)) {\r\n                return some(this.settings[id]);\r\n            }\r\n            return none;\r\n        }\r\n        add(setting) {\r\n            this.settings[setting.id] = setting;\r\n            this.observableSettings.push(setting);\r\n            setting.value.subscribe((newValue) => this.onSettingChanged(setting, newValue));\r\n        }\r\n        onSettingChanged(setting, newValue) {\r\n            this.notifyChangeListeners(setting, newValue);\r\n        }\r\n        /**\r\n         * Add a callback that will be called whenever the value of a setting changes.\r\n         *\r\n         * @param callback\r\n         */\r\n        addChangeListener(callback) {\r\n            const id = Symbol();\r\n            this.changeListeners.set(id, callback);\r\n            return id;\r\n        }\r\n        removeChangeListener(id) {\r\n            this.changeListeners.delete(id);\r\n        }\r\n        notifyChangeListeners(setting, newValue) {\r\n            for (const listener of this.changeListeners.values()) {\r\n                listener(setting, newValue);\r\n            }\r\n        }\r\n        getAllSettingIds() {\r\n            return Object.keys(this.settings);\r\n        }\r\n        getAllSettingValues() {\r\n            const values = {};\r\n            for (const id in this.settings) {\r\n                if (this.settings.hasOwnProperty(id)) {\r\n                    values[id] = this.settings[id].value();\r\n                }\r\n            }\r\n            return values;\r\n        }\r\n    }\r\n    AmeCustomizable.SettingCollection = SettingCollection;\r\n    function isSettingConditionData(data) {\r\n        if ((typeof data !== 'object') || (data === null)) {\r\n            return false;\r\n        }\r\n        const dataAsRecord = data;\r\n        return (typeof dataAsRecord.settingId === 'string'\r\n            && typeof dataAsRecord.op === 'string'\r\n            && typeof dataAsRecord.value !== 'undefined');\r\n    }\r\n    class SettingCondition {\r\n        constructor(setting, op, value) {\r\n            this.setting = setting;\r\n            this.op = op;\r\n            this.value = value;\r\n        }\r\n        evaluate() {\r\n            const settingValue = this.setting.value();\r\n            switch (this.op) {\r\n                case '==':\r\n                    //Note the intentional use of == instead of ===.\r\n                    return settingValue == this.value;\r\n                case '!=':\r\n                    return settingValue != this.value;\r\n                case '>':\r\n                    return settingValue > this.value;\r\n                case '<':\r\n                    return settingValue < this.value;\r\n                case '>=':\r\n                    return settingValue >= this.value;\r\n                case '<=':\r\n                    return settingValue <= this.value;\r\n                case 'falsy':\r\n                    return !settingValue;\r\n                case 'truthy':\r\n                    return !!settingValue;\r\n            }\r\n        }\r\n        static fromData(data, findSetting) {\r\n            const setting = findSetting(data.settingId);\r\n            if (!setting || setting.isEmpty()) {\r\n                throw new Error(`Setting with ID \"${data.settingId}\" not found for SettingCondition`);\r\n            }\r\n            return new SettingCondition(setting.get(), data.op, data.value);\r\n        }\r\n    }\r\n    AmeCustomizable.SettingCondition = SettingCondition;\r\n    class UiElement {\r\n        constructor(data, children = []) {\r\n            this.component = data.component || '';\r\n            this.id = data.id || '';\r\n            this.description = data.description || '';\r\n            this.classes = data.classes || [];\r\n            this.styles = data.styles || {};\r\n            this.componentParams = data.params || {};\r\n            this.children = children;\r\n        }\r\n        getComponentParams() {\r\n            return Object.assign(Object.assign({}, this.componentParams), { uiElement: this, id: this.id, description: this.description, classes: this.classes, styles: this.styles, children: this.children });\r\n        }\r\n    }\r\n    AmeCustomizable.UiElement = UiElement;\r\n    class Container extends UiElement {\r\n        constructor(data, children = []) {\r\n            super(data, children);\r\n            this.title = data.title;\r\n        }\r\n        replaceChild(oldChild, newChild) {\r\n            const index = this.children.indexOf(oldChild);\r\n            if (index === -1) {\r\n                throw new Error('Child not found');\r\n            }\r\n            this.children[index] = newChild;\r\n        }\r\n        replaceChildByIndex(index, newChild) {\r\n            this.children[index] = newChild;\r\n        }\r\n    }\r\n    AmeCustomizable.Container = Container;\r\n    class Section extends Container {\r\n        constructor(data, children = []) {\r\n            super(data, children);\r\n            this.preferredRole = data.preferredRole || 'navigation';\r\n        }\r\n    }\r\n    AmeCustomizable.Section = Section;\r\n    class ControlGroup extends Container {\r\n        constructor(data, children = [], enabled = null) {\r\n            super(data, children);\r\n            this.enabled = enabled || ko.observable(true);\r\n            this.labelFor = data.labelFor || null;\r\n        }\r\n        getComponentParams() {\r\n            return Object.assign(Object.assign({}, super.getComponentParams()), { enabled: this.enabled });\r\n        }\r\n    }\r\n    AmeCustomizable.ControlGroup = ControlGroup;\r\n    class InterfaceStructure extends Container {\r\n        constructor(data, children = []) {\r\n            super(data, children);\r\n        }\r\n        getAsSections() {\r\n            let currentAnonymousSection = null;\r\n            let sections = [];\r\n            for (const child of this.children) {\r\n                if (child instanceof Section) {\r\n                    sections.push(child);\r\n                    currentAnonymousSection = null;\r\n                }\r\n                else {\r\n                    if (!currentAnonymousSection) {\r\n                        currentAnonymousSection = new Section({\r\n                            t: 'section',\r\n                            title: '',\r\n                            children: []\r\n                        });\r\n                        sections.push(currentAnonymousSection);\r\n                    }\r\n                    currentAnonymousSection.children.push(child);\r\n                }\r\n            }\r\n            return sections;\r\n        }\r\n    }\r\n    AmeCustomizable.InterfaceStructure = InterfaceStructure;\r\n    class Control extends UiElement {\r\n        constructor(data, settings = {}, enabled = null, children = []) {\r\n            super(data, children);\r\n            this.label = data.label;\r\n            this.settings = settings;\r\n            this.inputClasses = data.inputClasses || [];\r\n            this.inputAttributes = data.inputAttributes || {};\r\n            this.enabled = enabled || ko.observable(true);\r\n            // noinspection PointlessBooleanExpressionJS -- Might not actually be a boolean if sent from the server.\r\n            this.includesOwnLabel = (typeof data.includesOwnLabel !== 'undefined') ? (!!data.includesOwnLabel) : false;\r\n            this.labelTargetId = data.labelTargetId || '';\r\n            this.primaryInputId = data.primaryInputId || '';\r\n            this.settingValidationErrors = ko.pureComputed(() => {\r\n                const errors = [];\r\n                for (const [settingId, setting] of Object.entries(this.settings)) {\r\n                    const settingErrors = setting.validationErrors();\r\n                    if (settingErrors.length > 0) {\r\n                        for (const error of settingErrors) {\r\n                            errors.push([settingId, error]);\r\n                        }\r\n                    }\r\n                }\r\n                return errors;\r\n            });\r\n        }\r\n        getComponentParams() {\r\n            return Object.assign(Object.assign({}, super.getComponentParams()), { settings: this.settings, enabled: this.enabled, label: this.label, primaryInputId: this.primaryInputId });\r\n        }\r\n        getAutoGroupTitle() {\r\n            if (this.settings['value']) {\r\n                const customGroupTitle = this.settings['value'].groupTitle;\r\n                if (customGroupTitle) {\r\n                    return customGroupTitle;\r\n                }\r\n            }\r\n            return this.label;\r\n        }\r\n        /**\r\n         * Create a control group wrapper with this control as its only child.\r\n         */\r\n        createControlGroup() {\r\n            let title = this.getAutoGroupTitle();\r\n            //Some controls like the checkbox already show their own label.\r\n            //Don't add a group title in that case.\r\n            if (this.includesOwnLabel) {\r\n                title = '';\r\n            }\r\n            const data = {\r\n                t: 'control-group',\r\n                title: title\r\n            };\r\n            if (this.labelTargetId) {\r\n                data.labelFor = this.labelTargetId;\r\n            }\r\n            return new ControlGroup(data, [this], this.enabled);\r\n        }\r\n    }\r\n    AmeCustomizable.Control = Control;\r\n    function unserializeUiElement(data, findSetting, dataCustomizer) {\r\n        if (typeof dataCustomizer === 'function') {\r\n            dataCustomizer(data);\r\n        }\r\n        const dataAsRecord = data;\r\n        //Unserialize children recursively.\r\n        let children = [];\r\n        if ((typeof dataAsRecord['children'] !== 'undefined') && Array.isArray(dataAsRecord['children'])) {\r\n            for (const childData of dataAsRecord['children']) {\r\n                children.push(unserializeUiElement(childData, findSetting, dataCustomizer));\r\n            }\r\n        }\r\n        //Unserialize the \"enabled\" condition.\r\n        let enabled = null;\r\n        if ((data.t === 'control') || (data.t === 'control-group')) {\r\n            if (typeof data.enabled !== 'undefined') {\r\n                if (isSettingConditionData(data.enabled)) {\r\n                    const condition = SettingCondition.fromData(data.enabled, findSetting);\r\n                    enabled = ko.pureComputed(() => condition.evaluate());\r\n                }\r\n                else {\r\n                    enabled = ko.pureComputed(() => !!data.enabled);\r\n                }\r\n            }\r\n            else {\r\n                enabled = ko.observable(true);\r\n            }\r\n        }\r\n        switch (data.t) {\r\n            case 'section':\r\n                return new Section(data, children);\r\n            case 'control-group':\r\n                return new ControlGroup(data, children, enabled);\r\n            case 'structure':\r\n                return new InterfaceStructure(data, children);\r\n            case 'control':\r\n                let settings = {};\r\n                if (data.settings) {\r\n                    for (const childName in data.settings) {\r\n                        if (data.settings.hasOwnProperty(childName)) {\r\n                            const settingId = data.settings[childName];\r\n                            const setting = findSetting(settingId);\r\n                            if (setting.isDefined()) {\r\n                                settings[childName] = setting.get();\r\n                            }\r\n                            else {\r\n                                throw new Error('Unknown setting \"' + settingId + '\" referenced by control \"' + data.label + '\".');\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n                return new Control(data, settings, enabled, children);\r\n        }\r\n    }\r\n    AmeCustomizable.unserializeUiElement = unserializeUiElement;\r\n    class SettingReaderRegistry {\r\n        constructor() {\r\n            this.notFound = {};\r\n            this.valueReaders = [];\r\n        }\r\n        registerValueReader(getter, idPrefix = null) {\r\n            this.valueReaders.push({ getter, idPrefix });\r\n        }\r\n        /**\r\n         * Try to find a setting in a registered setting reader.\r\n         */\r\n        getValue(settingId) {\r\n            for (const { getter, idPrefix } of this.valueReaders) {\r\n                if ((idPrefix !== null) && !(settingId.startsWith(idPrefix))) {\r\n                    continue;\r\n                }\r\n                const result = getter(settingId, this.notFound);\r\n                if (result !== this.notFound) {\r\n                    return some(result);\r\n                }\r\n            }\r\n            return none;\r\n        }\r\n    }\r\n    AmeCustomizable.SettingReaderRegistry = SettingReaderRegistry;\r\n    class PreviewRegistry {\r\n        constructor(previewValueGetter) {\r\n            this.previewValueGetter = previewValueGetter;\r\n            this.settingPreviewUpdaters = {};\r\n            this.notFound = {};\r\n            this.allPreviewUpdaters = ko.observableArray([]);\r\n        }\r\n        preview(settingId, value) {\r\n            if (!this.settingPreviewUpdaters.hasOwnProperty(settingId)) {\r\n                return;\r\n            }\r\n            const updaters = this.settingPreviewUpdaters[settingId];\r\n            for (const updater of updaters) {\r\n                updater.preview(settingId, value, this.previewValueGetter);\r\n            }\r\n        }\r\n        clearPreview() {\r\n            for (const updater of this.allPreviewUpdaters()) {\r\n                updater.clearPreview();\r\n            }\r\n        }\r\n        registerPreviewUpdater(settingIds, updater) {\r\n            for (const settingId of settingIds) {\r\n                if (!this.settingPreviewUpdaters.hasOwnProperty(settingId)) {\r\n                    this.settingPreviewUpdaters[settingId] = [];\r\n                }\r\n                this.settingPreviewUpdaters[settingId].push(updater);\r\n            }\r\n            if (this.allPreviewUpdaters.indexOf(updater) < 0) {\r\n                this.allPreviewUpdaters.push(updater);\r\n            }\r\n        }\r\n        registerPreviewCallback(settingId, callback) {\r\n            this.registerPreviewUpdater([settingId], new PreviewCallbackWrapper(callback));\r\n        }\r\n        canPreview(settingId) {\r\n            return (this.settingPreviewUpdaters.hasOwnProperty(settingId)\r\n                && (this.settingPreviewUpdaters[settingId].length > 0));\r\n        }\r\n    }\r\n    AmeCustomizable.PreviewRegistry = PreviewRegistry;\r\n    class PreviewCallbackWrapper {\r\n        constructor(callback) {\r\n            this.callback = callback;\r\n        }\r\n        preview(settingId, value, getSettingValue) {\r\n            this.callback(value);\r\n        }\r\n        clearPreview() {\r\n            //Nothing to do in this case.\r\n        }\r\n    }\r\n    class ThrottledPreviewRegistry extends PreviewRegistry {\r\n        constructor(previewValueGetter, minPreviewRefreshInterval = 40) {\r\n            super(previewValueGetter);\r\n            this.minPreviewRefreshInterval = minPreviewRefreshInterval;\r\n            this.pendingSettings = {};\r\n            this.throttledUpdate = throttleAnimationFrame(this.applyPendingUpdates.bind(this), this.minPreviewRefreshInterval);\r\n        }\r\n        queuePreview(settingId) {\r\n            this.pendingSettings[settingId] = true;\r\n            this.throttledUpdate();\r\n        }\r\n        applyPendingUpdates() {\r\n            //Cancel any pending updates in case this method was called directly.\r\n            this.throttledUpdate.cancel();\r\n            const pendingSettingIds = Object.keys(this.pendingSettings);\r\n            if (pendingSettingIds.length === 0) {\r\n                return;\r\n            }\r\n            this.updatePreview(pendingSettingIds);\r\n            this.pendingSettings = {};\r\n        }\r\n        /**\r\n         * Update the preview for the specified settings.\r\n         *\r\n         * This method is called by the throttled update function, but it can also be called\r\n         * directly if necessary, e.g. to update the preview for all settings when the user\r\n         * opens a settings screen for the first time. Note that calling it will *not* cancel\r\n         * pending updates.\r\n         *\r\n         * @param settingIds\r\n         */\r\n        updatePreview(settingIds) {\r\n            if (settingIds.length < 1) {\r\n                return;\r\n            }\r\n            for (const settingId of settingIds) {\r\n                const value = this.previewValueGetter(settingId, this.notFound);\r\n                if (value !== this.notFound) {\r\n                    this.preview(settingId, value);\r\n                }\r\n            }\r\n        }\r\n        clearPreview() {\r\n            this.throttledUpdate.cancel();\r\n            this.pendingSettings = {};\r\n            super.clearPreview();\r\n        }\r\n    }\r\n    AmeCustomizable.ThrottledPreviewRegistry = ThrottledPreviewRegistry;\r\n    /**\r\n     * Creates a throttled function that runs the specified callback at most once\r\n     * every `minInterval` milliseconds.\r\n     *\r\n     * The callback is always invoked using `requestAnimationFrame()`, so it will be delayed\r\n     * until the next frame even if the required interval has already passed.\r\n     */\r\n    function throttleAnimationFrame(callback, minInterval = 0) {\r\n        /**\r\n         * Expected time between animation frames. Intervals shorter than this will be ineffective.\r\n         */\r\n        const expectedFrameTime = 1000 / 60;\r\n        /**\r\n         * The threshold at which we will use `setTimeout()` instead of `requestAnimationFrame()`.\r\n         */\r\n        const timeoutThreshold = Math.max(1000 / 20, expectedFrameTime * 2 + 1);\r\n        const epsilon = 0.001;\r\n        let requestAnimationFrameId = null;\r\n        let timerId = null;\r\n        let lastCallTimestamp = 0;\r\n        let nextCallTimestamp = 0;\r\n        function animationCallback() {\r\n            requestAnimationFrameId = null;\r\n            const now = Date.now();\r\n            if (nextCallTimestamp <= now) {\r\n                lastCallTimestamp = now;\r\n                callback();\r\n                return;\r\n            }\r\n            else {\r\n                requestAnimationFrameId = window.requestAnimationFrame(animationCallback);\r\n            }\r\n        }\r\n        const invoke = () => {\r\n            if ((requestAnimationFrameId !== null) || (timerId !== null)) {\r\n                return; //Already scheduled.\r\n            }\r\n            nextCallTimestamp = lastCallTimestamp + minInterval;\r\n            const now = Date.now();\r\n            if (nextCallTimestamp <= now) {\r\n                nextCallTimestamp = now + expectedFrameTime - epsilon;\r\n            }\r\n            //Two-stage throttling: If the remaining time is large, use setTimeout().\r\n            //If it's small, use requestAnimationFrame() and go frame by frame.\r\n            const remainingTime = nextCallTimestamp - now;\r\n            if (remainingTime > timeoutThreshold) {\r\n                timerId = window.setTimeout(() => {\r\n                    timerId = null;\r\n                    requestAnimationFrameId = window.requestAnimationFrame(animationCallback);\r\n                }, remainingTime - (expectedFrameTime / 2));\r\n            }\r\n            else {\r\n                //Use requestAnimationFrame.\r\n                requestAnimationFrameId = window.requestAnimationFrame(animationCallback);\r\n            }\r\n        };\r\n        invoke.cancel = () => {\r\n            if (requestAnimationFrameId !== null) {\r\n                window.cancelAnimationFrame(requestAnimationFrameId);\r\n                requestAnimationFrameId = null;\r\n            }\r\n            if (timerId !== null) {\r\n                window.clearTimeout(timerId);\r\n                timerId = null;\r\n            }\r\n        };\r\n        return invoke;\r\n    }\r\n    //endregion\r\n})(AmeCustomizable || (AmeCustomizable = {}));\r\nexport var AmeCustomizableViewModel;\r\n(function (AmeCustomizableViewModel) {\r\n    var SettingCollection = AmeCustomizable.SettingCollection;\r\n    var Setting = AmeCustomizable.Setting;\r\n    var ThrottledPreviewRegistry = AmeCustomizable.ThrottledPreviewRegistry;\r\n    var SettingReaderRegistry = AmeCustomizable.SettingReaderRegistry;\r\n    var lift = AmeMiniFunc.lift;\r\n    class SimpleVm extends ThrottledPreviewRegistry {\r\n        constructor(extraPreviewCondition = null) {\r\n            const getSettingValue = (settingId, defaultResult) => {\r\n                const setting = this.getOrCreateKnownSetting(settingId);\r\n                if (setting !== null) {\r\n                    return setting.value();\r\n                }\r\n                return defaultResult;\r\n            };\r\n            super(getSettingValue, 40);\r\n            this.previewDesired = ko.observable(false);\r\n            this.settings = new SettingCollection();\r\n            this.settingReaders = new SettingReaderRegistry();\r\n            this.isPreviewPossible = ko.pureComputed(() => {\r\n                return this.allPreviewUpdaters().length > 0;\r\n            });\r\n            this.isPreviewEnabled = ko.computed({\r\n                read: () => {\r\n                    if (extraPreviewCondition !== null) {\r\n                        if (!extraPreviewCondition()) {\r\n                            return false;\r\n                        }\r\n                    }\r\n                    return this.getPreviewActiveState();\r\n                },\r\n                write: (newValue) => {\r\n                    this.previewDesired(newValue);\r\n                    if (newValue && !this.getPreviewActiveState()) {\r\n                        //Can't actually enable preview. Reset the checkbox/other input.\r\n                        this.isPreviewEnabled.notifySubscribers();\r\n                    }\r\n                }\r\n            });\r\n            this.isPreviewEnabled.subscribe((newValue) => {\r\n                if (newValue) {\r\n                    this.updatePreview(this.settings.getAllSettingIds());\r\n                }\r\n                else {\r\n                    this.clearPreview();\r\n                }\r\n            });\r\n            this.settings.addChangeListener((setting) => {\r\n                if (!this.isPreviewEnabled()) {\r\n                    return;\r\n                }\r\n                this.queuePreview(setting.id);\r\n            });\r\n        }\r\n        getSettingObservable(settingId, unusedDefaultValue = null) {\r\n            const result = this.getOrCreateKnownSetting(settingId);\r\n            if (result !== null) {\r\n                return result.value;\r\n            }\r\n            throw new Error('Unknown setting ID: ' + settingId);\r\n        }\r\n        getOrCreateKnownSetting(settingId) {\r\n            const result = this.settings.get(settingId);\r\n            if (result.isDefined()) {\r\n                return result.get();\r\n            }\r\n            const foundValue = this.settingReaders.getValue(settingId);\r\n            if (foundValue.isDefined()) {\r\n                const setting = new Setting(settingId, foundValue.get());\r\n                this.settings.add(setting);\r\n                return setting;\r\n            }\r\n            return null;\r\n        }\r\n        registerSettingReader(reader, idPrefix = null) {\r\n            this.settingReaders.registerValueReader(reader, idPrefix);\r\n        }\r\n        getPreviewActiveState() {\r\n            return this.previewDesired() && this.isPreviewPossible();\r\n        }\r\n        getAllSettingValues() {\r\n            return this.settings.getAllSettingValues();\r\n        }\r\n        /**\r\n         * Reread all settings from the value readers. This will be used to reload settings\r\n         * in case the underlying configuration is reset or a new configuration is loaded.\r\n         */\r\n        reloadAllSettings() {\r\n            for (const settingId of this.settings.getAllSettingIds()) {\r\n                lift([this.settings.get(settingId), this.settingReaders.getValue(settingId)], (setting, newValue) => setting.value(newValue));\r\n            }\r\n        }\r\n    }\r\n    AmeCustomizableViewModel.SimpleVm = SimpleVm;\r\n    // noinspection JSUnusedGlobalSymbols -- Not used right now, but kept for testing and prototyping purposes.\r\n    class NullVm {\r\n        constructor() {\r\n            this.settings = new SettingCollection();\r\n        }\r\n        getSettingObservable(settingId, defaultValue = null) {\r\n            const existingSetting = this.settings.get(settingId);\r\n            if (existingSetting.isDefined()) {\r\n                return existingSetting.get().value;\r\n            }\r\n            const setting = new Setting(settingId, defaultValue);\r\n            this.settings.add(setting);\r\n            return setting.value;\r\n        }\r\n        getAllSettingValues() {\r\n            return this.settings.getAllSettingValues();\r\n        }\r\n    }\r\n    AmeCustomizableViewModel.NullVm = NullVm;\r\n})(AmeCustomizableViewModel || (AmeCustomizableViewModel = {}));\r\n//# sourceMappingURL=customizable.js.map"], "names": [], "sourceRoot": ""}