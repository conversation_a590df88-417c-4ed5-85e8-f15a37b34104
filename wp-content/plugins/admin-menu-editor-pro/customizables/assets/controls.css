.ame-image-selector-v2 .ame-image-preview {
  border: 1px dashed #b4b9be;
  text-align: center;
  background: #f1f1f1;
  line-height: 0;
  display: inline-block;
  box-sizing: border-box;
}
.ame-image-selector-v2 .ame-image-preview img {
  max-width: 100%;
}
.ame-image-selector-v2 .ame-image-preview-placeholder {
  display: inline-block;
  padding: 10px;
  line-height: normal;
}
.ame-image-selector-v2 .ame-external-image-url {
  margin-top: 0.4em;
}
.ame-image-selector-v2 .ame-image-selector-actions {
  display: block;
  margin-top: 0.8em;
}
.ame-image-selector-v2 .ame-remove-image-link {
  line-height: 30px;
  margin-left: 1em;
}

.ame-ac-control .ame-image-selector-v2 {
  box-sizing: border-box;
  width: 100%;
}
.ame-ac-control .ame-image-selector-v2 .ame-image-selector-actions {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: baseline;
}
.ame-ac-control .ame-image-selector-v2 .ame-set-external-image-url {
  margin-left: 0.30769231em;
}
.ame-ac-control .ame-image-selector-v2 .ame-remove-image-link {
  margin-left: auto;
}
.ame-ac-control .ame-image-selector-v2 .ame-image-preview {
  box-sizing: border-box;
}
.ame-ac-control .ame-image-selector-v2 .ame-image-preview-empty {
  width: 100%;
}

.ame-code-editor-control-wrap .CodeMirror {
  height: auto;
}
.ame-code-editor-control-wrap .CodeMirror-scroll {
  overflow-y: hidden;
  overflow-x: auto;
  min-height: 100px;
}

.ame-number-input-control select:invalid, .ame-number-input-control select.ame-has-validation-errors, .ame-number-input-control input:invalid, .ame-number-input-control input.ame-has-validation-errors {
  border-color: #d63638;
}
.ame-number-input-control select:invalid:focus, .ame-number-input-control select.ame-has-validation-errors:focus, .ame-number-input-control input:invalid:focus, .ame-number-input-control input.ame-has-validation-errors:focus {
  box-shadow: 0 0 0 1px #d63638;
}

.ame-small-number-input {
  width: 5.1em;
}

.ame-font-size-input {
  width: 5.1em;
}

.ame-container-with-popup-slider {
  overflow: visible;
}

.ame-popup-slider {
  position: absolute;
  border: 1px solid #ccd0d4;
  border-radius: 4px;
  background: white;
  padding: 8px 16px;
  box-shadow: 0 3px 10px 0 rgba(0, 0, 0, 0.2);
}
.ame-popup-slider, .ame-popup-slider * {
  box-sizing: border-box;
}
.ame-popup-slider .ame-popup-slider-bar {
  display: block;
  height: 16px;
  position: relative;
  overflow: visible;
  cursor: pointer;
}
.ame-popup-slider .ame-popup-slider-groove {
  display: block;
  height: 100%;
  min-width: 16px;
  padding: 6px 0;
}
.ame-popup-slider .ame-popup-slider-groove:before {
  display: block;
  content: " ";
  width: 100%;
  height: 100%;
  background-color: #ebebeb;
  border-radius: 3px;
}
.ame-popup-slider .ame-popup-slider-handle {
  cursor: pointer;
  width: 16px;
  height: 16px;
  position: absolute;
  top: 0px;
  margin-left: -8px;
  border-radius: 50%;
  background-color: #fff;
  border: 2px solid #3582c4;
}
.ame-popup-slider .ame-popup-slider-tip {
  display: block;
  width: 24px;
  height: 12px;
  padding-right: 12px;
  padding-left: 1px;
  padding-bottom: 0;
  position: absolute;
  top: -12px;
  overflow: hidden;
  pointer-events: none;
}
.ame-popup-slider .ame-popup-slider-tip:after {
  display: block;
  content: " ";
  width: 12px;
  height: 12px;
  background-color: white;
  box-shadow: 0 0 0 0.9px #ccd0d4;
  transform-origin: left bottom;
  transform: rotate(45deg);
}
.ame-popup-slider .ame-popup-slider-top-tip {
  top: -12px;
}
.ame-popup-slider .ame-popup-slider-bottom-tip {
  top: 100%;
  transform: scaleY(-1);
}

.ame-radio-button-bar-control {
  display: flex;
  flex-direction: row;
}
.ame-radio-button-bar-control input[type=radio], .ame-radio-button-bar-control input[type=checkbox] {
  position: absolute;
  left: -9999em;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  margin: -1px;
}
.ame-radio-button-bar-control > label {
  display: inline-block;
}
.ame-radio-button-bar-control input[type=radio]:checked ~ .button {
  background-color: #dcdcde;
  color: #135e96;
  border-color: #0a4b78;
  box-shadow: inset 0 2px 5px -3px #0a4b78;
  z-index: 1;
}
.ame-radio-button-bar-control > .ame-radio-bar-item:not(:first-child) > .button {
  margin-left: -1px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.ame-radio-button-bar-control > .ame-radio-bar-item:not(:last-child) > .button {
  margin-right: 0;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.ame-radio-button-bar-control input[type=radio]:disabled ~ .button {
  color: #a7aaad;
  border-color: #dcdcde;
  background: #f6f7f7;
  box-shadow: none;
  cursor: default;
}
.ame-radio-button-bar-control .ame-radio-bar-button {
  display: flex;
  align-items: center;
}
.ame-radio-button-bar-control .ame-radio-bar-button.ame-rb-has-label .dashicons {
  margin-right: 0.2em;
}
.ame-radio-button-bar-control .ame-radio-bar-button .dashicons-image-rotate {
  font-size: 17px;
  line-height: 17px;
  height: 16px;
}
.ame-radio-button-bar-control .ame-radio-bar-button .dashicons-no, .ame-radio-button-bar-control .ame-radio-bar-button .dashicons-no-alt {
  font-size: 22px;
  line-height: 22px;
  height: 22px;
}

.ame-rg-has-nested-controls {
  display: grid;
  grid-template-columns: repeat(2, minmax(auto, max-content));
  column-gap: 1.2307692308em;
  row-gap: 0.6153846154em;
  align-items: center;
}
.ame-rg-has-nested-controls .ame-rg-option-label {
  grid-column: 1;
}
.ame-rg-has-nested-controls .ame-rg-nested-control {
  grid-column: 2;
}
.ame-rg-has-nested-controls.ame-rg-no-center-items {
  align-items: normal;
}

.ame-rg-with-color-pickers {
  align-items: normal;
}
.ame-rg-with-color-pickers .ame-rg-has-choice-child {
  margin-top: 0.3846153846em;
}
.ame-rg-with-color-pickers .ame-rg-nested-control .wp-picker-container {
  min-width: 257px;
}

.ame-ac-control .ame-radio-group-component > p {
  margin-top: 8px;
  margin-bottom: 8px;
}
.ame-ac-control .ame-radio-group-component > p:first-of-type {
  margin-top: 0;
}
.ame-ac-control .ame-radio-group-component > p:last-of-type {
  margin-bottom: 0;
}
.ame-ac-control .ame-rg-with-color-pickers .ame-rg-nested-control {
  grid-column: 1;
}
.ame-ac-control .ame-rg-with-color-pickers .ame-rg-nested-control .wp-picker-container {
  min-width: unset;
}

/*# sourceMappingURL=controls.css.map */
