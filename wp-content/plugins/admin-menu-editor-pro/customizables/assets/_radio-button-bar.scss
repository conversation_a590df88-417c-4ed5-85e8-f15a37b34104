@import "../../css/forms";

.ame-radio-button-bar-control {
	display: flex;
	flex-direction: row;

	input[type="radio"], input[type="checkbox"] {
		@include ame-visually-hide-input;
	}

	> label {
		display: inline-block;
	}

	//Selected option. Emulates the default .active style for buttons.
	input[type="radio"]:checked ~ .button {
		background-color: #dcdcde;
		color: #135e96;
		border-color: #0a4b78;
		box-shadow: inset 0 2px 5px -3px #0a4b78;
		z-index: 1;
	}

	> .ame-radio-bar-item:not(:first-child) > .button {
		margin-left: -1px;
		border-top-left-radius: 0;
		border-bottom-left-radius: 0;
	}

	> .ame-radio-bar-item:not(:last-child) > .button {
		margin-right: 0;
		border-top-right-radius: 0;
		border-bottom-right-radius: 0;
	}

	//If the input is disabled, make the button look disabled too.
	input[type="radio"]:disabled ~ .button {
		color: #a7aaad;
		border-color: #dcdcde;
		background: #f6f7f7;
		box-shadow: none;
		cursor: default;
	}

	.ame-radio-bar-button {
		display: flex;
		align-items: center;

		&.ame-rb-has-label {
			.dashicons {
				margin-right: 0.2em;
			}
		}

		//Unfortunately, Dashicons are not all the same size. Let's resize some
		//individual icons to make them look more consistent.
		.dashicons-image-rotate {
			font-size: 17px;
			line-height: 17px;
			height: 16px;
		}

		.dashicons-no, .dashicons-no-alt {
			font-size: 22px;
			line-height: 22px;
			height: 22px;
		}
	}
}