// General form mixins and styles.

@mixin ame-visually-hide-input {
	//Hide an input like a radio or a checkbox but leave it interactive.
	position: absolute;
	left: -9999em;
	overflow: hidden;
	clip: rect(0, 0, 0, 0);
	margin: -1px;
}

$invalidColor: #d63638; //Matches the Theme Customizer.

@mixin ame-invalid-input-styles {
	select, input {
		&:invalid, &.ame-has-validation-errors {
			border-color: $invalidColor;

			//Override the box shadow that WordPress adds on focus.
			&:focus {
				box-shadow: 0 0 0 1px $invalidColor;
			}
		}
	}
}