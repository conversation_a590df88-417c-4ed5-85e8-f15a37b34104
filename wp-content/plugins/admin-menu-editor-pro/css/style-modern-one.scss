$itemBackground: #fafafa;
$itemBorder: #dfdfdf;

//$itemBackground: #f7f7f7;
//$itemBorder: #cacaca;

$itemText: #23282D; //WordPress default.
//$itemText: #5a5a5a; //CodePress default.
//$itemText: #555; //Theme customizer widget headings.

$horizontalPadding: 7px;
$headVerticalPadding: 7px;
$itemWidth: 304px;
$itemMarginBottom: 9px;

$selectedItemBackground: #c7c7c7;
$selectedItemBorder: #999;
$selectedItemText: #23282D;

/*
//Alternative: like the "invalid" state in the menu customizer.
$hiddenItemBackground: #f6c9cc;
$hiddenItemBorder: #f1acb1;
//*/

$hiddenItemBackground: darken($itemBackground, 9);
$hiddenItemBorder: $itemBorder;
$hiddenItemText: #888; //#9a9ea5; //#82878C; //#999

$columnPadding: 10px;
$mainContainerWidth: $itemWidth + $columnPadding * 2;

.ws_container {
	$itemHorizontalMargin: ($mainContainerWidth - $itemWidth) / 2;

	border: 0 solid transparent;
	background: $itemBackground;

	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;

	width: $itemWidth;
	padding: 0;

	margin-top: 0;
	margin-bottom: $itemMarginBottom;
	margin-left: $itemHorizontalMargin;

	box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);

	.ws_item_title {
		color: $itemText;
		padding-left: 0;
		font-weight: 600;
		font-size: 13px;
	}

	.ws_item_head {
		border: 1px solid $itemBorder;
		padding: $headVerticalPadding 0 $headVerticalPadding $horizontalPadding;
	}
}

#ws_menu_editor.ws_is_actor_view input[type="checkbox"].ws_actor_access_checkbox {
	margin-right: 5px;
}

.ws_editbox {
	background: white;
	padding: $horizontalPadding;

	border: 1px solid $itemBorder;
	border-top-width: 0;

	-webkit-border-radius: 0;
	-moz-border-radius: 0;
	border-radius: 0;
}

a.ws_edit_link {
	background: transparent;
	color: #A0A5AA;

	text-align: center;
	border-radius: 0;

	&::before {
		content: "\f140";
		font: normal 20px/1 dashicons;
		display: block;
	}

	&:hover {
		color: #777;
	}
}

.ws_edit_link.ws_edit_link_expanded::before {
	content: "\f142";
}

.ws_toolbar .ws_button {
	border: 1px solid #C0C0C0;
}

.ws_box {
	margin-top: 6px;
}

.ws_menu_separator  {
	.ws_item_head {
		//background: url("../images/menu-arrows.png") no-repeat ($horizontalPadding + 22px) center;
	}

	.ws_item_head::after {
		content: '';
		display: inline-block;
		-webkit-box-sizing: border-box;
		-moz-box-sizing: border-box;
		box-sizing: border-box;

		vertical-align: middle;
		width: $itemWidth * 0.82;
		height: 0;
		$separatorColor: rgba(0, 0, 0, 0.2);

		border: 2px inset $separatorColor;
	}

	.ws_item_title {
		width: 0;
		padding-left: 0;
		padding-right: 0;
	}
}


.ws_menu.ws_active::after {
	right: -22px;
}

//==============================================
//              Selected state
//==============================================
.ws_container.ws_active, .ws_container.ws_is_hidden_for_actor.ws_active {
	z-index: 2;

	.ws_item_head {
		border-color: $selectedItemBorder;
		background-color: $selectedItemBackground;
	}

	.ws_item_title {
		color: $selectedItemText;
	}

	.ws_editbox {
		border-color: $selectedItemBorder;
	}
}

//==============================================
//              Hidden state
//==============================================
.ws_container.ws_is_hidden_for_actor {
	.ws_item_head {
		border-color: $hiddenItemBorder;
		background-color: $hiddenItemBackground;
	}

	.ws_editbox {
		border-color: $hiddenItemBorder;
	}

	.ws_item_title {
		color: $hiddenItemText;
	}
}

//==============================================
//           Compact layout option
//==============================================

.ws_compact_layout {
	$compactBorderColor: $itemBorder; //Alternative: #cacaca
	$compactVerticalPadding: 4px;

	.ws_container {
		margin-top: -1px;
		margin-bottom: 0;

		-webkit-box-shadow: none;
		-moz-box-shadow: none;
		box-shadow: none;

		.ws_item_head {
			padding-top: $compactVerticalPadding;
			padding-bottom: $compactVerticalPadding;
		}

		.ws_item_title {
			//Alternative: thinner, less in-your-face title text.
			//font-weight: normal;
			//font-size: 14px;
		}

		&.ws_active {
			z-index: 2;
		}
	}

	.ws_container:last-child {
		margin-bottom: $itemMarginBottom;
	}

	&.ws_is_hidden_for_actor {
		.ws_item_head, .ws_editbox {
			border-color: $hiddenItemBorder;
		}
	}
	&.ws_active {
		.ws_item_head, .ws_editbox {
			border-color: $selectedItemBorder;
		}
	}
}

#ws_menu_editor #ws_toggle_editor_layout {
	display: block;
}

//====================================================
//  Dropdowns should appear above the selected item.
//====================================================

#ws_icon_selector, #ws_embedded_page_selector {
	z-index: 3;
}

//==============================================
//              Item dragging
//==============================================

.ws_container.ui-sortable-helper {
	box-shadow: 1px 3px 6px 0 rgba(1, 1, 1, 0.4);
}

//==============================================
//           Columns / containers
//==============================================

.ws_main_container {
	width: $mainContainerWidth;

	.ws_toolbar {
		padding: $columnPadding $columnPadding 0;
	}

	.ws_dropzone {
		margin-left: $columnPadding;
		margin-right: $columnPadding;
	}
}

#ws_editor_sidebar {
	padding: ($columnPadding - 4px) $columnPadding;

	.ws_main_button {
		margin-left: 0;
		margin-right: 0;
	}
}


//==============================================
//                  Dialogs
//==============================================

.settings_page_menu_editor {
	.ui-dialog {
		background: white;
		border: 1px solid #c0c0c0;
		border-radius: 0;
	}

	.ui-dialog-titlebar {
		background-color: #fcfcfc;
		border-bottom: 1px solid #dfdfdf;
		height: auto;
		padding: 0;

		.ui-button.ui-dialog-titlebar-close {
			background: none;
			border-style: none;

			color: #666;
			cursor: pointer;
			padding: 0;
			margin: 0;
			position: absolute;
			top: 0;
			right: 0;

			width: 36px;
			height: 36px;

			text-align: center;

			.ui-icon, .ui-button-text {
				display: none;
			}
		}

		.ui-dialog-titlebar-close::before {
			font: normal 20px/36px 'dashicons';
			content: '\f158';

			vertical-align: middle;
			width: 36px;
			height: 36px;
		}

		.ui-dialog-titlebar-close:hover {
			background: transparent none;
			color: #2ea2cc;
		}
	}

	.ui-dialog-title {
		color: #444444;
		font-size: 18px;
		font-weight: 600;
		line-height: 36px;

		padding: 0 36px 0 8px;
		display: block;
	}
}
