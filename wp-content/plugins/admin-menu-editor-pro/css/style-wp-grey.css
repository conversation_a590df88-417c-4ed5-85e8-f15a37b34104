.ws_container {
	padding: 0;
	width: 296px;
	margin-bottom: 5px;

	background: white;

	border: 1px solid #aeaeae;

	-webkit-box-shadow: inset 0 1px 0 #fff;
	box-shadow: inset 0 1px 0 #fff;

	/*-webkit-border-radius: 2px;
	border-radius: 2px;*/
}

/**
 * Item head elements
 */

.ws_item_head {
	padding: 3px;

	background-color: #d9d9d9;
	background-image: -ms-linear-gradient(top, #e9e9e9, #d9d9d9);
	background-image: -moz-linear-gradient(top, #e9e9e9, #d9d9d9);
	background-image: -webkit-gradient(linear, left top, left bottom, from(#e9e9e9), to(#d9d9d9));
	background-image: -webkit-linear-gradient(top, #e9e9e9, #d9d9d9);
	background-image: linear-gradient(to bottom, #e9e9e9, #d9d9d9);
}

.ws_item_title {
	color: #222;
	text-shadow: #FFFFFF 0 1px 0;
}

/**
 * The down-arrow that expands menu settings
 */

.ws_edit_link {
	background: transparent url(../images/arrows.png) no-repeat center 3px;
	overflow: hidden;
	text-indent:-999em;
}

a.ws_edit_link:hover {
	background-image: url(../images/arrows-dark.png);
}

.ws_edit_link:active {
	background-image: url(../images/arrows-dark.png);
}

.ws_edit_link_expanded {
	border-bottom: none;
	border-color: #ffffd0;

	padding-bottom: 1px;
	border-bottom-right-radius: 0;
	border-bottom-left-radius: 0;

	-moz-border-radius-bottomright: 0;
	-moz-border-radius-bottomleft: 0;

	-webkit-border-bottom-right-radius: 0;
	-webkit-border-bottom-left-radius: 0;
}


/**
 * Separators
 */

.ws_menu_separator {
	border-color: #d9d9d9;
}

.ws_menu_separator .ws_item_head {
	min-height: 22px;
	background: #F9F9F9 url("../images/menu-arrows.png") no-repeat 4px 8px;
}

.ws_menu_separator.ws_active .ws_item_head {
	background: #999 url("../images/menu-arrows.png") no-repeat 4px 8px;
}

/* Offset the separator image in actor view to prevent it from overlapping the checkbox.  */
.ws_is_actor_view  .ws_menu_separator .ws_item_head {
	background-position: 25px 8px;
}

/**
 * Active item
 */

.ws_active .ws_item_head {
	background: #777;
	background-image: -webkit-gradient(linear, left bottom, left top, from(#6d6d6d), to(#808080));
	background-image: -webkit-linear-gradient(bottom, #6d6d6d, #808080);
	background-image:    -moz-linear-gradient(bottom, #6d6d6d, #808080);
	background-image:      -o-linear-gradient(bottom, #6d6d6d, #808080);
	background-image: linear-gradient(to top, #6d6d6d, #808080);
}

.ws_active .ws_item_title {
	text-shadow: 0 -1px 0 #333;
	color: #fff;
	border-top-color: #808080;
	border-bottom-color: #6d6d6d;
}

/**
 * Hidden items.
 */
.ws_is_actor_view .ws_container.ws_is_hidden_for_actor .ws_item_head {
	background: #F9F9F9 linear-gradient(to top, #F3F3F3, #FFFFFF);
}

/* selected hidden items */
.ws_is_actor_view .ws_is_hidden_for_actor.ws_active .ws_item_head {
	background: #dedede linear-gradient(to top, #8f8f8f, #a2a2a2);
}
.ws_is_actor_view .ws_is_hidden_for_actor.ws_active .ws_item_title {
	color: #fff;
	text-shadow: none;
}

/* hidden separators */
.ws_is_actor_view .ws_menu_separator.ws_is_hidden_for_actor .ws_item_head {
	/* Override gradient with the separator image. */
	background: url("../images/menu-arrows.png") no-repeat 25px 8px;
}
/* selected hidden separators */
.ws_menu_separator.ws_is_hidden_for_actor.ws_active .ws_item_head {
	background: #aaa url("../images/menu-arrows.png") no-repeat 25px 8px;
}


/**
 * Dropping menus on other menus.
 */

.ws_menu_drop_hover, .ws_menu_drop_hover .ws_item_head  {
	background: #43b529;
}

.ws_menu_drop_hover .ws_item_title {
	text-shadow: none;
}

/**
 * Misc
 */

.ws_editbox {
	/*background-color: #ffffd0;*/
	background-color: #FBFBFB;
	padding: 4px 6px;
}

.ws_input_default input, .ws_input_default select {
	color: gray;
}

/*
 * Show/Hide advanced fields
 */

.ws_toggle_advanced_fields {
	color: #6087CB;
	font-size: 0.85em;
}

.ws_toggle_advanced_fields:visited, .ws_toggle_advanced_fields:active {
	color: #6087CB;
}

.ws_toggle_advanced_fields:hover {
	color: #d54e21;
	text-decoration: underline;
}


/*
 * Toolbars
 */

.ws_button {
	border: 1px solid #c0c0e0;
}

a.ws_button:hover {
	background-color: #d0e0ff;
	border-color: #9090c0;
}

/************************************
           Export and import
*************************************/

.ui-dialog {
	background: white;
	border: 1px solid #c0c0c0;

	border-radius: 0;
}

.ui-dialog-titlebar {
	background-color: #fcfcfc;;
	border-bottom-color: #dfdfdf;
}

.ui-dialog-title {
	color: #444444;
}

.ui-dialog-titlebar-close {
	background-color: transparent;
	border-style: none;

	color: #666;
	cursor: pointer;
	padding: 0;
	position: absolute;
	top: 0;
	right: 0;
	width: 36px;
	height: 30px;
	text-align: center;
}

.ui-dialog-titlebar-close::before {
	font: normal 20px/30px 'dashicons';
	content: '\f158';

	vertical-align: top;
	width: 36px;
	height: 30px;
}

.ui-dialog-titlebar-close:hover {
	background: transparent none;
	color: #2ea2cc;
}