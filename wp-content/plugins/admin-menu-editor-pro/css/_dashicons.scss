/*
This file was automatically generated from /wp-includes/css/dashicons.css.
Last update: 2017-06-07T16:55:55+00:00
*/
.dashicons-menu:before { content: "\f333" !important; }
.dashicons-admin-site:before { content: "\f319" !important; }
.dashicons-admin-media:before { content: "\f104" !important; }
.dashicons-admin-page:before { content: "\f105" !important; }
.dashicons-admin-comments:before { content: "\f101" !important; }
.dashicons-admin-appearance:before { content: "\f100" !important; }
.dashicons-admin-plugins:before { content: "\f106" !important; }
.dashicons-admin-users:before { content: "\f110" !important; }
.dashicons-admin-tools:before { content: "\f107" !important; }
.dashicons-admin-settings:before { content: "\f108" !important; }
.dashicons-admin-network:before { content: "\f112" !important; }
.dashicons-admin-generic:before { content: "\f111" !important; }
.dashicons-admin-home:before { content: "\f102" !important; }
.dashicons-admin-collapse:before { content: "\f148" !important; }
.dashicons-filter:before { content: "\f536" !important; }
.dashicons-admin-customizer:before { content: "\f540" !important; }
.dashicons-admin-multisite:before { content: "\f541" !important; }
.dashicons-admin-links:before, .dashicons-format-links:before { content: "\f103" !important; }
.dashicons-admin-post:before, .dashicons-format-standard:before { content: "\f109" !important; }
.dashicons-format-image:before { content: "\f128" !important; }
.dashicons-format-gallery:before { content: "\f161" !important; }
.dashicons-format-audio:before { content: "\f127" !important; }
.dashicons-format-video:before { content: "\f126" !important; }
.dashicons-format-chat:before { content: "\f125" !important; }
.dashicons-format-status:before { content: "\f130" !important; }
.dashicons-format-aside:before { content: "\f123" !important; }
.dashicons-format-quote:before { content: "\f122" !important; }
.dashicons-welcome-write-blog:before, .dashicons-welcome-edit-page:before { content: "\f119" !important; }
.dashicons-welcome-add-page:before { content: "\f133" !important; }
.dashicons-welcome-view-site:before { content: "\f115" !important; }
.dashicons-welcome-widgets-menus:before { content: "\f116" !important; }
.dashicons-welcome-comments:before { content: "\f117" !important; }
.dashicons-welcome-learn-more:before { content: "\f118" !important; }
.dashicons-image-crop:before { content: "\f165" !important; }
.dashicons-image-rotate:before { content: "\f531" !important; }
.dashicons-image-rotate-left:before { content: "\f166" !important; }
.dashicons-image-rotate-right:before { content: "\f167" !important; }
.dashicons-image-flip-vertical:before { content: "\f168" !important; }
.dashicons-image-flip-horizontal:before { content: "\f169" !important; }
.dashicons-image-filter:before { content: "\f533" !important; }
.dashicons-undo:before { content: "\f171" !important; }
.dashicons-redo:before { content: "\f172" !important; }
.dashicons-editor-ul:before { content: "\f203" !important; }
.dashicons-editor-ol:before { content: "\f204" !important; }
.dashicons-editor-quote:before { content: "\f205" !important; }
.dashicons-editor-alignleft:before { content: "\f206" !important; }
.dashicons-editor-aligncenter:before { content: "\f207" !important; }
.dashicons-editor-alignright:before { content: "\f208" !important; }
.dashicons-editor-insertmore:before { content: "\f209" !important; }
.dashicons-editor-spellcheck:before { content: "\f210" !important; }
.dashicons-editor-distractionfree:before, .dashicons-editor-expand:before { content: "\f211" !important; }
.dashicons-editor-contract:before { content: "\f506" !important; }
.dashicons-editor-kitchensink:before { content: "\f212" !important; }
.dashicons-editor-underline:before { content: "\f213" !important; }
.dashicons-editor-justify:before { content: "\f214" !important; }
.dashicons-editor-textcolor:before { content: "\f215" !important; }
.dashicons-editor-paste-word:before { content: "\f216" !important; }
.dashicons-editor-paste-text:before { content: "\f217" !important; }
.dashicons-editor-removeformatting:before { content: "\f218" !important; }
.dashicons-editor-video:before { content: "\f219" !important; }
.dashicons-editor-customchar:before { content: "\f220" !important; }
.dashicons-editor-outdent:before { content: "\f221" !important; }
.dashicons-editor-indent:before { content: "\f222" !important; }
.dashicons-editor-help:before { content: "\f223" !important; }
.dashicons-editor-strikethrough:before { content: "\f224" !important; }
.dashicons-editor-unlink:before { content: "\f225" !important; }
.dashicons-editor-rtl:before { content: "\f320" !important; }
.dashicons-editor-break:before { content: "\f474" !important; }
.dashicons-editor-code:before { content: "\f475" !important; }
.dashicons-editor-paragraph:before { content: "\f476" !important; }
.dashicons-editor-table:before { content: "\f535" !important; }
.dashicons-align-left:before { content: "\f135" !important; }
.dashicons-align-right:before { content: "\f136" !important; }
.dashicons-align-center:before { content: "\f134" !important; }
.dashicons-align-none:before { content: "\f138" !important; }
.dashicons-lock:before { content: "\f160" !important; }
.dashicons-unlock:before { content: "\f528" !important; }
.dashicons-calendar:before { content: "\f145" !important; }
.dashicons-calendar-alt:before { content: "\f508" !important; }
.dashicons-visibility:before { content: "\f177" !important; }
.dashicons-hidden:before { content: "\f530" !important; }
.dashicons-post-status:before { content: "\f173" !important; }
.dashicons-edit:before { content: "\f464" !important; }
.dashicons-post-trash:before, .dashicons-trash:before { content: "\f182" !important; }
.dashicons-sticky:before { content: "\f537" !important; }
.dashicons-external:before { content: "\f504" !important; }
.dashicons-leftright:before { content: "\f229" !important; }
.dashicons-sort:before { content: "\f156" !important; }
.dashicons-randomize:before { content: "\f503" !important; }
.dashicons-list-view:before { content: "\f163" !important; }
.dashicons-exerpt-view:before, .dashicons-excerpt-view:before { content: "\f164" !important; }
.dashicons-grid-view:before { content: "\f509" !important; }
.dashicons-move:before { content: "\f545" !important; }
.dashicons-hammer:before { content: "\f308" !important; }
.dashicons-art:before { content: "\f309" !important; }
.dashicons-migrate:before { content: "\f310" !important; }
.dashicons-performance:before { content: "\f311" !important; }
.dashicons-universal-access:before { content: "\f483" !important; }
.dashicons-universal-access-alt:before { content: "\f507" !important; }
.dashicons-tickets:before { content: "\f486" !important; }
.dashicons-nametag:before { content: "\f484" !important; }
.dashicons-clipboard:before { content: "\f481" !important; }
.dashicons-heart:before { content: "\f487" !important; }
.dashicons-megaphone:before { content: "\f488" !important; }
.dashicons-schedule:before { content: "\f489" !important; }
.dashicons-wordpress:before { content: "\f120" !important; }
.dashicons-wordpress-alt:before { content: "\f324" !important; }
.dashicons-pressthis:before { content: "\f157" !important; }
.dashicons-update:before { content: "\f463" !important; }
.dashicons-screenoptions:before { content: "\f180" !important; }
.dashicons-cart:before { content: "\f174" !important; }
.dashicons-feedback:before { content: "\f175" !important; }
.dashicons-cloud:before { content: "\f176" !important; }
.dashicons-translation:before { content: "\f326" !important; }
.dashicons-tag:before { content: "\f323" !important; }
.dashicons-category:before { content: "\f318" !important; }
.dashicons-archive:before { content: "\f480" !important; }
.dashicons-tagcloud:before { content: "\f479" !important; }
.dashicons-text:before { content: "\f478" !important; }
.dashicons-media-archive:before { content: "\f501" !important; }
.dashicons-media-audio:before { content: "\f500" !important; }
.dashicons-media-code:before { content: "\f499" !important; }
.dashicons-media-default:before { content: "\f498" !important; }
.dashicons-media-document:before { content: "\f497" !important; }
.dashicons-media-interactive:before { content: "\f496" !important; }
.dashicons-media-spreadsheet:before { content: "\f495" !important; }
.dashicons-media-text:before { content: "\f491" !important; }
.dashicons-media-video:before { content: "\f490" !important; }
.dashicons-playlist-audio:before { content: "\f492" !important; }
.dashicons-playlist-video:before { content: "\f493" !important; }
.dashicons-controls-play:before { content: "\f522" !important; }
.dashicons-controls-pause:before { content: "\f523" !important; }
.dashicons-controls-forward:before { content: "\f519" !important; }
.dashicons-controls-skipforward:before { content: "\f517" !important; }
.dashicons-controls-back:before { content: "\f518" !important; }
.dashicons-controls-skipback:before { content: "\f516" !important; }
.dashicons-controls-repeat:before { content: "\f515" !important; }
.dashicons-controls-volumeon:before { content: "\f521" !important; }
.dashicons-controls-volumeoff:before { content: "\f520" !important; }
.dashicons-yes:before { content: "\f147" !important; }
.dashicons-no:before { content: "\f158" !important; }
.dashicons-no-alt:before { content: "\f335" !important; }
.dashicons-plus:before { content: "\f132" !important; }
.dashicons-plus-alt:before { content: "\f502" !important; }
.dashicons-plus-alt2:before { content: "\f543" !important; }
.dashicons-minus:before { content: "\f460" !important; }
.dashicons-dismiss:before { content: "\f153" !important; }
.dashicons-marker:before { content: "\f159" !important; }
.dashicons-star-filled:before { content: "\f155" !important; }
.dashicons-star-half:before { content: "\f459" !important; }
.dashicons-star-empty:before { content: "\f154" !important; }
.dashicons-flag:before { content: "\f227" !important; }
.dashicons-info:before { content: "\f348" !important; }
.dashicons-warning:before { content: "\f534" !important; }
.dashicons-share:before { content: "\f237" !important; }
.dashicons-share1:before { content: "\f237" !important; }
.dashicons-share-alt:before { content: "\f240" !important; }
.dashicons-share-alt2:before { content: "\f242" !important; }
.dashicons-twitter:before { content: "\f301" !important; }
.dashicons-rss:before { content: "\f303" !important; }
.dashicons-email:before { content: "\f465" !important; }
.dashicons-email-alt:before { content: "\f466" !important; }
.dashicons-facebook:before { content: "\f304" !important; }
.dashicons-facebook-alt:before { content: "\f305" !important; }
.dashicons-networking:before { content: "\f325" !important; }
.dashicons-googleplus:before { content: "\f462" !important; }
.dashicons-location:before { content: "\f230" !important; }
.dashicons-location-alt:before { content: "\f231" !important; }
.dashicons-camera:before { content: "\f306" !important; }
.dashicons-images-alt:before { content: "\f232" !important; }
.dashicons-images-alt2:before { content: "\f233" !important; }
.dashicons-video-alt:before { content: "\f234" !important; }
.dashicons-video-alt2:before { content: "\f235" !important; }
.dashicons-video-alt3:before { content: "\f236" !important; }
.dashicons-vault:before { content: "\f178" !important; }
.dashicons-shield:before { content: "\f332" !important; }
.dashicons-shield-alt:before { content: "\f334" !important; }
.dashicons-sos:before { content: "\f468" !important; }
.dashicons-search:before { content: "\f179" !important; }
.dashicons-slides:before { content: "\f181" !important; }
.dashicons-analytics:before { content: "\f183" !important; }
.dashicons-chart-pie:before { content: "\f184" !important; }
.dashicons-chart-bar:before { content: "\f185" !important; }
.dashicons-chart-line:before { content: "\f238" !important; }
.dashicons-chart-area:before { content: "\f239" !important; }
.dashicons-groups:before { content: "\f307" !important; }
.dashicons-businessman:before { content: "\f338" !important; }
.dashicons-id:before { content: "\f336" !important; }
.dashicons-id-alt:before { content: "\f337" !important; }
.dashicons-products:before { content: "\f312" !important; }
.dashicons-awards:before { content: "\f313" !important; }
.dashicons-forms:before { content: "\f314" !important; }
.dashicons-testimonial:before { content: "\f473" !important; }
.dashicons-portfolio:before { content: "\f322" !important; }
.dashicons-book:before { content: "\f330" !important; }
.dashicons-book-alt:before { content: "\f331" !important; }
.dashicons-download:before { content: "\f316" !important; }
.dashicons-upload:before { content: "\f317" !important; }
.dashicons-backup:before { content: "\f321" !important; }
.dashicons-clock:before { content: "\f469" !important; }
.dashicons-lightbulb:before { content: "\f339" !important; }
.dashicons-microphone:before { content: "\f482" !important; }
.dashicons-desktop:before { content: "\f472" !important; }
.dashicons-laptop:before { content: "\f547" !important; }
.dashicons-tablet:before { content: "\f471" !important; }
.dashicons-smartphone:before { content: "\f470" !important; }
.dashicons-phone:before { content: "\f525" !important; }
.dashicons-smiley:before { content: "\f328" !important; }
.dashicons-index-card:before { content: "\f510" !important; }
.dashicons-carrot:before { content: "\f511" !important; }
.dashicons-building:before { content: "\f512" !important; }
.dashicons-store:before { content: "\f513" !important; }
.dashicons-album:before { content: "\f514" !important; }
.dashicons-palmtree:before { content: "\f527" !important; }
.dashicons-tickets-alt:before { content: "\f524" !important; }
.dashicons-money:before { content: "\f526" !important; }
.dashicons-thumbs-up:before { content: "\f529" !important; }
.dashicons-thumbs-down:before { content: "\f542" !important; }
.dashicons-layout:before { content: "\f538" !important; }
.dashicons-paperclip:before { content: "\f546" !important; }
