$catNavHoverColor: #E5F3FF;
$catNavSelectedColor: #CCE8FF;

.ame-cat-nav-item {
	cursor: pointer;
	margin: 0;
}

.ame-cat-nav-item:hover {
	background-color: $catNavHoverColor;
}

.ame-selected-cat-nav-item {
	background-color: $catNavSelectedColor;
	box-shadow: 0px -1px 0px 0px #99D1FF, 0px 1px 0px 0px #99D1FF;

	//Don't change the color when hovering over a selected item.
	&:hover {
		background-color: $catNavSelectedColor;
	}
}

$navItemLevelPadding: 13px;
@for $level from 2 through 5 {
	$padding: ($level - 2) * $navItemLevelPadding;
	.ame-cat-nav-item.ame-cat-nav-level-#{$level} {
		padding-left: $padding;
	}
}

.ame-cat-nav-toggle {
	visibility: hidden;
	display: inline-block;

	box-sizing: border-box;

	max-height: 100%;
	width: 20px;
	text-align: right;
	vertical-align: middle;

	margin-right: 0.3em;

	&:after {
		font-family: dashicons, sans-serif;
		content: "\f345";
	}

	&:hover {
		color: #3ECEF9;
	}
}

.ame-cat-nav-is-expanded > .ame-cat-nav-toggle {
	&:after {
		content: "\f347";
	}
}

.ame-cat-nav-has-children > .ame-cat-nav-toggle {
	visibility: visible;
}