@charset "UTF-8";
#rex-loading-message {
  margin-top: 10px;
}

#rex-main-ui {
  display: flex;
  flex-direction: row;
  margin-top: 10px;
  width: 100%;
}

#rex-content-container,
#rex-main-buttons {
  border: 1px solid #ccd0d4;
  background: #fff;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}

#rex-content-container {
  display: flex;
  flex-grow: 80;
  padding: 0;
  overflow-x: hidden;
}

#rex-action-sidebar {
  box-sizing: border-box;
  width: 170px;
  flex-grow: 0;
  flex-shrink: 0;
  align-self: flex-start;
  display: flex;
  flex-direction: column;
  margin: 0 0 0 15px;
}
#rex-action-sidebar.metabox-holder {
  padding-top: 0;
}
#rex-action-sidebar .rex-action-separator {
  height: 10px;
}

#rex-main-buttons {
  padding: 10px 8px;
}

#rex-main-buttons,
#rex-related-widget {
  min-width: unset;
  margin: 0;
}

#rex-related-widget {
  margin-top: 15px;
}
#rex-related-widget .hndle {
  cursor: initial;
}
#rex-related-widget .ws-ame-postbox-content {
  margin-bottom: 0;
}

#rex-category-sidebar {
  width: 240px;
  flex-grow: 0;
  flex-shrink: 0;
  position: relative;
  border-right: 1px solid #ccd0d4;
  padding: 10px 0;
  background: #f8f8f8;
}
#rex-category-sidebar > ul {
  margin-top: 0;
}
#rex-category-sidebar .rex-nav-item {
  cursor: pointer;
  margin: 0;
  padding: 3px 8px 3px 10px;
}
#rex-category-sidebar .rex-nav-item:hover {
  background-color: #E5F3FF;
}
#rex-category-sidebar .rex-selected-nav-item {
  background-color: #CCE8FF;
  box-shadow: 0px -1px 0px 0px #99D1FF, 0px 1px 0px 0px #99D1FF;
}
#rex-category-sidebar .rex-selected-nav-item:hover {
  background-color: #CCE8FF;
}
#rex-category-sidebar .rex-nav-level-2 {
  padding-left: 0px;
}
#rex-category-sidebar .rex-nav-level-3 {
  padding-left: 13px;
}
#rex-category-sidebar .rex-nav-level-4 {
  padding-left: 26px;
}
#rex-category-sidebar .rex-nav-level-5 {
  padding-left: 39px;
}
#rex-category-sidebar .rex-nav-toggle {
  visibility: hidden;
  display: inline-block;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  max-height: 100%;
  width: 20px;
  text-align: right;
  vertical-align: middle;
}
#rex-category-sidebar .rex-nav-toggle:after {
  font-family: dashicons, sans-serif;
  content: "\f345";
}
#rex-category-sidebar .rex-nav-toggle:hover {
  color: #3ECEF9;
}
#rex-category-sidebar .rex-nav-is-expanded .rex-nav-toggle:after {
  content: "\f347";
}
#rex-category-sidebar .rex-nav-has-children .rex-nav-toggle {
  visibility: visible;
}
#rex-category-sidebar .rex-dropdown-trigger {
  position: absolute;
  right: 0;
  top: 0;
  padding: 12px 10px 3px 8px;
}
#rex-category-sidebar .rex-nav-item {
  display: flex;
  flex-wrap: nowrap;
  align-items: baseline;
  height: 21px;
  padding-top: 4px;
  padding-bottom: 2px;
}
#rex-category-sidebar .rex-nav-item .rex-nav-toggle {
  flex-shrink: 0;
  margin-right: 0.3em;
  align-self: stretch;
  padding: 1px 0;
}
#rex-category-sidebar .rex-nav-item .rex-capability-count {
  flex-shrink: 0;
  margin-left: 0.3em;
  margin-right: 0.3em;
}
#rex-category-sidebar .rex-nav-item .rex-nav-item-header {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

#rex-capability-view-container {
  flex-grow: 70;
  padding: 10px 10px;
  overflow-x: hidden;
}

#rex-capability-view {
  width: 100%;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}

.rex-category {
  box-sizing: border-box;
  min-width: 160px;
  width: 250px;
  flex-grow: 0;
  flex-shrink: 0;
  flex-basis: auto;
  padding: 0;
  margin: 0 16px 16px 0;
  border: 1px solid #ccd0d4;
}
.rex-category .rex-category-name {
  font-weight: 600;
}
.rex-category .rex-category-subheading {
  display: none;
  color: #666;
  font-size: 12px;
  font-variant: small-caps;
}
.rex-category .rex-category-subtitle {
  color: #888;
  font-size: 0.95em;
  font-family: Consolas, Monaco, monospace;
}
.rex-category .rex-category-contents {
  box-sizing: border-box;
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  padding: 10px;
}
.rex-category.rex-has-subcategories {
  width: 100%;
  flex-basis: 100%;
}
.rex-category .rex-category-header {
  padding: 8px 10px;
  border-bottom: 1px solid #ccd0d4;
}
.rex-category.rex-top-category {
  border: none;
  margin: 0 0 10px 0;
  padding: 0;
}
.rex-category.rex-top-category > .rex-category-header {
  color: #23282d;
  font-size: 1.3em;
  margin: 1em 0;
  padding: 0;
  border-bottom: none;
}
.rex-category.rex-top-category > .rex-category-contents {
  padding: 0;
}
.rex-category.rex-sub-category {
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}
.rex-category.rex-sub-category > .rex-category-header {
  background: #fafafa;
}

.rex-desired-columns-1 {
  width: 250px;
  flex-grow: 0;
  max-width: 500px;
}

.rex-desired-columns-2 {
  width: 516px;
  flex-grow: 0;
  max-width: 1032px;
}

.rex-desired-columns-3 {
  width: 782px;
  flex-grow: 0;
  max-width: 1564px;
}

.rex-desired-columns-max {
  flex-basis: 100%;
  width: 100%;
}

@media screen and (max-width: 1432px) {
  .rex-desired-columns-3 {
    flex-basis: 100%;
    width: 100%;
  }
}
@media screen and (max-width: 1168px) {
  .rex-desired-columns-2 {
    flex-basis: 100%;
    width: 100%;
  }

  .rex-desired-columns-3 {
    flex-basis: 100%;
    width: 100%;
  }
}
.rex-full-width-categories .rex-category {
  width: 100%;
  max-width: unset;
}
.rex-full-width-categories .rex-desired-columns-1 > .rex-category-contents > .rex-permission-list {
  column-count: 1;
  max-width: 300px;
}

/*
 * Ensure that each category contains no more than the desired number of columns.
 * This is done by adding an invisible space filler element to the end of the permission list.
 *
 * Warning: This hack is not perfect. It can allow n+1 columns sometimes.
 */
@media screen and (min-width: 1292px) and (max-width: 1501px) {
  .rex-full-width-categories .rex-desired-columns-2 > .rex-category-contents > .rex-permission-list::after {
    content: "filler";
    display: block;
    background: yellowgreen;
    font-size: 13px;
    height: 81px;
    visibility: hidden;
  }
}
@media screen and (min-width: 1502px) and (max-width: 1711px) {
  .rex-full-width-categories .rex-desired-columns-2 > .rex-category-contents > .rex-permission-list::after {
    content: "filler";
    display: block;
    background: yellowgreen;
    font-size: 13px;
    height: 162px;
    visibility: hidden;
  }
  .rex-full-width-categories .rex-desired-columns-3 > .rex-category-contents > .rex-permission-list::after {
    content: "filler";
    display: block;
    background: yellowgreen;
    font-size: 13px;
    height: 81px;
    visibility: hidden;
  }
}
@media screen and (min-width: 1712px) {
  .rex-full-width-categories .rex-desired-columns-2 > .rex-category-contents > .rex-permission-list::after {
    content: "filler";
    display: block;
    background: yellowgreen;
    font-size: 13px;
    height: 243px;
    visibility: hidden;
  }
  .rex-full-width-categories .rex-desired-columns-3 > .rex-category-contents > .rex-permission-list::after {
    content: "filler";
    display: block;
    background: yellowgreen;
    font-size: 13px;
    height: 162px;
    visibility: hidden;
  }
  .rex-full-width-categories .rex-desired-columns-4 > .rex-category-contents > .rex-permission-list::after {
    content: "filler";
    display: block;
    background: yellowgreen;
    font-size: 13px;
    height: 81px;
    visibility: hidden;
  }
}
.rex-show-category-subheadings .rex-category .rex-category-subheading {
  display: block;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.rex-capability-count {
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  border-radius: 10px;
  font-size: 12px;
}
.rex-capability-count:before {
  content: "(";
}
.rex-capability-count:after {
  content: ")";
}

.rex-enabled-capability-count + .rex-total-capability-count:before {
  content: "/";
}

.rex-permission-list {
  box-sizing: border-box;
  width: 100%;
  columns: 200px;
  column-gap: 10px;
}
.rex-permission {
  box-sizing: border-box;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 13px;
  height: 27px;
  vertical-align: baseline;
  break-inside: avoid-column;
  display: flex;
}
.rex-permission label, .rex-permission .rex-permission-tip-trigger {
  vertical-align: baseline;
  padding-top: 3px;
  padding-bottom: 3px;
}
.rex-permission label {
  flex-grow: 1;
  flex-shrink: 1;
  flex-basis: 50px;
  overflow: hidden;
  text-overflow: ellipsis;
}
.rex-permission .rex-permission-tip-trigger {
  flex-grow: 0;
  flex-shrink: 0;
  flex-basis: 20px;
}

.rex-is-redundant {
  color: #888;
}

.rex-is-explicitly-denied input[type=checkbox] {
  border-color: red;
}

.rex-is-personal-override.rex-is-explicitly-denied input[type=checkbox] {
  background-color: #ffe5e5;
}
.rex-is-personal-override input[type=checkbox]:checked {
  background-color: #d9ffd9;
  border-color: green;
}

.rex-permission-tip-trigger {
  visibility: hidden;
  display: inline-block;
  min-width: 20px;
  height: 100%;
  margin: 0;
  padding-left: 2px;
  cursor: pointer;
  color: #777;
}
.rex-permission-tip-trigger:hover {
  color: #0096dd;
}

.rex-permission:hover {
  background-color: #fafafa;
}
.rex-permission:hover .rex-permission-tip-trigger {
  visibility: visible;
}

.rex-tooltip {
  max-width: 700px;
}
.rex-tooltip .rex-tooltip-section-container {
  display: flex;
  flex-direction: column;
  flex-wrap: nowrap;
}
.rex-tooltip .rex-tooltip-section {
  max-width: 400px;
}

#rex-permission-tip {
  overflow-y: auto;
  max-height: 600px;
}
#rex-permission-tip h4 {
  margin-bottom: 0.4em;
}
#rex-permission-tip .rex-tip-granted-permissions {
  list-style: disc inside;
  margin-top: 0;
  margin-bottom: 0;
}
#rex-permission-tip .rex-documentation-link {
  display: inline-block;
  max-width: 100%;
  overflow-wrap: break-word;
}

.rex-capability-inheritance-breakdown tbody tr:nth-child(2n+1) {
  background-color: #F9F9F9;
}
.rex-capability-inheritance-breakdown .rex-is-decisive-actor td:first-child:after {
  content: "🡄";
  display: inline-block;
  font-weight: bold;
  margin-left: 0.5em;
}

#rex-view-toolbar {
  background: #fcfcfc;
  border-bottom: 1px solid #ddd;
  padding: 0 8px 10px 8px;
  margin: -10px -10px 0 -10px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
#rex-view-toolbar > * {
  margin-top: 10px;
}
#rex-view-toolbar .button {
  vertical-align: middle;
}
#rex-view-toolbar > label {
  margin-right: 10px;
}
#rex-view-toolbar .rex-dropdown-trigger .dashicons {
  line-height: 1.3;
}

#rex-quick-search-query {
  min-width: 250px;
  max-width: 100%;
  margin-right: 10px;
}

#rex-misc-view-options-button {
  margin-left: auto;
  margin-right: 10px;
}

.rex-search-highlight {
  background-color: #ffff00;
}

.rex-permission-table th input[type=checkbox] {
  vertical-align: middle;
  margin: -4px 4px -1px 0;
}
.rex-permission-table tbody tr:nth-child(2n+1) {
  background-color: #F9F9F9;
}
.rex-permission-table td ul {
  margin: 0;
}
.rex-permission-table .rex-base-cap-notice {
  color: #888;
}

/* Switch to fixed layout in narrow viewports to prevent overflow. */
@media screen and (max-width: 1540px) {
  .rex-permission-table {
    table-layout: fixed;
    max-width: 100%;
  }
  .rex-permission-table .rex-category-name-column {
    width: 20%;
  }

  .rex-readable-names-enabled .rex-permission-table {
    table-layout: fixed;
    max-width: 100%;
  }
  .rex-readable-names-enabled .rex-permission-table .rex-category-name-column {
    width: 25%;
  }
}
/* The taxonomy table needs a wider screen because it has more columns. */
@media screen and (max-width: 1650px) {
  #rex-taxonomy-summary-category .rex-permission-table {
    table-layout: fixed;
    max-width: 100%;
  }
  #rex-taxonomy-summary-category .rex-permission-table .rex-category-name-column {
    width: 25%;
  }
}
/*
When in "human readable" mode, the taxonomy table doesn't show capability names,
so it won't overflow its container unless the viewport is very small.
*/
.rex-readable-names-enabled #rex-taxonomy-summary-category .rex-permission-table {
  table-layout: auto;
  max-width: 600px;
}
.rex-readable-names-enabled #rex-taxonomy-summary-category .rex-permission-table .rex-capability-name, .rex-readable-names-enabled #rex-taxonomy-summary-category .rex-permission-table .rex-permission-tip-trigger {
  display: none;
}
.rex-readable-names-enabled #rex-taxonomy-summary-category .rex-permission-table .rex-permission, .rex-readable-names-enabled #rex-taxonomy-summary-category .rex-permission-table th[scope=col] {
  text-align: center;
}
.rex-readable-names-enabled #rex-taxonomy-summary-category .rex-permission-table .rex-category-name-column {
  width: unset;
}

@media screen and (max-width: 1200px) {
  .rex-readable-names-enabled #rex-taxonomy-summary-category .rex-permission-table {
    table-layout: fixed;
    max-width: 100%;
  }
  .rex-readable-names-enabled #rex-taxonomy-summary-category .rex-permission-table .rex-category-name-column {
    width: 40%;
  }
}
#rex-action-sidebar .rex-action-button {
  display: block;
  margin-bottom: 4px;
  width: 100%;
}

#rex-permission-list-view {
  column-width: 240px;
  column-gap: 16px;
  padding-top: 8px;
}

#rex-category-view-spacer {
  width: 100%;
  height: 10px;
}

.rex-dropdown-trigger {
  display: inline-block;
  box-sizing: border-box;
  cursor: pointer;
  padding: 2px;
  color: #aaa;
  text-decoration: none;
}
.rex-dropdown-trigger:hover, .rex-dropdown-trigger:focus {
  color: #777;
  text-decoration: none;
}

.rex-dropdown {
  position: absolute;
  border: 1px solid #ccd0d4;
  background: #fff;
  box-shadow: 0 3px 5px rgba(0, 0, 0, 0.2);
  padding: 10px 8px;
  z-index: 100;
}
.rex-dropdown .rex-dropdown-item {
  display: block;
  margin-bottom: 10px;
}
.rex-dropdown .rex-dropdown-item:last-child {
  margin-bottom: 0;
}
.rex-dropdown .rex-dropdown-sub-item {
  margin-left: 1em;
}
.rex-dropdown .rex-dropdown-item > .rex-dropdown-item {
  margin-bottom: 6px;
}
.rex-dropdown .rex-dropdown-item > .rex-dropdown-item:last-child {
  margin-bottom: 0;
}

.ui-dialog .ui-dialog-buttonpane {
  background: #fcfcfc;
  border-top: 1px solid #dfdfdf;
  padding: 8px;
}
.ui-dialog .ui-dialog-buttonpane:after {
  clear: both;
  content: "";
  min-height: 0;
  display: table;
  border-collapse: collapse;
}
.ui-dialog .ui-dialog-buttonset {
  width: 100%;
}
.ui-dialog .ui-dialog-buttonset .ui-button.rex-dialog-cancel-button, .ui-dialog .ui-dialog-buttonset .ui-button.ame-dialog-cancel-button {
  float: right;
  margin-right: 0 !important;
}
.ui-dialog .ui-dialog-buttonset .ui-button {
  float: left;
}

.rex-dialog input[type=text], .rex-dialog select {
  box-sizing: border-box;
  display: block;
  width: 100%;
}

.rex-dialog-section {
  margin-top: 0;
}

#rex-delete-capability-dialog .rex-deletable-capability-container {
  max-height: 400px;
  overflow-y: auto;
}
#rex-delete-capability-dialog .rex-deletable-capability-list {
  margin-top: 0;
  list-style-type: none;
}

#rex-add-capability-dialog #rex-new-capability-name {
  box-sizing: border-box;
  width: 100%;
}
#rex-add-capability-dialog #rex-add-capability-validation-message {
  min-height: 40px;
  margin-bottom: 6px;
}

#rex-delete-role-dialog .rex-deletable-role-list-container {
  max-height: 380px;
  overflow-y: auto;
  margin-top: 10px;
}
#rex-delete-role-dialog .rex-deletable-role-list {
  table-layout: fixed;
}
#rex-delete-role-dialog .rex-deletable-role-list tbody tr:nth-child(2n+1) {
  background-color: #F9F9F9;
}
#rex-delete-role-dialog .rex-role-name-column > label {
  display: inline-block;
  width: 100%;
}
#rex-delete-role-dialog .rex-role-usage-column {
  width: 6em;
  max-width: 30%;
  color: #888;
  text-align: right;
}

#rex-editable-roles-container {
  display: flex;
}
#rex-editable-roles-container .ame-role-table {
  min-width: 190px;
  border: 1px solid #ccd0d4;
  border-right-style: none;
}
#rex-editable-roles-container .ame-role-table td {
  cursor: pointer;
}
#rex-editable-roles-container .ame-selected-role-table-row {
  background: #CCE8FF;
}
#rex-editable-roles-container .ame-selected-role-table-row .ame-selected-role-tip {
  visibility: visible;
}
#rex-editable-roles-container .ame-selected-role-table-row .ame-column-role-name {
  font-weight: bold;
}
#rex-editable-roles-container .ame-column-selected-role-tip {
  position: relative;
  padding: 0;
  min-width: 30px;
}
#rex-editable-roles-container .ame-selected-role-tip {
  visibility: hidden;
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  position: absolute;
  top: 0;
  right: -2px;
  border-right: 1px solid white;
}
#rex-editable-roles-container .ame-selected-role-tip .ame-rex-svg-triangle {
  box-sizing: border-box;
  position: absolute;
  right: 0;
  height: 100%;
}
#rex-editable-roles-container .ame-selected-role-tip .ame-rex-svg-triangle polygon {
  fill: white;
  stroke: white;
  stroke-width: 1px;
}

#rex-editable-roles-options {
  padding: 4px 10px 10px 10px;
  border: 1px solid #ccd0d4;
}
#rex-editable-roles-options fieldset > p:first-of-type {
  margin-top: 0;
}

#rex-editable-role-list {
  margin-left: 1em;
  margin-top: 0;
}

#rex-user-role-list {
  border-right: 1px solid #ccd0d4;
  padding: 10px 8px;
  background: #f8f8f8;
}
#rex-user-role-list p:first-child {
  margin-top: 0;
}

#rex-primary-user-role {
  display: block;
}

.rex-user-role-option-list {
  margin-top: 0;
}

/*# sourceMappingURL=role-editor.css.map */
