//These definitions were automatically generated.
//Do not edit this file directly.
///*{WS_PCGN_GENERATED_FILE}*/

interface AmeEhBasePrefInterface {
	save(): void;
	enableAutoSave(debounceTimeout: number): void;
}

interface AmeEhIsExplanationHidden extends AmeEhBasePrefInterface, Function {
	observable(ko: KnockoutStatic): KnockoutComputed<number>;
}
declare const ameEhIsExplanationHidden: AmeEhIsExplanationHidden;

interface AmeEhUserPreferencesKoProxy{
	numberOfColumns: KnockoutComputed<number>;
	csExpandedCategories: KnockoutComputed<string>;
}
interface AmeEhUserPreferences extends AmeEhBasePrefInterface{
	numberOfColumns: number;
	csExpandedCategories: string;
	observableObject(ko: KnockoutStatic): AmeEhUserPreferencesKoProxy;
}
declare const ameEhUserPreferences: AmeEhUserPreferences;
