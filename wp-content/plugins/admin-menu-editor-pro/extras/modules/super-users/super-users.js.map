{"version": 3, "file": "super-users.js", "sourceRoot": "", "sources": ["super-users.ts"], "names": [], "mappings": ";AAAA,kDAAkD;AAClD,gDAAgD;AAChD,wDAAwD;AACxD,uCAAuC;AACvC,0EAA0E;AAK1E,MAAM,aAAa;IAWlB,YAAY,QAAa;QAHlB,kBAAa,GAAW,UAAU,CAAC;QAIzC,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,CAAC;QACxC,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC,gBAAgB,CAAC;QAElD,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC,eAAe,CAAC,EAAe,CAAC,CAAC;QACtD,aAAa,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC,WAAW,EAAE,EAAE;YAC5D,IAAI,IAAI,GAAG,OAAO,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;YACrD,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;gBACvC,SAAS,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;aAC3B;YACD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;QAElD,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC,QAAQ,CAAS,GAAW,EAAE;YACpD,OAAO,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;QAEH,8CAA8C;QAC9C,IAAI,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,CAAC;QACzD,IAAI,UAAU,GAAG,EAAE,CAAC,UAAU,CAAU,CAAC,OAAO,YAAY,KAAK,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,YAAY,KAAK,GAAG,CAAC,CAAC,CAAC;QAE/G,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC,QAAQ,CAAU;YACzC,IAAI,EAAE,GAAY,EAAE;gBACnB,OAAO,UAAU,EAAE,CAAC;YACrB,CAAC;YACD,KAAK,EAAE,CAAC,KAAc,EAAE,EAAE;gBACzB,MAAM,CAAC,MAAM,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,EAAC,OAAO,EAAE,EAAE,EAAC,CAAC,CAAC;gBACxE,UAAU,CAAC,KAAK,CAAC,CAAC;YACnB,CAAC;SACD,CAAC,CAAC;IACJ,CAAC;IAEM,UAAU,CAAC,IAAa;QAC9B,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC;IAEM,WAAW,CAAC,IAAa;QAC/B,OAAO,IAAI,CAAC,WAAW,GAAG,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC;IACrD,CAAC;IAEM,iBAAiB;QACvB,oBAAoB,CAAC,IAAI,CAAC;YACzB,aAAa,EAAE,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,WAAW,CAAC;YAClE,KAAK,EAAE,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,WAAW,CAAC;YAC5D,YAAY,EAAE,SAAS;YAEvB,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,wBAAwB,EAAE,KAAK;YAE/B,IAAI,EAAE,CAAC,aAAwB,EAAE,EAAE;gBAClC,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;gBAChD,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;YAChC,CAAC;SACD,CAAC,CAAC;IACJ,CAAC;IAEO,MAAM,CAAC,aAAa,CAAC,CAAU,EAAE,CAAU;QAClD,IAAI,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,EAAE;YAC9B,OAAO,CAAC,CAAC;SACT;aAAM,IAAI,CAAC,CAAE,SAAS,GAAG,CAAC,CAAC,SAAS,EAAE;YACtC,OAAO,CAAC,CAAC,CAAC;SACV;QACD,OAAO,CAAC,CAAC;IACV,CAAC;IAEM,eAAe,CAAC,IAAa;QACnC,IAAI,YAAY,GAAG,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,EAAE;YAC7D,IAAI,KAAK,GAAG,SAAS,CAAC,QAAQ,CAAC,OAAO,GAAG,MAAM,CAAC,CAAC;YACjD,IAAI,KAAK,EAAE;gBACV,OAAO,KAAK,CAAC,WAAW,CAAC;aACzB;iBAAM;gBACN,OAAO,gBAAgB,CAAC;aACxB;QACF,CAAC,CAAC,CAAC;QACH,OAAO,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAChC,CAAC;IAEM,aAAa;QACnB,IAAI,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;IAC3C,CAAC;;AA1Fc,eAAC,GAAG,WAAW,CAAC;AA6FhC,MAAM,CAAC;IACN,IAAI,WAAW,GAAG,IAAI,aAAa,CAAC,sBAAsB,CAAC,CAAC;IAC5D,EAAE,CAAC,aAAa,CAAC,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC,yBAAyB,CAAC,CAAC,CAAC;AACnF,CAAC,CAAC,CAAC"}