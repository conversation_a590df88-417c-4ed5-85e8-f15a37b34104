{"version": 3, "file": "communicator.js", "sourceRoot": "", "sources": ["communicator.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;AAEb,gDAAgD;AAEhD,IAAU,iBAAiB,CAymB1B;AAzmBD,WAAU,iBAAiB;IAC1B,MAAM,CAAC,GAAG,MAAM,CAAC;IACjB,MAAM,iBAAiB,GAAG,oBAAoB,CAAC;IAI/C,SAAgB,cAAc,CAC7B,KAAwB,EACxB,aAAkC,EAAE,EACpC,iBAA2B,CAAC,GAAG,CAAC,EAChC,uBAAgC,KAAK;QAErC,IAAI,KAAK,CAAC,aAAa,KAAK,IAAI,EAAE;YACjC,OAAO,IAAI,aAAa,EAAE,CAAC;SAC3B;QAED,sEAAsE;QACtE,sEAAsE;QACtE,2EAA2E;QAC3E,OAAO,IAAI,UAAU,CACpB,QAAQ,EACR,KAAK,CAAC,aAAa,EACnB,UAAU,EACV,cAAc,EACd,oBAAoB,CACpB,CAAC;IACH,CAAC;IApBe,gCAAc,iBAoB7B,CAAA;IAED,SAAgB,eAAe,CAC9B,aAAkC,EAAE,EACpC,iBAA2B,CAAC,GAAG,CAAC,EAChC,uBAAgC,KAAK;QAErC,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,MAAM,CAAC,EAAE;YAC3D,OAAO,IAAI,aAAa,EAAE,CAAC;SAC3B;QAED,OAAO,IAAI,UAAU,CACpB,OAAO,EACP,MAAM,CAAC,MAAM,EACb,UAAU,EACV,cAAc,EACd,oBAAoB,CACpB,CAAC;IACH,CAAC;IAhBe,iCAAe,kBAgB9B,CAAA;IAiBD,MAAM,UAAU;QAiBf,YACoB,IAAY,EACZ,MAAc,EACd,aAAkC,EAAE,EAC7C,uBAAiC,CAAC,GAAG,CAAC,EACzC,wBAAiC,KAAK,EAC7C,oBAA4B,KAAK,EACd,WAAmB,MAAM;YANzB,SAAI,GAAJ,IAAI,CAAQ;YACZ,WAAM,GAAN,MAAM,CAAQ;YACd,eAAU,GAAV,UAAU,CAA0B;YAC7C,yBAAoB,GAApB,oBAAoB,CAAkB;YACzC,0BAAqB,GAArB,qBAAqB,CAAiB;YAE1B,aAAQ,GAAR,QAAQ,CAAiB;YApBrC,kBAAa,GAAY,KAAK,CAAC;YAC/B,oBAAe,GAAkB,IAAI,CAAC;YAItC,oBAAe,GAAW,CAAC,CAAC;YAC5B,uBAAkB,GAAwC,EAAE,CAAC;YAC7D,eAAU,GAAW,KAAK,CAAC;YAE3B,uBAAkB,GAAY,KAAK,CAAC;YAEpC,6BAAwB,GAA4B,IAAI,CAAC;YAWhE,IAAI,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;YAC5B,IAAI,CAAC,WAAW,GAAG,OAAO,GAAG,UAAU,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;YAEjE,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAc,CAAC;YACzC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;YAEvC,iEAAiE;YACjE,iCAAiC;YACjC,IAAI,IAAI,CAAC,eAAe,KAAK,IAAI,EAAE;gBAClC,IAAI;oBACH,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;oBACnD,IAAI,CAAC,GAAG,CAAC,2BAA2B,IAAI,CAAC,eAAe,EAAE,CAAC,CAAC;iBAC5D;gBAAC,OAAO,CAAC,EAAE;oBACX,2BAA2B;iBAC3B;aACD;YAED,IAAI,CAAC,eAAe,GAAG,CAAC,KAAY,EAAE,EAAE;gBACvC,IAAI,CAAC,CAAC,KAAK,YAAY,YAAY,CAAC,EAAE;oBACrC,IAAI,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;oBACxC,OAAO;iBACP;gBAED,IAAI,KAAK,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,EAAE;oBACjC,IAAI,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;oBAClD,OAAO;iBACP;gBAED,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE;oBACxC,IAAI,CAAC,GAAG,CAAC,2CAA2C,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;oBACrE,OAAO;iBACP;gBAED,MAAM,OAAO,GAAG,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBACzC,IAAI,OAAO,KAAK,IAAI,EAAE;oBACrB,IAAI,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;oBACtC,OAAO;iBACP;gBAED,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;YAC3C,CAAC,CAAC;YACF,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;YAChE,IAAI,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;YAElC,4DAA4D;YAC5D,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,oBAAoB,EAAE;gBAC/C,IAAI,CAAC,GAAG,CAAC,gCAAgC,MAAM,EAAE,CAAC,CAAC;gBACnD,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;gBAC9D,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,wBAAwB,EAAE,MAAM,CAAC,CAAC;aAC/D;YAED,sDAAsD;YACtD,IAAI,iBAAiB,GAAG,CAAC,EAAE;gBAC1B,UAAU,CAAC,GAAG,EAAE;oBACf,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,KAAK,SAAS,EAAE;wBACxC,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC;wBACrC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;qBACvB;gBACF,CAAC,EAAE,iBAAiB,CAAC,CAAC;aACtB;YAED,oEAAoE;YACpE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,EAAE;gBACvB,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;YACpE,CAAC,CAAC,CAAC;QACJ,CAAC;QAES,aAAa,CAAC,OAA4B,EAAE,MAAc;YACnE,IAAI,IAAI,CAAC,kBAAkB,EAAE;gBAC5B,IAAI,CAAC,GAAG,CAAC,oDAAoD,CAAC,CAAC;gBAC/D,OAAO;aACP;YACD,IAAI,CAAC,GAAG,CAAC,yBAAyB,MAAM,KAAK,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YAExE,6DAA6D;YAC7D,0DAA0D;YAC1D,4BAA4B;YAC5B,IAAI,mBAAmB,CAAC,OAAO,CAAC,EAAE;gBACjC,IAAI,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,EAAE;oBAC5C,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;iBACxC;qBAAM;oBACN,IAAI,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;oBACpD,IAAI,IAAI,CAAC,wBAAwB,EAAE;wBAClC,IAAI,CAAC,GAAG,CAAC,6BAA6B,GAAG,IAAI,CAAC,wBAAwB,CAAC,kBAAkB,CAAC,CAAC;wBAC3F,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC;wBACrC,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,KAAK,SAAS,EAAE;4BACxC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;yBACvB;qBACD;iBACD;aACD;iBAAM,IAAI,kBAAkB,CAAC,OAAO,CAAC,EAAE;gBACvC;;;;;;;;;mBASG;gBACH,IAAI,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,EAAE;oBAC3C,IAAI,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,eAAe,KAAK,MAAM,CAAC,EAAE;wBAC5D,IAAI,CAAC,GAAG,CACP,+EAA+E;8BAC7E,wBAAwB,CAC1B,CAAC;wBACF,OAAO;qBACP;oBACD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC,CAAC;oBACjD,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;iBACxC;qBAAM;oBACN,IAAI,CAAC,GAAG,CAAC,mDAAmD,CAAC,CAAC;iBAC9D;aACD;iBAAM;gBACN,gEAAgE;gBAChE,IAAI,IAAI,CAAC,aAAa,EAAE;oBACvB,IAAI,YAAY,CAAC,OAAO,CAAC,EAAE;wBAC1B,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;qBAC/B;yBAAM,IAAI,aAAa,CAAC,OAAO,CAAC,EAAE;wBAClC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;qBAChC;iBACD;qBAAM;oBACN,IAAI,CAAC,GAAG,CAAC,8DAA8D,CAAC,CAAC;iBACzE;aACD;QACF,CAAC;QAED,OAAO,CAAC,MAAc,EAAE,GAAG,IAAS;YACnC,IAAI,IAAI,CAAC,kBAAkB,EAAE;gBAC5B,OAAO,CAAC,CAAC,QAAQ,EAAE,CAAC,MAAM,CAAC,qCAAqC,CAAC,CAAC,OAAO,EAAE,CAAC;aAC5E;YAED,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,eAAe,GAAG,GAAG,GAAG,UAAU,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAEhG,MAAM,UAAU,GAAG,CAAC,CAAC,QAAQ,EAAO,CAAC;YACrC,0EAA0E;YAC1E,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,GAAG,UAAU,CAAC;YAChD,4BAA4B;YAC5B,IAAI,cAAc,GAAyC,IAAI,CAAC;YAEhE,uEAAuE;YACvE,gDAAgD;YAChD,UAAU,CAAC,MAAM,CAAC,GAAG,EAAE;gBACtB,OAAO,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;gBAC1C,IAAI,cAAc,KAAK,IAAI,EAAE;oBAC5B,YAAY,CAAC,cAAc,CAAC,CAAC;iBAC7B;YACF,CAAC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAe,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE;gBAC7D,QAAQ,EAAE,MAAM;gBAChB,MAAM,EAAE,IAAI;gBACZ,WAAW,EAAE,SAAS;aACtB,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ;iBACX,IAAI,CAAC,GAAG,EAAE;gBACV,kCAAkC;gBAClC,IAAI,IAAI,CAAC,UAAU,GAAG,CAAC,EAAE;oBACxB,cAAc,GAAG,UAAU,CAAC,GAAG,EAAE;wBAChC,cAAc,GAAG,IAAI,CAAC;wBACtB,IAAI,UAAU,CAAC,KAAK,EAAE,KAAK,SAAS,EAAE;4BACrC,UAAU,CAAC,MAAM,CAAC,wBAAwB,CAAC,CAAC;yBAC5C;oBACF,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;iBACpB;gBAED,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACpB,CAAC,CAAC;iBACD,IAAI,CAAC,GAAG,EAAE;gBACV,UAAU,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC;YACzC,CAAC,CAAC,CAAC;YAEJ,OAAO,UAAU,CAAC,OAAO,EAAE,CAAC;QAC7B,CAAC;QAES,IAAI,CAAgC,OAAU;YACvD,IAAI,IAAI,CAAC,kBAAkB,EAAE;gBAC5B,OAAO;aACP;YAED,IAAI,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC;YAClC,IAAI,MAAM,KAAK,IAAI,EAAE;gBACpB,MAAM,GAAG,GAAG,CAAC;aACb;YAED,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QAC1C,CAAC;QAED;;WAEG;QACH,UAAU;YACT,qCAAqC;YACrC,IAAI,IAAI,CAAC,kBAAkB,EAAE;gBAC5B,IAAI,CAAC,GAAG,CAAC,qEAAqE,CAAC,CAAC;gBAChF,OAAO;aACP;YAED,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;YAE/B,IAAI;gBACH,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;aACnE;YAAC,OAAO,CAAC,EAAE;gBACX,wCAAwC;aACxC;YAED,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,KAAK,SAAS,EAAE;gBACxC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;aACvB;YAED,kCAAkC;YAClC,6DAA6D;YAC7D,MAAM,eAAe,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC;YACtG,eAAe,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;gBACjC,IAAI,OAAO,CAAC,KAAK,EAAE,KAAK,SAAS,EAAE;oBAClC,OAAO,CAAC,MAAM,CAAC,+BAA+B,CAAC,CAAC;iBAChD;YACF,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;QAC3B,CAAC;QAED,YAAY,CAAC,UAAkB,EAAE,MAA+B;YAC/D,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,GAAG,MAAM,CAAC;QACtC,CAAC;QAES,sBAAsB;YAC/B,OAAO,IAAI,CAAC,aAAa,CACxB,mBAAmB,EACnB;gBACC,UAAU,EAAE,IAAI,CAAC,WAAW,EAAE;gBAC9B,oBAAoB,EAAE,UAAU,CAAC,eAAe,CAAC,EAAE,CAAC;aACpD,CACD,CAAC;QACH,CAAC;QAES,uBAAuB,CAAC,eAAiC;YAClE,OAAO,IAAI,CAAC,aAAa,CACxB,oBAAoB,EACpB;gBACC,SAAS,EAAE,IAAI;gBACf,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI;gBACpC,UAAU,EAAE,IAAI,CAAC,WAAW,EAAE;gBAC9B,oBAAoB,EAAE,eAAe,CAAC,kBAAkB;aACxD,CACD,CAAC;QACH,CAAC;QAES,WAAW;YACpB,IAAI,MAAM,CAAC;YACX,IAAI;gBACH,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC;aACvC;YAAC,OAAO,CAAC,EAAE;gBACX,aAAa;aACb;YACD,OAAO,MAAM,aAAN,MAAM,cAAN,MAAM,GAAI,IAAI,CAAC;QACvB,CAAC;QAES,eAAe,CAAC,MAAc;YACvC,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,eAAe,EAAE;gBAC/C,2BAA2B;gBAC3B,OAAO,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;aAC9D;YAED,4BAA4B;YAC5B,KAAK,MAAM,aAAa,IAAI,IAAI,CAAC,oBAAoB,EAAE;gBACtD,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,EAAE,MAAM,CAAC,EAAE;oBACpD,OAAO,IAAI,CAAC;iBACZ;aACD;YACD,OAAO,KAAK,CAAC;QACd,CAAC;QAEO,MAAM,CAAC,aAAa,CAAC,QAAuB,EAAE,KAAa;YAClE,iCAAiC;YACjC,IAAI,CAAC,QAAQ,KAAK,GAAG,CAAC,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,EAAE;gBAC9C,OAAO,IAAI,CAAC;aACZ;YACD,OAAO,QAAQ,KAAK,KAAK,CAAC;QAC3B,CAAC;QAES,wBAAwB,CAAC,OAAyB;YAC3D,OAAO,OAAO,CAAC,MAAM,KAAK,oBAAoB,IAAI,OAAO,CAAC,GAAG,KAAK,mBAAmB,CAAC;QACvF,CAAC;QAES,yBAAyB,CAAC,QAA2B;YAC9D,OAAO,CACN,CAAC,QAAQ,CAAC,MAAM,KAAK,oBAAoB,CAAC;mBACvC,CAAC,QAAQ,CAAC,GAAG,KAAK,oBAAoB,CAAC;mBACvC,CAAC,IAAI,CAAC,wBAAwB,KAAK,IAAI,CAAC;mBACxC,CAAC,QAAQ,CAAC,kBAAkB,KAAK,IAAI,CAAC,wBAAwB,CAAC,kBAAkB,CAAC,CACrF,CAAC;QACH,CAAC;QAES,iBAAiB,CAAC,OAA6C,EAAE,MAAc;YACxF,wEAAwE;YACxE,IAAI,IAAI,CAAC,eAAe,KAAK,IAAI,EAAE;gBAClC,IAAI,MAAM,IAAI,CAAC,MAAM,KAAK,GAAG,CAAC,EAAE;oBAC/B,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC;iBAC9B;qBAAM,IAAI,OAAO,CAAC,QAAQ,IAAI,CAAC,OAAO,CAAC,QAAQ,KAAK,GAAG,CAAC,EAAE;oBAC1D,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC,QAAQ,CAAC;iBACxC;aACD;YACD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC1B,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC;YACrC,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,KAAK,SAAS,EAAE;gBACxC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;aAC5B;YAED,IAAI,CAAC,GAAG,CAAC,wCAAwC,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC;QAC3E,CAAC;QAES,aAAa,CACtB,GAAM,EAAE,KAAQ;YAEhB,OAAO,UAAU,CAAC,cAAc,CAC/B;gBACC,QAAQ,EAAE,oBAAoB;gBAC9B,KAAK,EAAE,GAAG;aACV,EACD,KAAK,CACL,CAAC;QACH,CAAC;QAED;;;;;WAKG;QACK,MAAM,CAAC,cAAc,CAC5B,GAAM,EAAE,GAAM;YAEd,IAAI,MAAM,GAAwB,EAAE,CAAC;YACrC,KAAK,IAAI,GAAG,IAAI,GAAG,EAAE;gBACpB,MAAM,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;aACvB;YACD,KAAK,IAAI,GAAG,IAAI,GAAG,EAAE;gBACpB,MAAM,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;aACvB;YACD,OAAO,MAAe,CAAC;QACxB,CAAC;QAEO,MAAM,CAAC,eAAe,CAAC,MAAc;YAC5C,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;gBAClC,IAAI;oBACH,MAAM,KAAK,GAAG,MAAM,CAAC,eAAe,CAAC,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;oBAC7D,IAAI,MAAM,GAAG,EAAE,CAAC;oBAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;wBACtC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;qBACnC;oBACD,OAAO,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;iBAC5C;gBAAC,OAAO,CAAC,EAAE;oBACX,oCAAoC;iBACpC;aACD;YAED,IAAI,MAAM,GAAG,EAAE,CAAC;YAChB,OAAO,MAAM,CAAC,MAAM,GAAG,MAAM,EAAE;gBAC9B,MAAM,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;aAClD;YACD,OAAO,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;QACpC,CAAC;QAEO,gBAAgB,CAAC,OAAmB;;YAC3C,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;gBACpD,IAAI,CAAC,IAAI,CAAc,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE;oBACzD,WAAW,EAAE,OAAO,CAAC,SAAS;oBAC9B,QAAQ,EAAE,IAAI;oBACd,OAAO,EAAE,sBAAsB,GAAG,OAAO,CAAC,MAAM;iBAChD,CAAC,CAAC,CAAC;gBACJ,OAAO;aACP;YAED,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YAC/C,MAAM,IAAI,GAAG,MAAA,OAAO,CAAC,IAAI,mCAAI,EAAE,CAAC;YAChC,IAAI;gBACH,MAAM,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;gBACxC,IAAI,CAAC,IAAI,CAAc,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE;oBACzD,WAAW,EAAE,OAAO,CAAC,SAAS;oBAC9B,QAAQ,EAAE,CAAC,OAAO,MAAM,KAAK,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM;oBACzD,OAAO,EAAE,IAAI;iBACb,CAAC,CAAC,CAAC;aACJ;YAAC,OAAO,CAAC,EAAE;gBACX,IAAI,YAAoB,CAAC;gBACzB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,QAAQ,CAAC,IAAK,CAAS,CAAC,OAAO,EAAE;oBACvD,YAAY,GAAI,CAAS,CAAC,OAAO,CAAC;iBAClC;qBAAM;oBACN,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;iBACrC;gBAED,IAAI,CAAC,IAAI,CAAc,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE;oBACzD,WAAW,EAAE,OAAO,CAAC,SAAS;oBAC9B,QAAQ,EAAE,IAAI;oBACd,OAAO,EAAE,YAAY;iBACrB,CAAC,CAAC,CAAC;aACJ;QACF,CAAC;QAEO,iBAAiB,CAAC,QAAqB;YAC9C,mDAAmD;YACnD,MAAM,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAC;YACrC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,SAAS,CAAC,EAAE;gBACvD,OAAO;aACP;YAED,MAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;YACtD,IAAI,UAAU,CAAC,KAAK,EAAE,KAAK,SAAS,EAAE;gBACrC,iCAAiC;gBACjC,IAAI,OAAO,IAAI,OAAO,CAAC,IAAI,EAAE;oBAC5B,OAAO,CAAC,IAAI,CACX,wCAAwC,GAAG,SAAS;wBACpD,+CAA+C,CAC/C,CAAC;iBACF;gBACD,OAAO,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;gBAC1C,OAAO;aACP;YAED,IAAI,CAAC,OAAO,QAAQ,CAAC,KAAK,KAAK,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,KAAK,IAAI,CAAC,EAAE;gBACzE,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;aAClC;iBAAM;gBACN,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;aACpC;YAED,yDAAyD;YACzD,oCAAoC;YACpC,OAAO,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;QAC3C,CAAC;QAED,IAAI,WAAW;YACd,OAAO,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC;QACvD,CAAC;QAEO,GAAG,CAAC,OAAe;YAC1B,IAAI,IAAI,CAAC,qBAAqB,IAAI,OAAO,IAAI,OAAO,CAAC,GAAG,EAAE;gBACzD,OAAO,CAAC,GAAG,CAAC,qBAAqB,GAAG,IAAI,CAAC,IAAI,GAAG,KAAK,GAAG,OAAO,CAAC,CAAC;aACjE;QACF,CAAC;KACD;IAED,MAAM,aAAa;QAAnB;YACC,YAAO,GACN,CAAC,CAAC,QAAQ,EAAuB,CAAC,MAAM,CAAC,0CAA0C,CAAC,CAAC,OAAO,EAAE,CAAC;QAejG,CAAC;QAbA,IAAI,WAAW;YACd,OAAO,KAAK,CAAC;QACd,CAAC;QAED,YAAY,CAAC,UAAkB,EAAE,MAA+B;QAChE,CAAC;QAED,UAAU;QACV,CAAC;QAED,OAAO,CAAC,MAAc,EAAE,GAAG,IAAS;YACnC,OAAO,CAAC,CAAC,QAAQ,EAAE,CAAC,MAAM,CAAC,0CAA0C,CAAC,CAAC,OAAO,EAAE,CAAC;QAClF,CAAC;KACD;IAiCD,SAAS,YAAY,CAAC,IAAS;QAC9B,IACC,CAAC,OAAO,IAAI,KAAK,QAAQ,CAAC;eACvB,CAAC,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ,CAAC;eACjC,CAAC,OAAO,IAAI,CAAC,GAAG,KAAK,QAAQ,CAAC;eAC9B,CAAC,IAAI,CAAC,MAAM,KAAK,iBAAiB,CAAC,EACrC;YACD,OAAO,IAAI,CAAC;SACZ;QACD,OAAO,IAAI,CAAC;IACb,CAAC;IAED,SAAS,kBAAkB,CAAC,IAAyB;QACpD,OAAO,IAAI,CAAC,GAAG,KAAK,mBAAmB,CAAC;IACzC,CAAC;IAED,SAAS,mBAAmB,CAAC,IAAyB;QACrD,OAAO,CAAC,IAAI,CAAC,GAAG,KAAK,oBAAoB,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC;IACnF,CAAC;IAED,SAAS,YAAY,CAAC,IAAyB;QAC9C,OAAO,CACN,CAAC,IAAI,CAAC,GAAG,KAAK,aAAa,CAAC;eACzB,CAAC,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ,CAAC;eACjC,CAAC,OAAO,IAAI,CAAC,IAAI,KAAK,WAAW,CAAC;eAClC,CAAC,OAAO,IAAI,CAAC,SAAS,KAAK,QAAQ,CAAC;eACpC,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAC7B,CAAC;IACH,CAAC;IAED,SAAS,aAAa,CAAC,IAAyB;QAC/C,OAAO,CACN,CAAC,IAAI,CAAC,GAAG,KAAK,cAAc,CAAC;eAC1B,CAAC,OAAO,IAAI,CAAC,MAAM,KAAK,WAAW,CAAC;eACpC,CAAC,OAAO,IAAI,CAAC,SAAS,KAAK,QAAQ,CAAC,CACvC,CAAC;IACH,CAAC;AACF,CAAC,EAzmBS,iBAAiB,KAAjB,iBAAiB,QAymB1B"}