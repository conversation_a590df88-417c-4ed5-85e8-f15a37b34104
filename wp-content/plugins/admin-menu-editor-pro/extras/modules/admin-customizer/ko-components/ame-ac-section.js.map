{"version": 3, "file": "ame-ac-section.js", "sourceRoot": "", "sources": ["ame-ac-section.ts"], "names": [], "mappings": "AAAA,OAAO,EACN,uBAAuB,EACvB,qBAAqB,EAErB,oBAAoB,EACpB,MAAM,0DAA0D,CAAC;AAClE,OAAO,EAAC,eAAe,EAAC,MAAM,mDAAmD,CAAC;AAClF,IAAO,OAAO,GAAG,eAAe,CAAC,OAAO,CAAC;AACzC,IAAO,OAAO,GAAG,eAAe,CAAC,OAAO,CAAC;AACzC,IAAO,YAAY,GAAG,eAAe,CAAC,YAAY,CAAC;AAInD,MAAM,OAAO,YAAa,SAAQ,oBAA6B;IAK9D,YAAY,MAAyB,EAAE,QAAgB;QACtD,KAAK,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;QACxB,yBAAyB;QACzB,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,EAAE;YAC5B,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;SACxD;QACD,IAAI,CAAC,SAAS,GAAG,YAAY,CAAC,mBAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAElE,IAAI,CAAC,OAAO,MAAM,CAAC,WAAW,KAAK,WAAW,CAAC,IAAI,EAAE,CAAC,YAAY,CAAC,MAAM,CAAC,WAAW,CAAC,EAAE;YACvF,IAAI,CAAC,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC;SACtC;aAAM;YACN,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;SACxB;QAED,gFAAgF;QAChF,wDAAwD;QACxD,MAAM,0BAA0B,GAAG,uBAAuB,CAAC;QAC3D,4CAA4C;QAC5C,IAAI,2BAA2B,GAAY,IAAI,CAAC;QAChD,IAAI,EAAE,IAAI,EAAE,CAAC,KAAK,IAAI,EAAE,CAAC,KAAK,CAAC,YAAY,EAAE;YAC5C,2BAA2B,GAAG,EAAE,CAAC,KAAK,CAAC,YAAY,CAClD,wCAAwC,EACxC,0BAA0B,CAC1B,CAAC;SACF;QACD,MAAM,mBAAmB,GAAG,CAC3B,CAAC,OAAO,2BAA2B,KAAK,QAAQ,CAAC;YAChD,CAAC,CAAC,2BAA2B;YAC7B,CAAC,CAAC,0BAA0B,CAC7B,CAAC;QAEF,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC,YAAY,CAAC,GAAG,EAAE;YAC1C,IAAI,IAAI,CAAC,WAAW,KAAK,IAAI,EAAE;gBAC9B,OAAO,mBAAmB,CAAC;aAC3B;YACD,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;YACvC,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC3B,OAAO,mBAAmB,CAAC;aAC3B;YAED,IAAI,MAAM,GAAG,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAEnD,oEAAoE;YACpE,yFAAyF;YACzF,2FAA2F;YAC3F,uBAAuB;YACvB,MAAM,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC;YAE1B,6DAA6D;YAC7D,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;gBACtB,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;aACzC;YAED,OAAO,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;IACJ,CAAC;IAES,wBAAwB;QACjC,OAAO,OAAO,CAAC;IAChB,CAAC;IAES,0BAA0B,CAAC,KAAgC;QACpE,IAAI,KAAK,YAAY,OAAO,EAAE;YAC7B,IAAI,KAAK,CAAC,aAAa,KAAK,SAAS,EAAE;gBACtC,OAAO,uBAAuB,CAAC,WAAW,CAAC,KAAK,EAAE,wBAAwB,CAAC,CAAC;aAC5E;iBAAM;gBACN,OAAO,uBAAuB,CAAC,WAAW,CAAC,KAAK,EAAE,qBAAqB,CAAC,CAAC;aACzE;SACD;aAAM,IAAI,KAAK,YAAY,YAAY,EAAE;YACzC,OAAO,uBAAuB,CAAC,WAAW,CAAC,KAAK,EAAE,sBAAsB,CAAC,CAAC;SAC1E;aAAM,IACN,CAAC,KAAK,YAAY,OAAO,CAAC;eACvB,CAAC,CAAC,kBAAkB,EAAE,0BAA0B,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,EACjF;YACD,kEAAkE;YAClE,kFAAkF;YAClF,MAAM,YAAY,GAAG,KAAK,CAAC,kBAAkB,EAAE,CAAC;YAChD,OAAO,IAAI,CAAC,0BAA0B,CAAC,YAAY,CAAC,CAAC;SACrD;aAAM;YACN,OAAO,uBAAuB,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;SAClD;IACF,CAAC;IAED,MAAM,CAAC,mBAAmB,CAAC,OAAgB;QAC1C,OAAO,YAAY,CAAC,wBAAwB,CAAC,OAAO,EAAE,iBAAiB,CAAC,CAAC;IAC1E,CAAC;IAED,MAAM,CAAC,uBAAuB,CAAC,OAAgB;QAC9C,OAAO,YAAY,CAAC,wBAAwB,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;IACxE,CAAC;IAES,MAAM,CAAC,wBAAwB,CAAC,OAAgB,EAAE,MAAc;QACzE,IAAI,OAAO,CAAC,EAAE,EAAE;YACf,OAAO,MAAM,GAAG,OAAO,CAAC,EAAE,CAAC;SAC3B;QACD,MAAM,IAAI,GAAG,OAAO,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC;QACpE,IAAI,IAAI,KAAK,EAAE,EAAE;YAChB,OAAO,MAAM,GAAG,IAAI,CAAC;SACrB;QACD,MAAM,IAAI,KAAK,CACd,0FAA0F,CAC1F,CAAC;IACH,CAAC;IAED,OAAO;QACN,KAAK,CAAC,OAAO,EAAE,CAAC;QAChB,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;IAChC,CAAC;CACD;AAED,eAAe,qBAAqB,CAAC,YAAY,EAAE;;;;;;;;;;;;;;;;;;;CAmBlD,CAAC,CAAC"}