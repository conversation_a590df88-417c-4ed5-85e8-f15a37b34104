{"version": 3, "file": "preview-handler.js", "sourceRoot": "", "sources": ["preview-handler.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;AAEb,OAAO,EAAC,eAAe,EAAC,MAAM,gDAAgD,CAAC;AAC/E,OAAO,EAAC,sBAAsB,EAAC,MAAM,4BAA4B,CAAC;AAClE,OAAO,EAAC,iBAAiB,EAAC,MAAM,0CAA0C,CAAC;AAI3E,kFAAkF;AAClF,oDAAoD;AAEpD,IAAU,yBAAyB,CAkLlC;AAlLD,WAAU,yBAAyB;IAClC,IAAO,wBAAwB,GAAG,eAAe,CAAC,wBAAwB,CAAC;IAC3E,MAAM,CAAC,GAAG,MAAM,CAAC;IAMjB,MAAa,cAAe,SAAQ,sBAAsB,CAAC,mBAAmB;QAO7E,YAAY,UAA6B;YACxC,KAAK,CAAC,UAAU,CAAC,CAAC;YAHF,yBAAoB,GAAwB,EAAE,CAAC;YAI/D,IAAI,CAAC,aAAa,GAAG,UAAU,CAAC,aAAa,CAAC;YAE9C,IAAI,CAAC,eAAe,GAAG,IAAI,wBAAwB,CAClD,CAAC,SAAiB,EAAE,aAAkB,EAAE,EAAE;gBACzC,IAAI,IAAI,CAAC,oBAAoB,CAAC,cAAc,CAAC,SAAS,CAAC,EAAE;oBACxD,OAAO,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;iBAC5C;gBACD,2EAA2E;gBAC3E,IACC,UAAU,CAAC,QAAQ,CAAC,cAAc,CAAC,SAAS,CAAC;uBAC1C,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,OAAO,CAAC,EACxD;oBACD,OAAO,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC;iBAC5C;gBACD,OAAO,aAAa,CAAC;YACtB,CAAC,CACD,CAAC;YAEF,IAAI,CAAC,UAAU,GAAG,iBAAiB,CAAC,eAAe,CAClD;gBACC,gBAAgB,EAAE,CAAC,SAAiB,EAAE,KAAU,EAAE,EAAE;oBACnD,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;oBAE7C,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE;wBAChD,OAAO,KAAK,CAAC;qBACb;oBACD,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;oBAC7C,OAAO,IAAI,CAAC;gBACb,CAAC;gBACD,eAAe,EAAE,GAAG,EAAE;oBACrB,OAAO,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;gBAC7B,CAAC;aACD,EACD,UAAU,CAAC,kBAAkB,EAC7B,UAAU,CAAC,gBAAgB,CAC3B,CAAC;YAEF,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE;gBAClC,IAAI,OAAO,CAAC,KAAK,WAAW,EAAE;oBAC7B,IAAI,OAAO,IAAI,OAAO,CAAC,IAAI,EAAE;wBAC5B,OAAO,CAAC,IAAI,CAAC,qFAAqF,CAAC,CAAC;qBACpG;oBACD,OAAO,CAAC,2BAA2B;iBACnC;gBAED,sEAAsE;gBACtE,2EAA2E;gBAC3E,0EAA0E;gBAC1E,CAAC,CAAC,OAAO,CAAC,yBAAyB,EAAE,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAC5D,CAAC,CAAC,CAAC;YAEH,CAAC,CAAC,GAAG,EAAE;gBACN,IAAI,CAAC,uBAAuB,EAAE,CAAC;gBAE/B,yBAAyB;gBACzB,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,sBAAsB,EAAE,GAAG,EAAE,CAAC,KAAK,EAAE,EAAE;oBAC1D,OAAO,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;gBACpC,CAAC,CAAC,CAAC;gBAEH,uEAAuE;gBACvE,qBAAqB;gBACrB,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,uBAAuB,EAAE,MAAM,EAAE,UAAU,KAAK;oBACnE,KAAK,CAAC,cAAc,EAAE,CAAC;gBACxB,CAAC,CAAC,CAAC;YACJ,CAAC,CAAC,CAAC;YAEH,mEAAmE;YACnE,KAAK,MAAM,aAAa,IAAI,CAAC,UAAU,CAAC,mBAAmB,IAAI,EAAE,CAAC,EAAE;gBACnE,MAAM,eAAe,GAAG,IAAI,iBAAiB,CAAC,OAAO,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC;gBAC3F,IAAI,CAAC,eAAe,CAAC,sBAAsB,CAC1C,eAAe,CAAC,wBAAwB,EAAE,EAC1C,eAAe,CACf,CAAC;aACF;QACF,CAAC;QAED;;WAEG;QACH,uBAAuB;YACtB,MAAM,IAAI,GAAG,IAAI,CAAC;YAClB,CAAC,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC;gBACjB,MAAM,OAAO,GAAG,IAAI,CAAC;gBACrB,IAAI,CAAC,CAAC,OAAO,YAAY,iBAAiB,CAAC,EAAE;oBAC5C,OAAO;iBACP;gBACD,MAAM,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;gBAEtB,4CAA4C;gBAC5C,IAAI,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE;oBACjC,OAAO;iBACP;gBAED,sCAAsC;gBACtC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,EAAE;oBACrC,KAAK,CAAC,QAAQ,CAAC,wBAAwB,CAAC,CAAC;oBACzC,OAAO;iBACP;gBAED,qCAAqC;gBACrC,MAAM,MAAM,GAAG,IAAI,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;gBACnD,MAAM,CAAC,GAAG,CAAC,gBAAgB,EAAE,GAAG,CAAC,CAAC;gBAClC,MAAM,CAAC,GAAG,CAAC,kBAAkB,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;gBACnD,OAAO,CAAC,MAAM,GAAG,GAAG,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;YAC1C,CAAC,CAAC,CAAC;QACJ,CAAC;QAED,iBAAiB,CAAC,OAA0B;YAC3C,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QACvC,CAAC;QAED,gBAAgB,CAAC,KAAa;YAC7B,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAChC,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;gBAChC,OAAO,KAAK,CAAC;aACb;YACD,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC;QACvC,CAAC;QAED,eAAe,CAAC,KAAwB;YACvC,MAAM,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YAE3C,6BAA6B;YAC7B,IAAI,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE;gBACjC,OAAO;aACP;YAED,+DAA+D;YAC/D,MAAM,aAAa,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAsB,CAAC;YACxD,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,EAAE;gBAC3C,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,OAAO;aACP;YAED,+DAA+D;YAC/D,IAAI,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE;gBAChC,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,eAAe,EAAE,aAAa,CAAC,IAAI,CAAC,CAAC;aAC7D;QACF,CAAC;QAED,4DAA4D;QAC5D,iBAAiB,CAAC,SAAiB,EAAE,QAAiC;YACrE,IAAI,CAAC,eAAe,CAAC,uBAAuB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;QACnE,CAAC;QAED,iEAAiE;QACjE,sBAAsB,CAAC,UAAoB,EAAE,OAAuC;YACnF,IAAI,CAAC,eAAe,CAAC,sBAAsB,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAClE,CAAC;QAED,qCAAqC;QACrC,iBAAiB,CAAC,UAAkB,EAAE,OAA8B;YACnE,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QACnD,CAAC;KACD;IApKY,wCAAc,iBAoK1B,CAAA;IAED,MAAM,cAAc,GAAG,IAAI,cAAc,CAAC,kBAAkB,CAAC,CAAC;IAC9D,MAAM,CAAC,0BAA0B,CAAC,GAAG,cAAc,CAAC;IAEpD,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,gCAAgC,EAAE,CAAC,cAAc,CAAC,CAAC,CAAC;AACvE,CAAC,EAlLS,yBAAyB,KAAzB,yBAAyB,QAkLlC"}