.ame-input-group {
  display: flex;
  flex-wrap: wrap;
}
.ame-input-group .ame-input-group-secondary, .ame-input-group > :not(:first-child) {
  margin-left: -1px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.ame-input-group > :not(:last-child) {
  margin-right: 0;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

#ame-ac-admin-customizer {
  display: flex;
  flex-direction: row;
  align-items: stretch;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  max-height: 100%;
}

#ame-ac-sidebar {
  flex-basis: 18%;
  min-width: 320px;
  max-width: 600px;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  background: #f4f4f5;
  border-right: 1px solid #dcdcde;
}

#ame-ac-container-collection {
  position: relative;
  width: 100%;
}

#ame-ac-preview-container {
  flex-basis: 82%;
  flex-grow: 1;
  background: #ddd;
  position: relative;
}

#ame-ac-primary-actions {
  min-height: 30px;
  border-bottom: 1px solid #dcdcde;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding-right: 15px;
}

#ame-ac-sidebar-content {
  flex-grow: 1;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
}

#ame-ac-sidebar-blocker-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.4);
  display: none;
}

#ame-ac-exit-admin-customizer {
  box-sizing: border-box;
  display: block;
  height: 45px;
  width: 48px;
  margin-right: auto;
  border-right: 1px solid #dcdcde;
  border-top: 4px solid transparent;
  color: #3c434a;
  text-decoration: none;
  position: relative;
  text-align: center;
}
#ame-ac-exit-admin-customizer:before {
  font: normal 22px/45px dashicons;
  content: "\f335";
  position: relative;
  top: -3px;
}
#ame-ac-exit-admin-customizer:hover {
  background: #fff;
  color: #2271b1;
  border-top-color: #2271b1;
  cursor: pointer;
}

#ame-ac-primary-actions .spinner {
  margin-top: 0;
  float: none;
}

#ame-ac-save-button-wrapper #ame-ac-apply-changes {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
#ame-ac-save-button-wrapper #ame-ac-extra-actions-trigger {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  min-width: 30px;
  padding-left: 0;
  padding-right: 0;
}

#ame-ac-sidebar-info .notice {
  margin: 0;
  padding-top: 9px;
  padding-left: 12px;
  padding-bottom: 9px;
}
#ame-ac-sidebar-info #ame-ac-global-notification-area {
  border-bottom: 1px solid #dcdcde;
  overflow-x: hidden;
}
#ame-ac-sidebar-info #ame-ac-global-notification-area:empty {
  display: none;
}

.ame-ac-control-label {
  display: block;
  box-sizing: border-box;
  font-size: 14px;
  line-height: 1.75;
  font-weight: 600;
  margin-bottom: 4px;
}

.ame-ac-control {
  display: block;
  box-sizing: border-box;
  width: 100%;
  margin-bottom: 12px;
  padding-left: 12px;
  padding-right: 12px;
}
.ame-ac-control .description {
  color: #50575e;
  display: block;
  font-style: italic;
  line-height: 1.5;
  margin-top: 0;
  margin-bottom: 5px;
}
.ame-ac-control p.description {
  margin-top: 0.5em;
}

.ame-ac-control-group .ame-ac-control {
  padding-left: 0;
  padding-right: 0;
}

.ame-text-input-control {
  width: 100%;
}

.ame-ac-separator {
  margin: 15px 0 0 0;
  border: none;
}

#ame-ac-sidebar-content .ame-ac-validation-errors > ul, #ame-ac-sidebar-content ame-ac-validation-errors > ul {
  margin: 0;
  padding: 0;
}
#ame-ac-sidebar-content .ame-ac-validation-errors li, #ame-ac-sidebar-content ame-ac-validation-errors li {
  list-style: none;
}
#ame-ac-sidebar-content .ame-ac-validation-error {
  margin: 0 0 6px 0;
  padding: 9px 12px;
}

.ame-ac-control .ame-ac-has-error {
  outline: 2px solid #d63638;
}

.ame-ac-section {
  list-style: none;
  box-sizing: border-box;
  margin: 0;
  width: 100%;
}

.ame-ac-section-link {
  display: block;
  box-sizing: border-box;
  width: 100%;
  margin: 0;
  cursor: pointer;
}
.ame-ac-section-link .ame-ac-section-title {
  display: block;
  position: relative;
  padding: 10px 10px 11px 14px;
  margin: 0;
  color: #50575e;
  background-color: white;
  border-bottom: 1px solid #dcdcde;
  border-left: 4px solid #fff;
  line-height: 1.55;
  font-size: 14px;
  transition: 0.14s color ease-in-out, 0.14s background-color ease-in-out, 0.14s border-color ease-in-out;
}
.ame-ac-section-link .ame-ac-section-title:after {
  font: normal 20px/1 dashicons;
  display: block;
  content: "\f345";
  color: #a7aaad;
  position: absolute;
  right: 10px;
  top: calc(50% - 10px);
}
.ame-ac-section-link .ame-ac-section-title:hover {
  color: #2271b1;
  background: #f6f7f7;
  border-left-color: #2271b1;
}
.ame-ac-section-link .ame-ac-section-title:hover:after {
  color: #2271b1;
}

.ame-ac-section .ame-ac-section-meta + .ame-ac-section-link,
li:not(.ame-ac-section-link) + .ame-ac-section-link {
  border-top: 1px solid #dcdcde;
}

.ame-ac-section-meta {
  display: block;
  box-sizing: border-box;
  width: 100%;
  background: white;
}

.ame-ac-section-header {
  display: flex;
  border-bottom: 1px solid #dcdcde;
  margin-bottom: 15px;
  color: #50575e;
}
.ame-ac-section-header .ame-ac-section-title {
  flex-grow: 1;
  padding-left: 14px;
  padding-right: 10px;
  font-size: 20px;
  font-weight: 200;
  line-height: 27px;
  margin-top: 1.15em;
  margin-bottom: 1.2em;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.ame-ac-section-header .ame-ac-section-breadcrumbs {
  display: block;
  font-size: 13px;
  line-height: 26px;
  font-weight: 400;
  color: #50575e;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.ame-ac-section-header .ame-ac-section-title.ame-ac-has-breadcrumbs {
  margin-top: 0.45em;
  margin-bottom: 0.55em;
}

.ame-ac-section-back-button {
  display: block;
  width: 48px;
  flex-shrink: 0;
  margin: 0;
  padding: 0 3px 0 0;
  background: #fff;
  color: #50575e;
  border: none;
  border-right: 1px solid #dcdcde;
  border-left: 4px solid #fff;
  box-shadow: none;
  cursor: pointer;
  text-align: center;
}
.ame-ac-section-back-button:before {
  display: block;
  font: normal 20px/1 dashicons;
  content: "\f341";
  margin-right: 1px;
}
.ame-ac-section-back-button:hover {
  color: #2271b1;
  border-left-color: #2271b1;
  background: #f6f7f7;
}

#ame-ac-section-structure-root .ame-ac-section-back-button {
  display: none;
}

.ame-ac-section {
  transform: translateX(100%);
  visibility: hidden;
  height: 0;
  overflow: hidden;
  position: absolute;
  top: 0;
  left: 0;
  transition-property: transform, visibility;
  transition-duration: 0.182s;
  transition-timing-function: cubic-bezier(0.65, 0.05, 0.36, 1);
}
.ame-ac-section.ame-ac-transitioning {
  visibility: visible;
  height: auto;
  overflow: auto;
}

.ame-ac-current-section {
  transform: none;
  visibility: visible;
  height: auto;
  overflow: auto;
}

.ame-ac-previous-section {
  transform: translateX(-100%);
}

@media (prefers-reduced-motion: reduce) {
  .ame-ac-section {
    transition: none;
  }
}
.ame-ac-content-section {
  background: #fff;
  border-top: 1px solid #dcdcde;
  border-bottom: 1px solid #dcdcde;
  padding-top: 4px;
  padding-bottom: 5px;
}

.ame-ac-content-section-title {
  margin: 0;
  font-size: 15px;
}

.ame-ac-control + .ame-ac-content-section {
  margin-top: 18px;
}

.ame-ac-section-link + .ame-ac-content-section {
  margin-top: 0.5em;
}

.ame-ac-content-section-2 {
  border-top-style: none;
  background-color: transparent;
  margin-left: 8px;
  padding-left: 4px;
  width: calc(100% - 8px);
  padding-top: 0;
  padding-bottom: 0;
}
#ame-ac-preview {
  display: block;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  border: none;
}

#ame-ac-preview-refresh-indicator {
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  z-index: 2;
  background: rgba(255, 255, 255, 0.5);
  visibility: hidden;
  opacity: 0;
  transition: opacity 250ms ease-in, visibility 0ms ease-in 250ms;
}
#ame-ac-preview-refresh-indicator.ame-ac-show-indicator {
  visibility: visible;
  opacity: 1;
  transition-delay: 0ms;
}
#ame-ac-preview-refresh-indicator #ame-ac-refresh-spinner {
  display: none;
  /*box-sizing: border-box;
  width: 10em;
  height: 10em;
  border-radius: 50%;

  $backColor: rgba(100, 100, 100, 0.4);
  border: 1.1em solid $backColor;
  border-left: 1.1em solid #ffffff;

  animation: ame-ac-basic-spin 1.1s infinite linear;*/
}

@keyframes ame-ac-basic-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.ui-menu {
  background-color: #ffffff;
  border-radius: 5px;
  border: 1px solid #444a4a;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  z-index: 9999;
}
.ui-menu .ui-menu-item {
  color: #32373c;
  padding: 8px 16px;
  margin: 0;
  cursor: pointer;
  font-size: 14px;
  line-height: 1.4;
  white-space: nowrap;
}
.ui-menu .ui-menu-item:hover, .ui-menu .ui-menu-item.ui-state-focus {
  background-color: #0073aa;
  color: #ffffff;
  outline: none;
}
.ui-menu .ui-menu-item .dashicons {
  transition: none;
}
.ui-menu .ui-menu-divider {
  border-top: 1px solid #e5e5e5;
  margin: 5px 0;
}
.ui-menu .ui-menu-item.ui-state-disabled {
  color: #8c8f94;
  cursor: default;
}
.ui-menu .ui-menu-item.ui-state-disabled:hover, .ui-menu .ui-menu-item.ui-state-disabled.ui-state-focus {
  background-color: transparent;
}
.ui-menu .ui-menu-icon {
  float: right;
  margin-top: 2px;
}
.ui-menu .ui-menu .ui-menu {
  position: absolute;
  top: 0;
  left: 100%;
  border: none;
  margin-top: -2px;
  margin-left: -1px;
}
.ui-menu .ui-menu-item-delete, .ui-menu .ame-ac-menu-item-delete {
  color: #dc3232;
}
.ui-menu .ui-menu-item-delete:hover, .ui-menu .ui-menu-item-delete.ui-state-focus, .ui-menu .ame-ac-menu-item-delete:hover, .ui-menu .ame-ac-menu-item-delete.ui-state-focus {
  background-color: #e77373;
  color: #ffffff;
}
.ui-menu .ui-menu-item-delete.ui-state-disabled:hover, .ui-menu .ui-menu-item-delete.ui-state-disabled.ui-state-focus, .ui-menu .ame-ac-menu-item-delete.ui-state-disabled:hover, .ui-menu .ame-ac-menu-item-delete.ui-state-disabled.ui-state-focus {
  color: #8c8f94;
  background-color: transparent;
}

#ame-ac-extra-actions-menu {
  position: absolute;
}

.ui-dialog {
  background-color: white;
  border: 1px solid #c0c0c0;
  border-radius: 3px;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.25);
}
.ui-dialog .ui-dialog-titlebar {
  background-color: #fcfcfc;
  border-radius: 3px 3px 0 0;
  border-bottom: 1px solid #dfdfdf;
  padding: 0 36px 0 8px;
}
.ui-dialog .ui-dialog-title {
  font-size: 18px;
  font-weight: 600;
  line-height: 2;
  margin: 0;
}
.ui-dialog .ui-dialog-titlebar-close {
  background-color: transparent;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 0;
  margin: 0;
  position: absolute;
  top: 0;
  right: 0;
  width: 36px;
  height: 36px;
  text-align: center;
}
.ui-dialog .ui-dialog-titlebar-close:hover {
  color: #000;
}
.ui-dialog .ui-dialog-titlebar-close:before {
  font: normal 20px/36px "dashicons";
  content: "\f158";
  vertical-align: middle;
}
.ui-dialog .ui-dialog-content {
  font-size: 14px;
  line-height: 1.4285714286;
  padding: 8px 8px;
}
.ui-dialog .ui-dialog-buttonpane {
  background-color: #fcfcfc;
  border-top: 1px solid #dcdcde;
  padding: 8px;
  text-align: right;
}
.ui-dialog .ui-dialog-buttonpane .button-primary {
  float: left;
}
.ui-widget-overlay {
  background-color: rgba(0, 0, 0, 0.7);
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
}

.ui-front {
  z-index: 10000;
}

.ame-ac-dialog-label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 3px;
}

.ame-ac-dialog-row {
  margin-bottom: 0.7142857143em;
}
.ame-ac-dialog-row input, .ame-ac-dialog-row select, .ame-ac-dialog-row textarea {
  width: 100%;
}

.ame-ac-more-toggle {
  text-decoration: none;
  margin-bottom: 8px;
  line-height: 20px;
}
.ame-ac-more-toggle:before {
  font: normal 20px/1 "dashicons";
  content: "\f140";
  vertical-align: top;
  margin-left: -3px;
}
.ame-ac-more-toggle.ame-ac-more-toggle-active:before {
  content: "\f142";
}

.ame-ac-dialog-help {
  margin: -8px -8px 8px;
  padding: 3px 8px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #dcdcdc;
}
.ame-ac-dialog-help p:first-of-type {
  margin-top: 0;
}
.ame-ac-dialog-help ul {
  margin-top: 0.5em;
}
.ame-ac-dialog-help ul li {
  list-style: disc inside;
  margin-left: 0.5em;
}
.ame-ac-dialog-help .ame-ac-more-toggle.ame-ac-more-toggle-inactive:before {
  content: "\f348";
}

.ame-ac-dialog select:invalid, .ame-ac-dialog select.ame-has-validation-errors, .ame-ac-dialog input:invalid, .ame-ac-dialog input.ame-has-validation-errors {
  border-color: #d63638;
}
.ame-ac-dialog select:invalid:focus, .ame-ac-dialog select.ame-has-validation-errors:focus, .ame-ac-dialog input:invalid:focus, .ame-ac-dialog input.ame-has-validation-errors:focus {
  box-shadow: 0 0 0 1px #d63638;
}

#ame-ac-download-theme-dialog {
  position: relative;
}

.ui-dialog-buttonset .ame-dialog-confirm-button {
  min-width: 7em;
}

#ame-ac-download-progress-overlay {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 2;
  cursor: wait;
}

#ame-ac-latest-import-report table th {
  text-align: left;
}
#ame-ac-latest-import-report table td {
  text-align: right;
  padding-left: 1em;
}

.ame-ac-visually-hidden {
  clip: rect(0 0 0 0);
  clip-path: inset(50%);
  height: 1px;
  overflow: hidden;
  position: absolute;
  white-space: nowrap;
  width: 1px;
}

.ame-ac-general-progress-spinner {
  width: 50px;
  height: 50px;
  background-color: rgba(255, 255, 255, 0.8);
  border: 7px solid #f3f3f3;
  border-top: 7px solid #3498db;
  border-radius: 50%;
  animation: ac-progress-spin 2s linear infinite;
}

@keyframes ac-progress-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.ame-ac-spinner-container {
  width: 100%;
  height: 100%;
  /* Flexbox properties for centering the spinner */
  display: flex;
  justify-content: center;
  align-items: center;
}

/*# sourceMappingURL=admin-customizer.css.map */
