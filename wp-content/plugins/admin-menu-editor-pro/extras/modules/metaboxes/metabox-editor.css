#ame-meta-box-editor .ame-mb-check-column {
  padding: 11px 0 0 3px;
  width: 1.8em;
  vertical-align: top;
}
#ame-meta-box-editor .ame-meta-box-list {
  width: auto;
}
#ame-meta-box-editor .ame-meta-box-list td label {
  vertical-align: top;
}
#ame-meta-box-editor .ame-mb-default-visibility-column {
  text-align: center;
}

#ws_actor_selector_container {
  margin-right: 130px;
}

#ame-mb-screen-list {
  display: inline-block;
  background: white;
  border: 1px solid #ccd0d4;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
  padding: 10px 8px;
  padding-top: 0;
}
#ame-mb-screen-list .ame-mb-delete-section {
  text-decoration: none;
}
#ame-mb-screen-list .ame-mb-delete-section:not(:hover) {
  color: #888;
}
#ame-mb-screen-list .ame-mb-delete-section:hover, #ame-mb-screen-list .ame-mb-delete-section:active {
  color: #b32d2e;
}

#ame-mb-action-container {
  display: inline-block;
  vertical-align: top;
}

#ame-mb-main-actions {
  background: white;
  border: 1px solid #ccd0d4;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
  padding: 10px 8px;
  box-sizing: border-box;
  width: 150px;
  margin-left: 10px;
  margin-top: 0;
}
#ame-mb-main-actions input.button-primary {
  margin-bottom: 20px;
}
#ame-mb-main-actions input.button {
  width: 100%;
}

/* Tooltips */
#ame-meta-box-editor .ame-meta-box-list .ws_tooltip_trigger {
  visibility: hidden;
  color: silver;
}
#ame-meta-box-editor .ame-meta-box-list tr:hover .ws_tooltip_trigger {
  visibility: visible;
}

/*# sourceMappingURL=metabox-editor.css.map */
