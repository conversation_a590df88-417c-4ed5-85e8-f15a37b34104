{"version": 3, "file": "ame-number-input.js", "sourceRoot": "", "sources": ["ame-number-input.ts"], "names": [], "mappings": "AAAA,2EAA2E;AAE3E,OAAO,EAAC,4BAA4B,EAAE,kBAAkB,EAAC,MAAM,oBAAoB,CAAC;AACpF,OAAO,EAAC,eAAe,EAAC,MAAM,8BAA8B,CAAC;AAC7D,IAAO,OAAO,GAAG,eAAe,CAAC,OAAO,CAAC;AAGzC,MAAM,OAAO,cAAe,SAAQ,kBAAkB;IAkBrD,YAAY,MAAW,EAAE,QAAgB;QACxC,KAAK,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;QARN,iBAAY,GAAgC,IAAI,CAAC;QAG1D,WAAM,GAA0B,IAAI,CAAC;QAO9C,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,EAAC,iBAAiB,EAAE,IAAI,EAAC,CAAC,CAAC;QAEtE,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,IAAI,EAAE,CAAC;QACtC,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC,eAAe,IAAI,KAAK,CAAC;QACvD,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,aAAa,IAAI,EAAE,CAAC;QAEhD,IAAI,IAAI,CAAC,eAAe,IAAI,MAAM,CAAC,mBAAmB,EAAE;YACvD,IAAI,CAAC,mBAAmB,GAAG;gBAC1B,OAAO,EAAE,MAAM,CAAC,mBAAmB,CAAC,OAAO,IAAI,EAAE;gBACjD,WAAW,EAAE,MAAM,CAAC,mBAAmB,CAAC,WAAW,IAAI,MAAM;gBAC7D,YAAY,EAAE,MAAM,CAAC,mBAAmB,CAAC,YAAY,IAAI,OAAO;aAChE,CAAC;SACF;aAAM;YACN,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;SAChC;QAED,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC;QAC9B,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC;QAC9B,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC;QAEhC,IAAI,MAAM,CAAC,YAAY,EAAE;YACxB,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC,YAAY,CAAC;SACxC;QACD,IAAI,CAAC,iBAAiB,GAAG,CAAC,OAAO,MAAM,CAAC,iBAAiB,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC;QAE1G,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,6BAA6B,EAAE,kBAAkB,CAAC,CAAC;IAC9E,CAAC;IAED,IAAI,OAAO;QACV,MAAM,OAAO,GAAG,CAAC,0BAA0B,CAAC,CAAC;QAC7C,IAAI,IAAI,CAAC,YAAY,KAAK,IAAI,EAAE;YAC/B,OAAO,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;SAChD;QACD,OAAO,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC;QAC/B,OAAO,OAAO,CAAC;IAChB,CAAC;IAED,IAAI,YAAY;QACf,MAAM,OAAO,GAAG,CAAC,6BAA6B,EAAE,kBAAkB,CAAC,CAAC;QACpE,OAAO,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,YAAY,CAAC,CAAC;QACpC,OAAO,OAAO,CAAC;IAChB,CAAC;IAES,4BAA4B;QACrC,IAAI,UAAU,GAAG,KAAK,CAAC,4BAA4B,EAAE,CAAC;QAEtD,IAAI,IAAI,CAAC,GAAG,KAAK,IAAI,EAAE;YACtB,UAAU,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;SACxC;QACD,IAAI,IAAI,CAAC,GAAG,KAAK,IAAI,EAAE;YACtB,UAAU,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;SACxC;QACD,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,EAAE;YACvB,UAAU,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;SAC1C;QAED,IAAI,IAAI,CAAC,aAAa,EAAE;YACvB,UAAU,CAAC,sBAAsB,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;SACxD;QAED,OAAO,UAAU,CAAC;IACnB,CAAC;IAED,yFAAyF;IAClF,eAAe,CAAC,KAAU,EAAE,KAAY;QAC9C,IAAI,CAAC,IAAI,CAAC,YAAY,KAAK,IAAI,CAAC,IAAI,CAAC,OAAO,cAAc,KAAK,WAAW,CAAC,EAAE;YAC5E,OAAO;SACP;QAED,qBAAqB;QACrB,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;YAClB,OAAO;SACP;QACD,MAAM,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QACpC,IAAI,MAAM,CAAC,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE;YAClD,OAAO;SACP;QACD,MAAM,UAAU,GAAG,MAAM,CAAC,OAAO,CAAC,kCAAkC,CAAC,CAAC;QACtE,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YAC1B,OAAO;SACP;QAED,wDAAwD;QACxD,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YACjB,IAAI,aAAa,GAA0B,EAAE,CAAC;YAC9C,IAAI,IAAI,CAAC,iBAAiB,EAAE;gBAC3B,aAAa,CAAC,qBAAqB,GAAG,IAAI,CAAC,iBAAiB,CAAC;aAC7D;YAED,4EAA4E;YAC5E,6CAA6C;YAC7C,MAAM,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;YAChD,IAAI,CAAC,MAAM,GAAG,cAAc,CAAC,YAAY,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;SACrE;QAED,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;IAClC,CAAC;IAES,wBAAwB;QACjC,OAAO,OAAO,CAAC;IAChB,CAAC;CACD;AAED,eAAe,4BAA4B,CAAC,cAAc,EAAE;;;;;;;;;;;;;;;;;;;CAmB3D,CAAC,CAAC"}