{"version": 3, "file": "lazy-popup-slider-adapter.js", "sourceRoot": "", "sources": ["lazy-popup-slider-adapter.ts"], "names": [], "mappings": "AAAA,wEAAwE;AAExE;;;GAGG;AACH,MAAM,OAAO,sBAAsB;IAQlC,YACkB,YAAuC,EACvC,oBAA4B,kCAAkC,EAE9D,gBAAwB,OAAO,EAC/B,gBAAuC,EAAE;QAJzC,iBAAY,GAAZ,YAAY,CAA2B;QACvC,sBAAiB,GAAjB,iBAAiB,CAA6C;QAE9D,kBAAa,GAAb,aAAa,CAAkB;QAC/B,kBAAa,GAAb,aAAa,CAA4B;QAZnD,WAAM,GAAwB,IAAI,CAAC;QAc1C,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE;YAC5C,aAAa,CAAC,MAAM,GAAG,YAAY,CAAC;SACpC;QAED,IAAI,CAAC,kBAAkB,GAAI,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;YAC3C,4DAA4D;YAC5D,sDAAsD;YACtD,IAAI,KAAK,CAAC,MAAM,KAAK,IAAI,EAAE;gBAC1B,OAAO;aACP;YACD,MAAM,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YACpC,IAAI,MAAM,CAAC,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE;gBAC7D,OAAO;aACP;YAED,qDAAqD;YACrD,IAAI,IAAI,CAAC,MAAM,EAAE;gBAChB,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;gBACjC,OAAO;aACP;YAED,qBAAqB;YACrB,IAAI,OAAO,cAAc,KAAK,WAAW,EAAE;gBAC1C,OAAO;aACP;YACD,MAAM,UAAU,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAC1D,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC1B,OAAO;aACP;YAED,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;YAC5B,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,EAAE;gBACzB,uEAAuE;gBACtE,IAAI,CAAC,MAAyB,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;aACrD;QACF,CAAC,CAAA;IACF,CAAC;IAED;;OAEG;IACK,UAAU,CAAC,UAAkB;QACpC,IAAI,IAAI,CAAC,MAAM,EAAE;YAChB,OAAO;SACP;QAED,4EAA4E;QAC5E,yEAAyE;QACzE,IAAI,CAAC,MAAM,GAAG,cAAc,CAAC,YAAY,CAAC,UAAU,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;IAC3E,CAAC;CACD"}