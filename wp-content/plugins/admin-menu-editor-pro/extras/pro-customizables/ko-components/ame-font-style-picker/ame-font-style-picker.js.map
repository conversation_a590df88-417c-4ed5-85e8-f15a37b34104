{"version": 3, "file": "ame-font-style-picker.js", "sourceRoot": "", "sources": ["ame-font-style-picker.ts"], "names": [], "mappings": "AAAA,OAAO,EAAC,4BAA4B,EAAE,mBAAmB,EAAC,MAAM,oBAAoB,CAAC;AAErF,kFAAkF;AAClF,8EAA8E;AAC9E,+BAA+B;AAE/B;;;;;;GAMG;AACH,MAAM,gBAAgB,GAAG;IACxB,YAAY,EAAE;QACb;YACC,OAAO,EAAE,IAAI;YACb,MAAM,EAAE,oBAAoB;YAC5B,OAAO,EAAE,SAAS;SAClB;QACD;YACC,OAAO,EAAE,QAAQ;YACjB,MAAM,EAAE,QAAQ;YAChB,OAAO,EAAE,2DAA2D;SACpE;KACD;IACD,gBAAgB,EAAE;QACjB;YACC,OAAO,EAAE,IAAI;YACb,MAAM,EAAE,qBAAqB;YAC7B,OAAO,EAAE,SAAS;SAClB;QACD;YACC,OAAO,EAAE,WAAW;YACpB,MAAM,EAAE,WAAW;YACnB,OAAO,EAAE;gBACR,gBAAgB,EAAE,WAAW;aAC7B;SACD;QACD;YACC,OAAO,EAAE,WAAW;YACpB,MAAM,EAAE,WAAW;YACnB,OAAO,EAAE;gBACR,gBAAgB,EAAE,WAAW;aAC7B;SACD;QACD;YACC,OAAO,EAAE,YAAY;YACrB,MAAM,EAAE,sBAAsB;YAC9B,OAAO,EAAE;gBACR,gBAAgB,EAAE,YAAY;aAC9B;SACD;KACD;IACD,cAAc,EAAE;QACf;YACC,OAAO,EAAE,IAAI;YACb,MAAM,EAAE,sBAAsB;YAC9B,OAAO,EAAE,SAAS;SAClB;QACD;YACC,OAAO,EAAE,YAAY;YACrB,MAAM,EAAE,YAAY;YACpB,OAAO,EAAE;gBACR,cAAc,EAAE,YAAY;aAC5B;SACD;KACD;IACD,iBAAiB,EAAE;QAClB;YACC,OAAO,EAAE,IAAI;YACb,MAAM,EAAE,yBAAyB;YACjC,OAAO,EAAE,SAAS;SAClB;QACD;YACC,OAAO,EAAE,WAAW;YACpB,MAAM,EAAE,WAAW;YACnB,OAAO,EAAE,8DAA8D;SACvE;QACD;YACC,OAAO,EAAE,cAAc;YACvB,MAAM,EAAE,eAAe;YACvB,OAAO,EAAE,kEAAkE;SAC3E;KACD;CACD,CAAC;AAEF,yDAAyD;AACzD,SAAS,cAAc,CAAC,MAA8B;IACrD,IAAI,WAAW,GAAG,EAAE,CAAC;IACrB,KAAK,MAAM,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;QACvD,WAAW,IAAI,GAAG,QAAQ,KAAK,KAAK,GAAG,CAAC;KACxC;IACD,OAAO,wCAAwC,WAAW,aAAa,CAAC;AACzE,CAAC;AASD,IAAI,gBAAgB,GAA+B,EAAE,CAAC;AACtD,KAAK,MAAM,CAAC,QAAQ,EAAE,OAAO,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,EAAE;IACnE,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;QAC1B,gEAAgE;QAChE,kEAAkE;QAClE,IAAI,MAAM,CAAC,KAAK,KAAK,IAAI,EAAE;YAC1B,OAAO;SACP;QAED,IAAI,WAAmB,CAAC;QACxB,IAAI,OAAO,MAAM,CAAC,KAAK,KAAK,QAAQ,EAAE;YACrC,WAAW,GAAG,cAAc,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;SAC3C;aAAM;YACN,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC;SAC3B;QAED,gBAAgB,CAAC,IAAI,CAAC;YACrB,OAAO,EAAE,MAAM,CAAC,KAAK;YACrB,MAAM,EAAE,MAAM,CAAC,IAAI,IAAI,EAAE;YACzB,UAAU,EAAE,QAAQ;YACpB,OAAO,EAAE,WAAW;SACpB,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;CACH;AAED,MAAM,kBAAmB,SAAQ,mBAAmB;IAGnD,YAAY,MAAW,EAAE,QAAgB;QACxC,KAAK,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;QAHT,YAAO,GAAG,gBAAgB,CAAC;IAI3C,CAAC;IAED,IAAI,OAAO;QACV,OAAO,CAAC,wBAAwB,EAAE,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC;IACrD,CAAC;IAED,qEAAqE;IACrE,gBAAgB,CAAC,QAAgB,EAAE,KAAoB;QACtD,IAAI,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE;YAC3C,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,KAAK,EAAE,KAAK,KAAK,CAAC,CAAC;SACnD;QACD,OAAO,KAAK,CAAC;IACd,CAAC;IAED,8DAA8D;IAC9D,YAAY,CAAC,QAAgB,EAAE,KAAoB;QAClD,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE;YAC5C,OAAO;SACP;QAED,MAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAC9C,IAAI,aAAa,CAAC,KAAK,EAAE,KAAK,KAAK,EAAE;YACpC,iFAAiF;YACjF,aAAa,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;SAC9B;aAAM;YACN,+BAA+B;YAC/B,aAAa,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;SAC/B;IACF,CAAC;CACD;AAED,8EAA8E;AAC9E,6EAA6E;AAC7E,4BAA4B;AAC5B,eAAe,4BAA4B,CAAC,kBAAkB,EAAE;;;;;;;;;;;;;CAa/D,CAAC,CAAC"}