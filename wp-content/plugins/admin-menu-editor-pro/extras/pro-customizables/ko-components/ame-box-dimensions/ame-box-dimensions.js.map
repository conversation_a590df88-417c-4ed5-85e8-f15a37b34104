{"version": 3, "file": "ame-box-dimensions.js", "sourceRoot": "", "sources": ["ame-box-dimensions.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;AACb,OAAO,EAAC,4BAA4B,EAAqB,mBAAmB,EAAC,MAAM,oBAAoB,CAAC;AAGxG,OAAO,EAAC,sBAAsB,EAAC,MAAM,iCAAiC,CAAC;AAGvE,MAAM,gBAAgB,GAAG;IACxB,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO;IAChC,SAAS,EAAE,UAAU,EAAE,YAAY,EAAE,aAAa;CACzC,CAAC;AAGX,SAAS,cAAc,CAAC,GAAW;IAClC,OAAO,gBAAgB,CAAC,QAAQ,CAAC,GAAmB,CAAC,CAAC;AACvD,CAAC;AAED,MAAM,wBAAwB,GAAkC;IAC/D,CAAC,KAAK,EAAE,KAAK,CAAC;IACd,CAAC,QAAQ,EAAE,QAAQ,CAAC;IACpB,CAAC,MAAM,EAAE,MAAM,CAAC;IAChB,CAAC,OAAO,EAAE,OAAO,CAAC;CAClB,CAAC;AAEF,MAAM,cAAc,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAU,CAAC;AAEnE,MAAM,qBAAqB,GAAsD;IAChF,UAAU,EAAE,CAAC,KAAK,EAAE,QAAQ,CAAC;IAC7B,YAAY,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC;CAC/B,CAAA;AAED,SAAS,uBAAuB,CAAC,GAAW;IAC3C,OAAO,qBAAqB,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;AAClD,CAAC;AAID,IAAI,MAAM,GAAG,CAAC,CAAC;AAEf,MAAM,gBAAiB,SAAQ,mBAAmB;IAwCjD,YAAY,MAAyB,EAAE,QAAgB;QACtD,KAAK,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;QAExB,IAAI,CAAC,aAAa,GAAG,8BAA8B,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC;QACjE,IAAI,CAAC,aAAa,GAAG,6BAA6B,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC;QAEhE,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC;QAC5B,IAAI,CAAC,OAAO,MAAM,CAAC,EAAE,KAAK,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE;YAC1D,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,GAAG,wBAAwB,GAAG,MAAM,CAAC,EAAE,CAAC;SACpE;QAED,IAAI,CAAC,OAAO,MAAM,CAAC,gBAAgB,CAAC,KAAK,WAAW,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,EAAE;YACjG,IAAI,CAAC,iBAAiB,GAAG,MAAM,CAAC,gBAAgB,CAAC,CAAC;SAClD;aAAM;YACN,IAAI,CAAC,iBAAiB,GAAG,wBAAwB,CAAC;SAClD;QAED,gEAAgE;QAChE,MAAM,IAAI,GAAG,EAAsE,CAAC;QACpF,KAAK,MAAM,CAAC,YAAY,EAAE,aAAa,CAAC,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACnE,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,GAAG,YAAY,CAAC,CAAC;YACvD,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,OAAO,KAAK,QAAQ,CAAC,EAAE;gBAC9C,MAAM,IAAI,KAAK,CAAC,4BAA4B,aAAa,SAAS,CAAC,CAAC;aACpE;YAED,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC;gBAChC,IAAI,EAAE,GAAG,EAAE;oBACV,OAAO,OAAO,CAAC,KAAK,EAAE,CAAC;gBACxB,CAAC;gBACD,KAAK,EAAE,CAAC,QAAQ,EAAE,EAAE;oBACnB,IAAI,QAAQ,KAAK,EAAE,EAAE;wBACpB,QAAQ,GAAG,IAAI,CAAC;qBAChB;oBACD,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;gBACzB,CAAC;gBACD,eAAe,EAAE,IAAI;aACrB,CAAC,CAAC,MAAM,CAAC,EAAC,iBAAiB,EAAE,IAAI,EAAC,CAAC,CAAC;SACrC;QACD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QAEvB,qDAAqD;QACrD,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;QAChD,IAAI,CAAC,WAAW,IAAI,CAAC,OAAO,WAAW,KAAK,QAAQ,CAAC,EAAE;YACtD,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;SACjD;QACD,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAE/B,MAAM,sBAAsB,GAAqB;YAChD,OAAO,EAAE,EAAE;YACX,WAAW,EAAE,MAAM;YACnB,YAAY,EAAE,OAAO;SACrB,CAAC;QACF,IAAI,MAAM,CAAC,mBAAmB,IAAI,CAAC,OAAO,MAAM,CAAC,mBAAmB,KAAK,QAAQ,CAAC,EAAE;YACnF,MAAM,mBAAmB,GAAG,MAAM,CAAC,mBAA0C,CAAC;YAC9E,IAAI,CAAC,mBAAmB,GAAG;gBAC1B,OAAO,EAAE,mBAAmB,CAAC,SAAS,CAAC,IAAI,sBAAsB,CAAC,OAAO;gBACzE,WAAW,EAAE,mBAAmB,CAAC,aAAa,CAAC,IAAI,sBAAsB,CAAC,WAAW;gBACrF,YAAY,EAAE,mBAAmB,CAAC,cAAc,CAAC,IAAI,sBAAsB,CAAC,YAAY;aACxF,CAAC;SACF;aAAM;YACN,IAAI,CAAC,mBAAmB,GAAG,sBAAsB,CAAC;SAClD;QAED,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QACzC,wFAAwF;QACxF,oFAAoF;QACpF,qBAAqB;QACrB,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAiB,CAAC;QACjE,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;QAC/C,IAAI,CAAC,UAAU,KAAK,IAAI,CAAC,IAAI,CAAC,UAAU,KAAK,EAAE,CAAC,EAAE;YACjD,IAAI,qBAAqB,GAAG,IAAI,CAAC;YACjC,KAAK,MAAM,CAAC,YAAY,CAAC,IAAI,IAAI,CAAC,iBAAiB,EAAE;gBACpD,IAAI,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,KAAK,UAAU,EAAE;oBACnD,qBAAqB,GAAG,KAAK,CAAC;oBAC9B,MAAM;iBACN;aACD;YACD,IAAI,CAAC,YAAY,CAAC,qBAAqB,CAAC,CAAC;SACzC;QAED,2DAA2D;QAC3D,IAAI,uBAAuB,GAAG,KAAK,CAAC,CAAC,yBAAyB;QAC9D,MAAM,mBAAmB,GAAG,CAAC,QAAgC,EAAE,EAAE;YAChE,IAAI,CAAC,uBAAuB,IAAI,IAAI,CAAC,YAAY,EAAE,EAAE;gBACpD,uBAAuB,GAAG,IAAI,CAAC;gBAE/B,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;gBACzC,KAAK,MAAM,UAAU,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;oBACxD,UAAU,CAAC,QAAQ,CAAC,CAAC;iBACrB;gBAED,uBAAuB,GAAG,KAAK,CAAC;aAChC;QACF,CAAC,CAAC;QACF,KAAK,MAAM,YAAY,IAAK,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAoB,EAAE;YAC5E,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;SAC7D;QAED,iFAAiF;QACjF,gEAAgE;QAChE,IAAI,CAAC,oBAAoB,GAAG,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,CAAC;QAC5E,6EAA6E;QAC7E,IAAI,CAAC,eAAe,GAAG,EAAiC,CAAC;QACzD,KAAK,MAAM,IAAI,IAAI,qBAAqB,EAAE;YACzC,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;gBAClF,SAAS;aACT;YACD,MAAM,KAAK,GAAmB,qBAAqB,CAAC,IAAI,CAAC,CAAC;YAC1D,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC;gBACxC,IAAI,EAAE,GAAG,EAAE;oBACV,IAAI,IAAI,CAAC,oBAAoB,EAAE,EAAE;wBAChC,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;qBACnC;yBAAM;wBACN,OAAO,IAAI,CAAC;qBACZ;gBACF,CAAC;gBACD,KAAK,EAAE,CAAC,QAAQ,EAAE,EAAE;oBACnB,IAAI,IAAI,CAAC,oBAAoB,EAAE,EAAE;wBAChC,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;wBACzC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;4BACzB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;yBAChC;qBACD;gBACF,CAAC;gBACD,eAAe,EAAE,IAAI;aACrB,CAAC,CAAC,MAAM,CAAC,EAAC,iBAAiB,EAAE,IAAI,EAAC,CAAC,CAAC;SACrC;QAED,+EAA+E;QAC/E,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC,YAAY,CAAC,GAAG,EAAE;YACzC,IAAI,MAAiC,CAAC;YACtC,IAAI,IAAI,CAAC,oBAAoB,EAAE,EAAE;gBAChC,MAAM,GAAG;oBACR,CAAC,UAAU,EAAE,UAAU,CAAC;oBACxB,CAAC,YAAY,EAAE,YAAY,CAAC;iBAC5B,CAAC;aACF;iBAAM;gBACN,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC;aAChC;YACD,OAAO,MAAM,CAAC;QACf,CAAC,CAAC,CAAC;QAEH,IAAI,aAAa,GAA0B;YAC1C,wBAAwB,EAAE,2BAA2B;YACrD,gBAAgB,EAAE,CAAC,CAAC;SACpB,CAAC;QACF,IAAI,OAAO,MAAM,CAAC,iBAAiB,KAAK,QAAQ,EAAE;YACjD,aAAa,CAAC,qBAAqB,GAAG,MAAM,CAAC,iBAAiB,CAAC;SAC/D;QAED,IAAI,CAAC,aAAa,GAAG,IAAI,sBAAsB,CAC9C,MAAM,CAAC,YAAY,CAAC,CAAC,CAAE,MAAM,CAAC,YAAqC,CAAC,CAAC,CAAC,IAAI,EAC1E,6BAA6B,EAC7B,gCAAgC,EAChC,aAAa,CACb,CAAC;IACH,CAAC;IAED,IAAI,OAAO;QACV,OAAO,CAAC,4BAA4B,EAAE,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC;IACzD,CAAC;IAED,6DAA6D;IAC7D;;;;;OAKG;IACH,kBAAkB,CAAC,GAAa;QAC/B,IAAI,IAAI,CAAC,oBAAoB,EAAE,IAAI,uBAAuB,CAAC,GAAG,CAAC,EAAE;YAChE,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;SACjC;QACD,IAAI,cAAc,CAAC,GAAG,CAAC,EAAE;YACxB,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;SAC5B;QACD,MAAM,IAAI,KAAK,CAAC,0CAA0C,GAAG,GAAG,CAAC,CAAC;IACnE,CAAC;IAED,aAAa,CAAC,GAAa;QAC1B,OAAO,IAAI,CAAC,aAAa,GAAG,GAAG,GAAG,GAAG,CAAC;IACvC,CAAC;IAED,8DAA8D;IAC9D,kBAAkB,CAAC,GAAa;QAC/B,OAAO;YACN,EAAE,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;YAC3B,sBAAsB,EAAE,IAAI,CAAC,aAAa;YAC1C,wBAAwB,EAAE,GAAG;SAC7B,CAAC;IACH,CAAC;IAED,8DAA8D;IAC9D,aAAa,CAAC,GAAa;QAC1B,MAAM,WAAW,GAAG,QAAQ,GAAG,GAAG,CAAC;QACnC,IAAI,WAAW,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjC,OAAO,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;SAClC;QAED,IAAI,IAAI,CAAC,oBAAoB,EAAE,IAAI,uBAAuB,CAAC,GAAG,CAAC,EAAE;YAChE,KAAK,MAAM,SAAS,IAAI,qBAAqB,CAAC,GAAG,CAAC,EAAE;gBACnD,8DAA8D;gBAC9D,6BAA6B;gBAC7B,MAAM,WAAW,GAAG,QAAQ,GAAG,SAAS,CAAC;gBACzC,IAAI,WAAW,IAAI,IAAI,CAAC,QAAQ,EAAE;oBACjC,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;iBAChC;aACD;SACD;QAED,OAAO,IAAI,CAAC;IACb,CAAC;IAED,uEAAuE;IACvE,UAAU;QACT,IAAI,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC;QAExC,iEAAiE;QACjE,gCAAgC;QAChC,IAAI,IAAI,CAAC,YAAY,EAAE,EAAE;YACxB,IAAI,UAAU,GAA2B,IAAI,CAAC;YAC9C,KAAK,MAAM,mBAAmB,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;gBACjE,MAAM,KAAK,GAAG,mBAAmB,EAAE,CAAC;gBACpC,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,EAAE,CAAC,EAAE;oBACvC,UAAU,GAAG,KAAK,CAAC;oBACnB,MAAM;iBACN;aACD;YACD,IAAI,UAAU,KAAK,IAAI,EAAE;gBACxB,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;gBAC7C,KAAK,MAAM,mBAAmB,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;oBACjE,mBAAmB,CAAC,UAAU,CAAC,CAAC;iBAChC;aACD;SACD;IACF,CAAC;IAEO,cAAc,CAAC,KAA6B;QACnD,IAAI,KAAK,KAAK,IAAI,EAAE;YACnB,OAAO,IAAI,CAAC;SACZ;QACD,0DAA0D;QAC1D,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC9B,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;YAC1B,IAAI,KAAK,CAAC,KAAK,CAAC,EAAE;gBACjB,OAAO,IAAI,CAAC;aACZ;SACD;QACD,OAAO,KAAK,CAAC;IACd,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,eAAkC;QAC7D,0FAA0F;QAC1F,IAAI,UAAU,GAAG,CAAC,OAAO,eAAe,CAAC,eAAe,CAAC,KAAK,WAAW,CAAC;YACzE,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;YACtC,CAAC,CAAC,KAAK,CAAC;QACT,IAAI,CAAC,UAAU,EAAE;YAChB,OAAO,KAAK,CAAC;SACb;QAED,kFAAkF;QAClF,MAAM,oBAAoB,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE;YACzD,OAAO,CAAC,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,oBAAoB,EAAE;YAC1B,OAAO,KAAK,CAAC;SACb;QAED,gFAAgF;QAChF,OAAO,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,KAAK,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;eAC9D,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE,KAAK,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;IACjE,CAAC;CACD;AAED,eAAe,4BAA4B,CAAC,gBAAgB,EAAE;;;;;;;;;;;;;;;;;;;;;;;;CAwB7D,CAAC,CAAC"}