{"version": 3, "file": "ame-toggle-checkbox.js", "sourceRoot": "", "sources": ["ame-toggle-checkbox.ts"], "names": [], "mappings": "AAAA,OAAO,EAAC,4BAA4B,EAAqB,mBAAmB,EAAC,MAAM,oBAAoB,CAAC;AAExG,MAAM,iBAAkB,SAAQ,mBAAmB;IAKlD,YAAY,MAAyB,EAAE,QAAgB;QACtD,KAAK,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;QAExB,IAAI,CAAC,OAAO,GAAG,CAAC,OAAO,MAAM,CAAC,OAAO,KAAK,WAAW,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;QAC/E,IAAI,CAAC,QAAQ,GAAG,CAAC,OAAO,MAAM,CAAC,QAAQ,KAAK,WAAW,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC;QAEnF,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,WAAW,EAAE;YAClD,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC;SAC9C;aAAM;YACN,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC,QAAQ,CAAC;gBAC5B,IAAI,EAAE,GAAG,EAAE;oBACV,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBAChE,CAAC;gBACD,KAAK,EAAE,CAAC,QAAQ,EAAE,EAAE;oBACnB,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CACxB,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAClD,CAAC;gBACH,CAAC;gBACD,eAAe,EAAE,IAAI;aACrB,CAAC,CAAC;SACH;IACF,CAAC;IAED,IAAI,OAAO;QACV,OAAO,CAAC,6BAA6B,EAAE,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC;IAC1D,CAAC;CACD;AAED,4EAA4E;AAC5E,+EAA+E;AAC/E,qDAAqD;AAErD,eAAe,4BAA4B,CAAC,iBAAiB,EAAE;;;;;;;;;CAS9D,CAAC,CAAC"}