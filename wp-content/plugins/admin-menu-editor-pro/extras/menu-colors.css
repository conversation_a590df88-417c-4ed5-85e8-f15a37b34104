/* This is just a template for testing. */
/* Admin Menu */
#adminmenu > li {
  background: #1e73be;
  /* Admin Menu: submenu */
  /* Admin Menu: current */
  /* Admin Menu: bubble */ }
  #adminmenu > li a {
    color: #dd9933; }
  #adminmenu > li div.wp-menu-image:before {
    color: #dd9933; }
  #adminmenu > li a:hover, #adminmenu > li.menu-top:hover, #adminmenu > li.opensub > a.menu-top, #adminmenu > li > a.menu-top:focus {
    color: #dd9933; }
  #adminmenu > li.menu-top:hover, #adminmenu > li.opensub > a.menu-top, #adminmenu > li > a.menu-top:focus {
    background-color: #81d742; }
  #adminmenu > li.menu-top:hover div.wp-menu-image:before, #adminmenu > li.menu-top > a:focus div.wp-menu-image:before, #adminmenu > li.opensub > a.menu-top div.wp-menu-image:before {
    color: #dd9933; }
  #adminmenu > li .wp-submenu, #adminmenu > li.wp-has-current-submenu .wp-submenu, #adminmenu > li.wp-has-current-submenu.opensub .wp-submenu, .folded #adminmenu > li.wp-has-current-submenu .wp-submenu,
  #adminmenu > li a.wp-has-current-submenu:focus + .wp-submenu {
    background: #19609f; }
  #adminmenu > li.wp-has-submenu.wp-not-current-submenu.opensub:hover:after {
    border-right-color: #19609f; }
  #adminmenu > li .wp-submenu .wp-submenu-head {
    color: #a48e5d; }
  #adminmenu > li .wp-submenu a, #adminmenu > li.wp-has-current-submenu .wp-submenu a,
  #adminmenu > li a.wp-has-current-submenu:focus + .wp-submenu a, .folded #adminmenu > li.wp-has-current-submenu .wp-submenu a, #adminmenu > li.wp-has-current-submenu.opensub .wp-submenu a {
    color: #a48e5d; }
    #adminmenu > li .wp-submenu a:focus, #adminmenu > li .wp-submenu a:hover, #adminmenu > li.wp-has-current-submenu .wp-submenu a:focus, #adminmenu > li.wp-has-current-submenu .wp-submenu a:hover,
    #adminmenu > li a.wp-has-current-submenu:focus + .wp-submenu a:focus,
    #adminmenu > li a.wp-has-current-submenu:focus + .wp-submenu a:hover, .folded #adminmenu > li.wp-has-current-submenu .wp-submenu a:focus, .folded #adminmenu > li.wp-has-current-submenu .wp-submenu a:hover, #adminmenu > li.wp-has-current-submenu.opensub .wp-submenu a:focus, #adminmenu > li.wp-has-current-submenu.opensub .wp-submenu a:hover {
      color: #81d742; }
  #adminmenu > li .wp-submenu li.current a,
  #adminmenu > li a.wp-has-current-submenu:focus + .wp-submenu li.current a, #adminmenu > li.wp-has-current-submenu.opensub .wp-submenu li.current a {
    color: #dd9933; }
    #adminmenu > li .wp-submenu li.current a:hover, #adminmenu > li .wp-submenu li.current a:focus,
    #adminmenu > li a.wp-has-current-submenu:focus + .wp-submenu li.current a:hover,
    #adminmenu > li a.wp-has-current-submenu:focus + .wp-submenu li.current a:focus, #adminmenu > li.wp-has-current-submenu.opensub .wp-submenu li.current a:hover, #adminmenu > li.wp-has-current-submenu.opensub .wp-submenu li.current a:focus {
      color: #81d742; }
  #adminmenu > li.current a.menu-top, #adminmenu > li.wp-has-current-submenu a.wp-has-current-submenu, #adminmenu > li.wp-has-current-submenu .wp-submenu .wp-submenu-head, .folded #adminmenu > li.current.menu-top {
    color: #dd9933;
    background: #81d742; }
  #adminmenu > li.wp-has-current-submenu div.wp-menu-image:before {
    color: #dd9933; }
  #adminmenu > li .awaiting-mod,
  #adminmenu > li .update-plugins {
    color: #dd9933;
    background: #d54e21; }
  #adminmenu > li .current a .awaiting-mod,
  #adminmenu > li a.wp-has-current-submenu .update-plugins, #adminmenu > li:hover a .awaiting-mod, #adminmenu > li.menu-top:hover > a .update-plugins {
    color: #dd9933;
    background: #19609f; }

#adminmenuback, #adminmenuwrap, #adminmenu {
  background-color: #1e73be; }

/*# sourceMappingURL=menu-colors.css.map */
