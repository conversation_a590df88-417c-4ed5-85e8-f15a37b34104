<?php
/**
 * Search Handler
 * 
 * Handles search functionality for quiz attempts
 * 
 * @package TutorLMSAdvancedSearch
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class TutorLMS_Advanced_Search_Handler {
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // AJAX handlers for search
        add_action('wp_ajax_tutor_advanced_search', array($this, 'handle_ajax_search'));
        add_action('wp_ajax_nopriv_tutor_advanced_search', array($this, 'handle_ajax_search'));
        
        // Add search parameter handling
        add_action('init', array($this, 'handle_search_parameters'));
    }
    
    /**
     * Handle search parameters from URL
     */
    public function handle_search_parameters() {
        // This will be used in the template to get search parameters
        // The actual search logic is handled in the template
    }
    
    /**
     * Get search parameter from request
     */
    public function get_search_parameter() {
        if (class_exists('TUTOR\\Input')) {
            return \TUTOR\Input::get('search', '');
        }
        
        // Fallback for older versions
        return isset($_GET['search']) ? sanitize_text_field($_GET['search']) : '';
    }
    
    /**
     * Get course filter parameter
     */
    public function get_course_filter() {
        if (class_exists('TUTOR\\Input')) {
            return \TUTOR\Input::get('course-id', '');
        }
        
        return isset($_GET['course-id']) ? sanitize_text_field($_GET['course-id']) : '';
    }
    
    /**
     * Get date filter parameter
     */
    public function get_date_filter() {
        if (class_exists('TUTOR\\Input')) {
            return \TUTOR\Input::get('date', '');
        }
        
        return isset($_GET['date']) ? sanitize_text_field($_GET['date']) : '';
    }
    
    /**
     * Get order filter parameter
     */
    public function get_order_filter() {
        if (class_exists('TUTOR\\Input')) {
            return \TUTOR\Input::get('order', 'DESC');
        }
        
        return isset($_GET['order']) ? sanitize_text_field($_GET['order']) : 'DESC';
    }
    
    /**
     * Handle AJAX search request
     */
    public function handle_ajax_search() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'tutor_advanced_search_nonce')) {
            wp_die('Security check failed');
        }
        
        // Get search parameters
        $search_term = sanitize_text_field($_POST['search'] ?? '');
        $course_id = sanitize_text_field($_POST['course_id'] ?? '');
        $date = sanitize_text_field($_POST['date'] ?? '');
        $order = sanitize_text_field($_POST['order'] ?? 'DESC');
        $page = intval($_POST['page'] ?? 1);
        
        // Get pagination settings
        $item_per_page = tutor_utils()->get_option('pagination_per_page');
        $offset = ($page - 1) * $item_per_page;
        
        // Get quiz attempts with search
        if (class_exists('Tutor\\Models\\QuizModel')) {
            $quiz_attempts = \Tutor\Models\QuizModel::get_quiz_attempts(
                $offset,
                $item_per_page,
                $search_term,
                $course_id,
                $date,
                $order,
                null,
                false,
                true
            );
            
            $total_attempts = \Tutor\Models\QuizModel::get_quiz_attempts(
                $offset,
                $item_per_page,
                $search_term,
                $course_id,
                $date,
                $order,
                null,
                true,
                true
            );
        } else {
            $quiz_attempts = array();
            $total_attempts = 0;
        }
        
        // Prepare response
        $response = array(
            'success' => true,
            'data' => array(
                'attempts' => $quiz_attempts,
                'total' => $total_attempts,
                'page' => $page,
                'per_page' => $item_per_page,
                'search_term' => $search_term
            )
        );
        
        wp_send_json($response);
    }
    
    /**
     * Build search URL with parameters
     */
    public function build_search_url($search_term = '', $course_id = '', $date = '', $order = 'DESC') {
        $base_url = tutor_utils()->tutor_dashboard_url('quiz-attempts');
        
        $params = array();
        
        if (!empty($search_term)) {
            $params['search'] = urlencode($search_term);
        }
        
        if (!empty($course_id)) {
            $params['course-id'] = $course_id;
        }
        
        if (!empty($date)) {
            $params['date'] = $date;
        }
        
        if ($order !== 'DESC') {
            $params['order'] = $order;
        }
        
        if (!empty($params)) {
            $base_url .= '?' . http_build_query($params);
        }
        
        return $base_url;
    }
    
    /**
     * Get search results info text
     */
    public function get_search_results_info($search_term, $total_results) {
        if (empty($search_term)) {
            return '';
        }
        
        if ($total_results === 0) {
            return sprintf(
                __('No quiz attempts found for "%s".', 'tutor-lms-advanced-search'),
                esc_html($search_term)
            );
        }
        
        return sprintf(
            _n(
                '%d quiz attempt found for "%s".',
                '%d quiz attempts found for "%s".',
                $total_results,
                'tutor-lms-advanced-search'
            ),
            $total_results,
            esc_html($search_term)
        );
    }
    
    /**
     * Sanitize search input
     */
    public function sanitize_search_input($input) {
        return sanitize_text_field(trim($input));
    }
}
