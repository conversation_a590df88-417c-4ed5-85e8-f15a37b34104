<?php
/**
 * Template Override Handler
 * 
 * Handles template overrides for Tutor LMS dashboard pages
 * 
 * @package TutorLMSAdvancedSearch
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class TutorLMS_Advanced_Search_Template_Override {
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Override dashboard templates
        add_filter('load_dashboard_template_part_from_other_location', array($this, 'override_dashboard_template'), 10, 1);
        
        // Add custom template actions
        add_action('tutor_load_dashboard_template_before', array($this, 'before_template_load'), 10, 1);
        add_action('tutor_load_dashboard_template_after', array($this, 'after_template_load'), 10, 1);
    }
    
    /**
     * Override dashboard template for quiz attempts
     */
    public function override_dashboard_template($template_path) {
        global $wp_query;
        
        // Check if we're on the quiz-attempts page
        if (isset($wp_query->query_vars['tutor_dashboard_page']) && 
            $wp_query->query_vars['tutor_dashboard_page'] === 'quiz-attempts') {
            
            $custom_template = TUTOR_LMS_ADVANCED_SEARCH_PLUGIN_PATH . 'templates/dashboard/quiz-attempts.php';
            
            if (file_exists($custom_template)) {
                return $custom_template;
            }
        }
        
        return $template_path;
    }
    
    /**
     * Actions before template load
     */
    public function before_template_load($template_name) {
        // Add any pre-template loading logic here
        if ($template_name === 'quiz-attempts') {
            // Enqueue additional scripts if needed
            $this->enqueue_quiz_attempts_assets();
        }
    }
    
    /**
     * Actions after template load
     */
    public function after_template_load($template_name) {
        // Add any post-template loading logic here
    }
    
    /**
     * Enqueue assets specific to quiz attempts page
     */
    private function enqueue_quiz_attempts_assets() {
        // Additional CSS for quiz attempts page
        wp_add_inline_style('tutor-lms-advanced-search', '
            .tutor-advanced-search-wrapper {
                margin-bottom: 20px;
            }
            
            .tutor-advanced-search-input {
                width: 100%;
                max-width: 300px;
            }
            
            .tutor-search-clear-btn {
                margin-left: 10px;
                padding: 8px 16px;
                background: #f1f1f1;
                border: 1px solid #ddd;
                border-radius: 4px;
                cursor: pointer;
                text-decoration: none;
                color: #666;
                font-size: 14px;
            }
            
            .tutor-search-clear-btn:hover {
                background: #e1e1e1;
                color: #333;
            }
            
            .tutor-search-results-info {
                margin: 10px 0;
                padding: 10px;
                background: #f9f9f9;
                border-left: 4px solid #007cba;
                font-size: 14px;
            }
        ');
    }
    
    /**
     * Get template path
     */
    public function get_template_path($template_name) {
        $template_path = TUTOR_LMS_ADVANCED_SEARCH_PLUGIN_PATH . 'templates/' . $template_name . '.php';
        
        if (file_exists($template_path)) {
            return $template_path;
        }
        
        return false;
    }
    
    /**
     * Load custom template
     */
    public function load_template($template_name, $variables = array()) {
        $template_path = $this->get_template_path($template_name);
        
        if ($template_path) {
            // Extract variables for template
            if (!empty($variables)) {
                extract($variables);
            }
            
            include $template_path;
            return true;
        }
        
        return false;
    }
    
    /**
     * Check if template exists
     */
    public function template_exists($template_name) {
        return $this->get_template_path($template_name) !== false;
    }
}
