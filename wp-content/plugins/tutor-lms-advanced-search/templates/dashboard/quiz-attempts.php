<?php
/**
 * Enhanced Frontend Students Quiz Attempts with Advanced Search
 *
 * @package TutorLMSAdvancedSearch
 * @subpackage Templates
 * <AUTHOR> Name
 * @since 1.0.0
 */

use TUTOR\Input;
use Tutor\Models\QuizModel;

if ( isset( $_GET['view_quiz_attempt_id'] ) ) {
	// Load single attempt details if ID provided.
	include tutor()->path . 'templates/dashboard/quiz-attempts/quiz-reviews.php';
	return;
}

// Get search handler instance
$search_handler = new TutorLMS_Advanced_Search_Handler();

$item_per_page = tutor_utils()->get_option( 'pagination_per_page' );
$current_page  = max( 1, Input::get( 'current_page', 1, Input::TYPE_INT ) );
$offset        = ( $current_page - 1 ) * $item_per_page;

// Filter params including search
$course_filter = Input::get( 'course-id', '' );
$order_filter  = Input::get( 'order', 'DESC' );
$date_filter   = Input::get( 'date', '' );
$search_filter = Input::get( 'search', '' ); // NEW: Search parameter

?>

<div class="tutor-fs-5 tutor-fw-medium tutor-color-black tutor-mb-24 tutor-text-capitalize"><?php esc_html_e( 'Quiz Attempts', 'tutor' ); ?></div>

<?php
// Load enhanced filter template with search
tutor_load_template_from_custom_path( 
    TUTOR_LMS_ADVANCED_SEARCH_PLUGIN_PATH . 'templates/dashboard/elements/filters.php',
    array(
        'search_filter' => $search_filter,
        'course_filter' => $course_filter,
        'order_filter' => $order_filter,
        'date_filter' => $date_filter
    )
);

// Display search results info if search is active
if ( !empty( $search_filter ) ) {
    ?>
    <div class="tutor-search-results-info">
        <strong><?php esc_html_e( 'Search Results for:', 'tutor-lms-advanced-search' ); ?></strong> 
        "<?php echo esc_html( $search_filter ); ?>"
        <a href="<?php echo esc_url( tutor_utils()->tutor_dashboard_url( 'quiz-attempts' ) ); ?>" class="tutor-search-clear-btn">
            <?php esc_html_e( 'Clear Search', 'tutor-lms-advanced-search' ); ?>
        </a>
    </div>
    <?php
}

$course_id           = tutor_utils()->get_assigned_courses_ids_by_instructors();

// Enhanced query with search parameter
$quiz_attempts       = QuizModel::get_quiz_attempts( 
    $offset, 
    $item_per_page, 
    $search_filter,  // Pass search filter instead of empty string
    $course_filter, 
    $date_filter, 
    $order_filter, 
    null, 
    false, 
    true 
);

$quiz_attempts_count = QuizModel::get_quiz_attempts( 
    $offset, 
    $item_per_page, 
    $search_filter,  // Pass search filter for count too
    $course_filter, 
    $date_filter, 
    $order_filter, 
    null, 
    true, 
    true 
);

// Display results count
if ( !empty( $search_filter ) ) {
    $results_info = $search_handler->get_search_results_info( $search_filter, $quiz_attempts_count );
    if ( !empty( $results_info ) ) {
        echo '<div class="tutor-search-results-info">' . esc_html( $results_info ) . '</div>';
    }
}

tutor_load_template_from_custom_path(
	tutor()->path . '/views/quiz/attempt-table.php',
	array(
		'attempt_list' => $quiz_attempts,
		'context'      => 'frontend-dashboard-students-attempts',
	)
);

$pagination_data = array(
	'total_items' => $quiz_attempts_count,
	'per_page'    => $item_per_page,
	'paged'       => $current_page,
);

// Add search parameters to pagination
if ( !empty( $search_filter ) || !empty( $course_filter ) || !empty( $date_filter ) || $order_filter !== 'DESC' ) {
    $pagination_data['search_params'] = array(
        'search' => $search_filter,
        'course-id' => $course_filter,
        'date' => $date_filter,
        'order' => $order_filter
    );
}

if ( $quiz_attempts_count > $item_per_page ) {
	$pagination_template_frontend = tutor()->path . 'templates/dashboard/elements/pagination.php';
	tutor_load_template_from_custom_path( $pagination_template_frontend, $pagination_data );
}
?>

<script type="text/javascript">
jQuery(document).ready(function($) {
    // Handle search form submission
    $('#tutor-advanced-search-form').on('submit', function(e) {
        e.preventDefault();
        
        var searchTerm = $('#tutor-search-input').val();
        var courseId = $('select[name="course-id"]').val();
        var date = $('input[name="date"]').val();
        var order = $('select[name="order"]').val();
        
        // Build URL with parameters
        var url = '<?php echo esc_url( tutor_utils()->tutor_dashboard_url( 'quiz-attempts' ) ); ?>';
        var params = [];
        
        if (searchTerm) params.push('search=' + encodeURIComponent(searchTerm));
        if (courseId) params.push('course-id=' + encodeURIComponent(courseId));
        if (date) params.push('date=' + encodeURIComponent(date));
        if (order && order !== 'DESC') params.push('order=' + encodeURIComponent(order));
        
        if (params.length > 0) {
            url += '?' + params.join('&');
        }
        
        window.location.href = url;
    });
    
    // Handle clear search
    $('.tutor-search-clear-btn').on('click', function(e) {
        e.preventDefault();
        window.location.href = '<?php echo esc_url( tutor_utils()->tutor_dashboard_url( 'quiz-attempts' ) ); ?>';
    });
    
    // Real-time search (optional)
    var searchTimeout;
    $('#tutor-search-input').on('input', function() {
        clearTimeout(searchTimeout);
        var searchTerm = $(this).val();
        
        if (searchTerm.length >= 3) {
            searchTimeout = setTimeout(function() {
                // Trigger search after 500ms delay
                $('#tutor-advanced-search-form').submit();
            }, 500);
        }
    });
});
</script>
