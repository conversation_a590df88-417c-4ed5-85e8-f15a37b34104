<?php
/**
 * Enhanced Filter Template with Advanced Search
 * Contains search field and basic fields for filter/sorting table data
 *
 * @package TutorLMSAdvancedSearch
 * @subpackage Templates
 * <AUTHOR> Name
 * @since 1.0.0
 */

use TUTOR\Input;
use Tutor\Models\CourseModel;

$courses = ( current_user_can( 'administrator' ) ) ? CourseModel::get_courses() : CourseModel::get_courses_by_instructor();

// Filter params
$course_id    = isset($course_filter) ? $course_filter : Input::get( 'course-id', '' );
$order_filter = isset($order_filter) ? $order_filter : Input::get( 'order', 'DESC' );
$date_filter  = isset($date_filter) ? $date_filter : Input::get( 'date', '' );
$search_filter = isset($search_filter) ? $search_filter : Input::get( 'search', '' );
?>

<form id="tutor-advanced-search-form" method="get" action="">
    <!-- Preserve the dashboard page parameter -->
    <input type="hidden" name="tutor_dashboard_page" value="quiz-attempts">
    
    <div class="tutor-advanced-search-wrapper tutor-mb-24">
        <div class="tutor-row">
            <!-- Search Input - Full width on mobile, half width on larger screens -->
            <div class="tutor-col-12 tutor-col-lg-6 tutor-mb-16">
                <label class="tutor-form-label tutor-d-block tutor-mb-12">
                    <?php esc_html_e( 'Search Students', 'tutor-lms-advanced-search' ); ?>
                </label>
                <div class="tutor-form-wrap">
                    <span class="tutor-form-icon">
                        <span class="tutor-icon-search" aria-hidden="true"></span>
                    </span>
                    <input 
                        type="text" 
                        id="tutor-search-input"
                        name="search" 
                        class="tutor-form-control tutor-advanced-search-input" 
                        placeholder="<?php esc_attr_e( 'Search by student name or email...', 'tutor-lms-advanced-search' ); ?>"
                        value="<?php echo esc_attr( $search_filter ); ?>"
                    >
                </div>
            </div>
            
            <!-- Search Button -->
            <div class="tutor-col-12 tutor-col-lg-6 tutor-mb-16 tutor-d-flex tutor-align-end">
                <button type="submit" class="tutor-btn tutor-btn-primary tutor-mr-12">
                    <span class="tutor-icon-search tutor-mr-8"></span>
                    <?php esc_html_e( 'Search', 'tutor-lms-advanced-search' ); ?>
                </button>
                
                <?php if ( !empty( $search_filter ) || !empty( $course_id ) || !empty( $date_filter ) || $order_filter !== 'DESC' ) : ?>
                    <a href="<?php echo esc_url( tutor_utils()->tutor_dashboard_url( 'quiz-attempts' ) ); ?>" 
                       class="tutor-btn tutor-btn-ghost">
                        <?php esc_html_e( 'Clear All', 'tutor-lms-advanced-search' ); ?>
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Existing Filters Row -->
    <div class="tutor-row">
        <div class="tutor-col-lg-4">
            <label class="tutor-form-label tutor-d-block tutor-mb-12">
                <?php esc_html_e( 'Courses', 'tutor' ); ?>
            </label>
            <select name="course-id" class="tutor-form-select tutor-form-control tutor-announcement-course-sorting" data-searchable>
                <option value=""><?php esc_html_e( 'All', 'tutor' ); ?></option>
                <?php if ( $courses ) : ?>
                    <?php foreach ( $courses as $course ) : ?>
                        <option value="<?php echo esc_attr( $course->ID ); ?>" <?php selected( $course_id, $course->ID ); ?>>
                            <?php echo esc_html( $course->post_title ); ?>
                        </option>
                    <?php endforeach; ?>
                <?php else : ?>
                    <option value=""><?php esc_html_e( 'No course found', 'tutor' ); ?></option>
                <?php endif; ?>
            </select>
        </div>
        
        <div class="tutor-col-lg-4 tutor-mt-16 tutor-mt-lg-0">
            <label class="tutor-form-label tutor-d-block tutor-mb-10"><?php esc_html_e( 'Sort By', 'tutor' ); ?></label>
            <select name="order" class="tutor-form-select tutor-form-control tutor-announcement-order-sorting" data-search="no">
                <option value="ASC" <?php selected( $order_filter, 'ASC' ); ?>><?php esc_html_e( 'ASC', 'tutor' ); ?></option>
                <option value="DESC" <?php selected( $order_filter, 'DESC' ); ?>><?php esc_html_e( 'DESC', 'tutor' ); ?></option>
            </select>
        </div>
        
        <div class="tutor-col-lg-4 tutor-mt-16 tutor-mt-lg-0">
            <label class="tutor-form-label tutor-d-block tutor-mb-10"><?php esc_html_e( 'Create Date', 'tutor' ); ?></label>
            <div class="tutor-v2-date-picker">
                <div class="tutor-form-wrap">
                    <span class="tutor-form-icon tutor-form-icon-reverse">
                        <span class="tutor-icon-calender-line" aria-hidden="true"></span>
                    </span>
                    <input 
                        type="date" 
                        name="date" 
                        class="tutor-form-control" 
                        value="<?php echo esc_attr( $date_filter ); ?>"
                        placeholder="<?php esc_attr_e( 'Select date...', 'tutor-lms-advanced-search' ); ?>"
                    >
                </div>
            </div>
        </div>
    </div>
</form>

<style>
.tutor-advanced-search-wrapper {
    background: #f9f9f9;
    padding: 20px;
    border-radius: 6px;
    border: 1px solid #e1e1e1;
}

.tutor-advanced-search-input {
    padding-left: 40px !important;
}

.tutor-form-wrap {
    position: relative;
}

.tutor-form-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
    z-index: 2;
}

.tutor-form-icon-reverse {
    right: 12px;
    left: auto;
}

.tutor-search-results-info {
    margin: 15px 0;
    padding: 12px 16px;
    background: #e7f3ff;
    border-left: 4px solid #007cba;
    border-radius: 4px;
    font-size: 14px;
}

.tutor-search-clear-btn {
    display: inline-block;
    margin-left: 10px;
    padding: 4px 12px;
    background: #f1f1f1;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    color: #666;
    font-size: 12px;
    transition: all 0.2s ease;
}

.tutor-search-clear-btn:hover {
    background: #e1e1e1;
    color: #333;
    text-decoration: none;
}

@media (max-width: 768px) {
    .tutor-advanced-search-wrapper .tutor-row {
        margin: 0;
    }
    
    .tutor-advanced-search-wrapper .tutor-col-12 {
        padding: 0 0 16px 0;
    }
    
    .tutor-btn {
        width: 100%;
        margin-bottom: 8px;
    }
    
    .tutor-btn + .tutor-btn {
        margin-left: 0;
    }
}
</style>
