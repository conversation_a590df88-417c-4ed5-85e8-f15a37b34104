/**
 * <PERSON>tor LMS Advanced Search JavaScript
 * 
 * @package TutorLMSAdvancedSearch
 * @version 1.0.0
 */

(function($) {
    'use strict';
    
    // Initialize when document is ready
    $(document).ready(function() {
        TutorAdvancedSearch.init();
    });
    
    /**
     * Main Advanced Search Object
     */
    window.TutorAdvancedSearch = {
        
        // Configuration
        config: {
            searchDelay: 500,
            minSearchLength: 2,
            enableRealTimeSearch: false // Set to true for real-time search
        },
        
        // Cache DOM elements
        elements: {},
        
        // Search timeout
        searchTimeout: null,
        
        /**
         * Initialize the search functionality
         */
        init: function() {
            this.cacheElements();
            this.bindEvents();
            this.initializeState();
        },
        
        /**
         * C<PERSON> frequently used DOM elements
         */
        cacheElements: function() {
            this.elements = {
                searchForm: $('#tutor-advanced-search-form'),
                searchInput: $('#tutor-search-input'),
                courseSelect: $('select[name="course-id"]'),
                dateInput: $('input[name="date"]'),
                orderSelect: $('select[name="order"]'),
                clearButtons: $('.tutor-search-clear-btn, .tutor-reset-btn'),
                clearInputButton: $('.tutor-search-clear-input'),
                submitButton: $('.tutor-search-btn'),
                filtersToggle: $('.tutor-filters-toggle'),
                filtersContent: $('#tutor-advanced-filters-content'),
                activeFilters: $('.tutor-active-filters'),
                filterRemoveButtons: $('.tutor-filter-remove'),
                resultsContainer: $('.tutor-quiz-attempts-container'),
                loadingIndicator: $('.tutor-search-loading')
            };
        },
        
        /**
         * Bind event handlers
         */
        bindEvents: function() {
            var self = this;
            
            // Form submission
            this.elements.searchForm.on('submit', function(e) {
                e.preventDefault();
                self.handleFormSubmit();
            });
            
            // Clear search buttons
            this.elements.clearButtons.on('click', function(e) {
                e.preventDefault();
                self.clearSearch();
            });

            // Clear input button
            this.elements.clearInputButton.on('click', function(e) {
                e.preventDefault();
                self.elements.searchInput.val('').focus();
                $(this).hide();
            });

            // Show/hide clear input button
            this.elements.searchInput.on('input', function() {
                var hasValue = $(this).val().length > 0;
                self.elements.clearInputButton.toggle(hasValue);
            });

            // Filters toggle
            this.elements.filtersToggle.on('click', function(e) {
                e.preventDefault();
                self.toggleFilters();
            });

            // Filter remove buttons
            this.elements.filterRemoveButtons.on('click', function(e) {
                e.preventDefault();
                var url = $(this).attr('href');
                if (url) {
                    window.location.href = url;
                }
            });
            
            // Real-time search (if enabled)
            if (this.config.enableRealTimeSearch) {
                this.elements.searchInput.on('input', function() {
                    self.handleRealTimeSearch();
                });
            }
            
            // Filter changes
            this.elements.courseSelect.on('change', function() {
                if (self.config.enableRealTimeSearch) {
                    self.handleFormSubmit();
                }
            });
            
            this.elements.dateInput.on('change', function() {
                if (self.config.enableRealTimeSearch) {
                    self.handleFormSubmit();
                }
            });
            
            this.elements.orderSelect.on('change', function() {
                if (self.config.enableRealTimeSearch) {
                    self.handleFormSubmit();
                }
            });
            
            // Keyboard shortcuts
            $(document).on('keydown', function(e) {
                // Ctrl/Cmd + K to focus search
                if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                    e.preventDefault();
                    self.elements.searchInput.focus();
                }
                
                // Escape to clear search
                if (e.key === 'Escape' && self.elements.searchInput.is(':focus')) {
                    self.clearSearch();
                }
            });
        },
        
        /**
         * Initialize component state
         */
        initializeState: function() {
            // Focus search input if no search term is present
            if (!this.elements.searchInput.val()) {
                // Small delay to ensure page is fully loaded
                setTimeout(() => {
                    this.elements.searchInput.focus();
                }, 100);
            }

            // Initialize clear input button visibility
            this.elements.clearInputButton.toggle(this.elements.searchInput.val().length > 0);

            // Initialize filters toggle state
            this.initializeFiltersToggle();

            // Add search tips
            this.addSearchTips();

            // Add fade-in animation
            this.elements.searchForm.addClass('tutor-fade-in');
        },
        
        /**
         * Handle form submission
         */
        handleFormSubmit: function() {
            var searchTerm = this.elements.searchInput.val().trim();
            var courseId = this.elements.courseSelect.val();
            var date = this.elements.dateInput.val();
            var order = this.elements.orderSelect.val();
            
            // Build URL with parameters
            var url = this.buildSearchUrl(searchTerm, courseId, date, order);
            
            // Show loading state
            this.showLoading();
            
            // Navigate to search results
            window.location.href = url;
        },
        
        /**
         * Handle real-time search
         */
        handleRealTimeSearch: function() {
            var self = this;
            var searchTerm = this.elements.searchInput.val().trim();
            
            // Clear previous timeout
            if (this.searchTimeout) {
                clearTimeout(this.searchTimeout);
            }
            
            // Only search if term is long enough
            if (searchTerm.length >= this.config.minSearchLength || searchTerm.length === 0) {
                this.searchTimeout = setTimeout(function() {
                    self.handleFormSubmit();
                }, this.config.searchDelay);
            }
        },
        
        /**
         * Clear search and filters
         */
        clearSearch: function() {
            // Clear form fields
            this.elements.searchInput.val('');
            this.elements.courseSelect.val('');
            this.elements.dateInput.val('');
            this.elements.orderSelect.val('DESC');
            
            // Navigate to clean URL
            var baseUrl = this.getBaseUrl();
            window.location.href = baseUrl;
        },
        
        /**
         * Build search URL with parameters
         */
        buildSearchUrl: function(searchTerm, courseId, date, order) {
            var baseUrl = this.getBaseUrl();
            var params = [];
            
            if (searchTerm) {
                params.push('search=' + encodeURIComponent(searchTerm));
            }
            
            if (courseId) {
                params.push('course-id=' + encodeURIComponent(courseId));
            }
            
            if (date) {
                params.push('date=' + encodeURIComponent(date));
            }
            
            if (order && order !== 'DESC') {
                params.push('order=' + encodeURIComponent(order));
            }
            
            if (params.length > 0) {
                baseUrl += '?' + params.join('&');
            }
            
            return baseUrl;
        },
        
        /**
         * Get base URL for quiz attempts page
         */
        getBaseUrl: function() {
            // Try to get from localized data first
            if (typeof tutorAdvancedSearch !== 'undefined' && tutorAdvancedSearch.baseUrl) {
                return tutorAdvancedSearch.baseUrl;
            }
            
            // Fallback: construct from current URL
            var currentUrl = window.location.href;
            var baseUrl = currentUrl.split('?')[0];
            
            // Ensure it ends with quiz-attempts
            if (!baseUrl.includes('quiz-attempts')) {
                baseUrl = baseUrl.replace(/\/$/, '') + '/quiz-attempts/';
            }
            
            return baseUrl;
        },
        
        /**
         * Show loading state
         */
        showLoading: function() {
            this.elements.submitButton.prop('disabled', true);
            this.elements.searchForm.addClass('tutor-search-loading');
            
            // Update button text
            var originalText = this.elements.submitButton.text();
            this.elements.submitButton.data('original-text', originalText);
            this.elements.submitButton.html('<span class="tutor-icon-loading"></span> Searching...');
        },
        
        /**
         * Hide loading state
         */
        hideLoading: function() {
            this.elements.submitButton.prop('disabled', false);
            this.elements.searchForm.removeClass('tutor-search-loading');
            
            // Restore button text
            var originalText = this.elements.submitButton.data('original-text');
            if (originalText) {
                this.elements.submitButton.text(originalText);
            }
        },
        
        /**
         * Add search tips and help text
         */
        addSearchTips: function() {
            var searchInput = this.elements.searchInput;
            
            if (searchInput.length && !searchInput.attr('title')) {
                searchInput.attr('title', 'Search by student name, email, or course name. Use Ctrl+K to focus this field.');
            }
            
            // Add search tips below the input (optional)
            if ($('.tutor-search-tips').length === 0) {
                var tips = $('<div class="tutor-search-tips" style="font-size: 12px; color: #666; margin-top: 5px;">' +
                    'Tip: Search by student name, email, quiz name, or course name' +
                    '</div>');
                
                searchInput.closest('.tutor-form-wrap').after(tips);
            }
        },
        
        /**
         * Utility: Debounce function
         */
        debounce: function(func, wait, immediate) {
            var timeout;
            return function() {
                var context = this, args = arguments;
                var later = function() {
                    timeout = null;
                    if (!immediate) func.apply(context, args);
                };
                var callNow = immediate && !timeout;
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
                if (callNow) func.apply(context, args);
            };
        },
        
        /**
         * Toggle filters section
         */
        toggleFilters: function() {
            var isExpanded = this.elements.filtersContent.hasClass('tutor-show');

            if (isExpanded) {
                this.elements.filtersContent.removeClass('tutor-show').addClass('tutor-collapse');
                this.elements.filtersToggle.find('.tutor-toggle-text').text('Show Filters');
                this.elements.filtersToggle.attr('aria-expanded', 'false');
            } else {
                this.elements.filtersContent.removeClass('tutor-collapse').addClass('tutor-show');
                this.elements.filtersToggle.find('.tutor-toggle-text').text('Hide Filters');
                this.elements.filtersToggle.attr('aria-expanded', 'true');
            }
        },

        /**
         * Initialize filters toggle state
         */
        initializeFiltersToggle: function() {
            var hasActiveFilters = this.elements.activeFilters.length > 0 && this.elements.activeFilters.is(':visible');
            var isExpanded = this.elements.filtersContent.hasClass('tutor-show');

            if (hasActiveFilters && !isExpanded) {
                this.elements.filtersContent.removeClass('tutor-collapse').addClass('tutor-show');
                this.elements.filtersToggle.find('.tutor-toggle-text').text('Hide Filters');
                this.elements.filtersToggle.attr('aria-expanded', 'true');
            }
        },

        /**
         * Enhanced show loading state
         */
        showLoading: function() {
            this.elements.submitButton.prop('disabled', true);
            this.elements.searchForm.addClass('tutor-search-loading');

            // Update button content with loading spinner
            var originalContent = this.elements.submitButton.html();
            this.elements.submitButton.data('original-content', originalContent);
            this.elements.submitButton.html('<i class="tutor-icon-loading tutor-pulse"></i> <span>Searching...</span>');

            // Add loading class to search input
            this.elements.searchInput.addClass('tutor-pulse');
        },

        /**
         * Enhanced hide loading state
         */
        hideLoading: function() {
            this.elements.submitButton.prop('disabled', false);
            this.elements.searchForm.removeClass('tutor-search-loading');

            // Restore button content
            var originalContent = this.elements.submitButton.data('original-content');
            if (originalContent) {
                this.elements.submitButton.html(originalContent);
            }

            // Remove loading class from search input
            this.elements.searchInput.removeClass('tutor-pulse');
        },

        /**
         * Add visual feedback for successful search
         */
        showSearchSuccess: function() {
            var successMessage = $('<div class="tutor-search-success tutor-fade-in">' +
                '<i class="tutor-icon-check"></i> Search completed successfully!' +
                '</div>');

            this.elements.searchForm.after(successMessage);

            setTimeout(function() {
                successMessage.fadeOut(function() {
                    $(this).remove();
                });
            }, 3000);
        },

        /**
         * Utility: Get URL parameter
         */
        getUrlParameter: function(name) {
            name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
            var regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
            var results = regex.exec(location.search);
            return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
        }
    };

})(jQuery);
