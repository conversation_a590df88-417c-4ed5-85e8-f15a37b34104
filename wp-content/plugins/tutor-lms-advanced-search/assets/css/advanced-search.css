/**
 * <PERSON><PERSON> Advanced Search Styles
 * 
 * @package TutorLMSAdvancedSearch
 * @version 1.0.0
 */

/* Search Wrapper */
.tutor-advanced-search-wrapper {
    background: #f9f9f9;
    padding: 20px;
    border-radius: 6px;
    border: 1px solid #e1e1e1;
    margin-bottom: 24px;
}

/* Search Input Styling */
.tutor-advanced-search-input {
    width: 100%;
    padding-left: 40px !important;
    font-size: 14px;
    border: 1px solid #ddd;
    border-radius: 4px;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.tutor-advanced-search-input:focus {
    border-color: #007cba;
    box-shadow: 0 0 0 2px rgba(0, 124, 186, 0.1);
    outline: none;
}

/* Form Icon Positioning */
.tutor-form-wrap {
    position: relative;
}

.tutor-form-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
    z-index: 2;
    font-size: 16px;
}

.tutor-form-icon-reverse {
    right: 12px;
    left: auto;
}

/* Search Results Info */
.tutor-search-results-info {
    margin: 15px 0;
    padding: 12px 16px;
    background: #e7f3ff;
    border-left: 4px solid #007cba;
    border-radius: 4px;
    font-size: 14px;
    line-height: 1.5;
}

.tutor-search-results-info strong {
    font-weight: 600;
    color: #333;
}

/* Clear Search Button */
.tutor-search-clear-btn {
    display: inline-block;
    margin-left: 10px;
    padding: 4px 12px;
    background: #f1f1f1;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    color: #666;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.2s ease;
    vertical-align: middle;
}

.tutor-search-clear-btn:hover {
    background: #e1e1e1;
    color: #333;
    text-decoration: none;
    border-color: #ccc;
}

.tutor-search-clear-btn:focus {
    outline: 2px solid #007cba;
    outline-offset: 2px;
}

/* Button Enhancements */
.tutor-btn.tutor-btn-primary {
    background: #007cba;
    border-color: #007cba;
    color: white;
    font-weight: 500;
    transition: all 0.2s ease;
}

.tutor-btn.tutor-btn-primary:hover {
    background: #005a87;
    border-color: #005a87;
    transform: translateY(-1px);
}

.tutor-btn.tutor-btn-ghost {
    background: transparent;
    border: 1px solid #ddd;
    color: #666;
    font-weight: 500;
    transition: all 0.2s ease;
}

.tutor-btn.tutor-btn-ghost:hover {
    background: #f5f5f5;
    border-color: #ccc;
    color: #333;
}

/* Search Icon in Button */
.tutor-btn .tutor-icon-search {
    font-size: 14px;
    vertical-align: middle;
}

/* Loading State */
.tutor-search-loading {
    opacity: 0.6;
    pointer-events: none;
}

.tutor-search-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007cba;
    border-radius: 50%;
    animation: tutor-search-spin 1s linear infinite;
}

@keyframes tutor-search-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* No Results Message */
.tutor-no-search-results {
    text-align: center;
    padding: 40px 20px;
    color: #666;
    font-style: italic;
}

.tutor-no-search-results .tutor-icon {
    font-size: 48px;
    color: #ddd;
    margin-bottom: 16px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .tutor-advanced-search-wrapper {
        padding: 16px;
        margin-bottom: 20px;
    }
    
    .tutor-advanced-search-wrapper .tutor-row {
        margin: 0;
    }
    
    .tutor-advanced-search-wrapper .tutor-col-12 {
        padding: 0 0 16px 0;
    }
    
    .tutor-btn {
        width: 100%;
        margin-bottom: 8px;
        justify-content: center;
    }
    
    .tutor-btn + .tutor-btn {
        margin-left: 0;
    }
    
    .tutor-search-clear-btn {
        display: block;
        margin: 8px 0 0 0;
        text-align: center;
        width: 100%;
    }
    
    .tutor-search-results-info {
        font-size: 13px;
        padding: 10px 12px;
    }
}

@media (max-width: 480px) {
    .tutor-advanced-search-wrapper {
        padding: 12px;
    }
    
    .tutor-form-label {
        font-size: 14px;
        margin-bottom: 8px;
    }
    
    .tutor-advanced-search-input {
        font-size: 16px; /* Prevent zoom on iOS */
    }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .tutor-advanced-search-wrapper {
        border: 2px solid #000;
        background: #fff;
    }
    
    .tutor-search-results-info {
        border-left-width: 6px;
        background: #fff;
        border: 1px solid #000;
    }
    
    .tutor-btn.tutor-btn-primary {
        background: #000;
        border-color: #000;
        color: #fff;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    .tutor-advanced-search-input,
    .tutor-btn,
    .tutor-search-clear-btn {
        transition: none;
    }
    
    @keyframes tutor-search-spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(0deg); }
    }
}

/* Focus Visible Support */
.tutor-advanced-search-input:focus-visible,
.tutor-btn:focus-visible,
.tutor-search-clear-btn:focus-visible {
    outline: 2px solid #007cba;
    outline-offset: 2px;
}

/* Print Styles */
@media print {
    .tutor-advanced-search-wrapper,
    .tutor-btn {
        display: none;
    }
}
