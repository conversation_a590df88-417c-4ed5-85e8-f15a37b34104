/**
 * <PERSON><PERSON> Advanced Search Styles - Enhanced UI
 *
 * @package TutorLMSAdvancedSearch
 * @version 1.1.0
 */

/* CSS Variables for consistent theming */
:root {
    --tutor-search-primary: #3b82f6;
    --tutor-search-primary-hover: #2563eb;
    --tutor-search-secondary: #6b7280;
    --tutor-search-success: #10b981;
    --tutor-search-warning: #f59e0b;
    --tutor-search-danger: #ef4444;
    --tutor-search-light: #f8fafc;
    --tutor-search-border: #e2e8f0;
    --tutor-search-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --tutor-search-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --tutor-search-radius: 8px;
    --tutor-search-radius-lg: 12px;
}

/* Main Search Wrapper */
.tutor-advanced-search-wrapper {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-size: 200% 200%;
    animation: gradientShift 8s ease infinite;
    padding: 32px;
    border-radius: var(--tutor-search-radius-lg);
    box-shadow: var(--tutor-search-shadow-lg);
    margin-bottom: 32px;
    position: relative;
    overflow: hidden;
}

.tutor-advanced-search-wrapper::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1;
}

.tutor-advanced-search-wrapper > * {
    position: relative;
    z-index: 2;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Search Header */
.tutor-search-header {
    text-align: center;
    margin-bottom: 24px;
}

.tutor-search-title {
    font-size: 24px;
    font-weight: 700;
    color: #1f2937;
    margin: 0 0 8px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.tutor-search-title i {
    color: var(--tutor-search-primary);
    font-size: 28px;
}

.tutor-search-subtitle {
    color: var(--tutor-search-secondary);
    font-size: 14px;
    margin: 0;
    font-weight: 400;
}

/* Main Search Input Group */
.tutor-search-main-row {
    margin-bottom: 24px;
}

.tutor-search-input-group {
    display: flex;
    gap: 16px;
    align-items: flex-end;
    flex-wrap: wrap;
}

.tutor-search-input-wrapper {
    flex: 1;
    min-width: 300px;
    position: relative;
}

.tutor-search-input {
    width: 100%;
    padding: 16px 20px 16px 50px;
    font-size: 16px;
    border: 2px solid var(--tutor-search-border);
    border-radius: var(--tutor-search-radius);
    background: white;
    transition: all 0.3s ease;
    box-shadow: var(--tutor-search-shadow);
}

.tutor-search-input:focus {
    border-color: var(--tutor-search-primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    outline: none;
    transform: translateY(-1px);
}

.tutor-search-icon {
    position: absolute;
    left: 18px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--tutor-search-secondary);
    font-size: 18px;
    z-index: 2;
}

.tutor-search-clear-input {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: var(--tutor-search-secondary);
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    z-index: 3;
}

.tutor-search-clear-input:hover {
    background: var(--tutor-search-danger);
    transform: translateY(-50%) scale(1.1);
}

/* Search Action Buttons */
.tutor-search-actions {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.tutor-search-btn,
.tutor-reset-btn {
    padding: 16px 24px;
    border-radius: var(--tutor-search-radius);
    font-weight: 600;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    cursor: pointer;
    text-decoration: none;
    border: 2px solid transparent;
    white-space: nowrap;
}

.tutor-search-btn {
    background: var(--tutor-search-primary);
    color: white;
    box-shadow: var(--tutor-search-shadow);
}

.tutor-search-btn:hover {
    background: var(--tutor-search-primary-hover);
    transform: translateY(-2px);
    box-shadow: var(--tutor-search-shadow-lg);
}

.tutor-reset-btn {
    background: white;
    color: var(--tutor-search-secondary);
    border-color: var(--tutor-search-border);
}

.tutor-reset-btn:hover {
    background: var(--tutor-search-light);
    border-color: var(--tutor-search-secondary);
    color: var(--tutor-search-primary);
    transform: translateY(-1px);
}

/* Active Filters */
.tutor-active-filters {
    background: rgba(59, 130, 246, 0.05);
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: var(--tutor-search-radius);
    padding: 16px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 12px;
}

.tutor-active-filters-label {
    font-weight: 600;
    color: var(--tutor-search-primary);
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 14px;
}

.tutor-filter-tag {
    background: white;
    border: 1px solid var(--tutor-search-border);
    border-radius: 20px;
    padding: 6px 12px;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 6px;
    color: var(--tutor-search-secondary);
    box-shadow: var(--tutor-search-shadow);
}

.tutor-filter-tag i {
    font-size: 12px;
}

.tutor-filter-search {
    border-color: var(--tutor-search-primary);
    color: var(--tutor-search-primary);
    background: rgba(59, 130, 246, 0.1);
}

.tutor-filter-course {
    border-color: var(--tutor-search-success);
    color: var(--tutor-search-success);
    background: rgba(16, 185, 129, 0.1);
}

.tutor-filter-date {
    border-color: var(--tutor-search-warning);
    color: var(--tutor-search-warning);
    background: rgba(245, 158, 11, 0.1);
}

.tutor-filter-remove {
    background: rgba(0, 0, 0, 0.1);
    color: var(--tutor-search-secondary);
    border-radius: 50%;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    font-weight: bold;
    font-size: 12px;
    margin-left: 4px;
    transition: all 0.2s ease;
}

.tutor-filter-remove:hover {
    background: var(--tutor-search-danger);
    color: white;
    transform: scale(1.1);
}

/* Advanced Filters Section */
.tutor-advanced-filters {
    background: white;
    border: 1px solid var(--tutor-search-border);
    border-radius: var(--tutor-search-radius);
    overflow: hidden;
    box-shadow: var(--tutor-search-shadow);
}

.tutor-filters-header {
    background: var(--tutor-search-light);
    padding: 16px 20px;
    border-bottom: 1px solid var(--tutor-search-border);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.tutor-filters-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--tutor-search-secondary);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.tutor-filters-toggle {
    background: none;
    border: none;
    color: var(--tutor-search-primary);
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border-radius: var(--tutor-search-radius);
    transition: all 0.2s ease;
}

.tutor-filters-toggle:hover {
    background: rgba(59, 130, 246, 0.1);
}

.tutor-toggle-icon {
    transition: transform 0.3s ease;
}

.tutor-filters-toggle[aria-expanded="true"] .tutor-toggle-icon {
    transform: rotate(180deg);
}

.tutor-filters-content {
    padding: 20px;
    transition: all 0.3s ease;
}

.tutor-filters-content.tutor-collapse {
    display: none;
}

.tutor-filters-content.tutor-show {
    display: block;
}

.tutor-filters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.tutor-filter-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.tutor-filter-label {
    font-weight: 600;
    color: var(--tutor-search-secondary);
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 6px;
}

.tutor-filter-label i {
    color: var(--tutor-search-primary);
    font-size: 16px;
}

/* Custom Select Styling */
.tutor-select-wrapper {
    position: relative;
}

.tutor-select {
    width: 100%;
    padding: 12px 40px 12px 16px;
    border: 2px solid var(--tutor-search-border);
    border-radius: var(--tutor-search-radius);
    background: white;
    font-size: 14px;
    color: var(--tutor-search-secondary);
    appearance: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.tutor-select:focus {
    border-color: var(--tutor-search-primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    outline: none;
}

.tutor-select-arrow {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--tutor-search-secondary);
    pointer-events: none;
    font-size: 14px;
}

/* Date Input Styling */
.tutor-date-wrapper {
    position: relative;
}

.tutor-date-input {
    width: 100%;
    padding: 12px 40px 12px 16px;
    border: 2px solid var(--tutor-search-border);
    border-radius: var(--tutor-search-radius);
    background: white;
    font-size: 14px;
    color: var(--tutor-search-secondary);
    transition: all 0.2s ease;
}

.tutor-date-input:focus {
    border-color: var(--tutor-search-primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    outline: none;
}

.tutor-date-icon {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--tutor-search-secondary);
    pointer-events: none;
    font-size: 16px;
}

/* Search Results Info */
.tutor-search-results-info {
    background: linear-gradient(135deg, #e0f2fe 0%, #f3e5f5 100%);
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: var(--tutor-search-radius);
    padding: 16px 20px;
    margin: 20px 0;
    font-size: 14px;
    color: var(--tutor-search-secondary);
    display: flex;
    align-items: center;
    gap: 12px;
    box-shadow: var(--tutor-search-shadow);
}

.tutor-search-results-info::before {
    content: "ℹ️";
    font-size: 18px;
}

.tutor-search-results-info strong {
    color: var(--tutor-search-primary);
    font-weight: 600;
}

/* Loading States */
.tutor-search-loading {
    opacity: 0.7;
    pointer-events: none;
    position: relative;
}

.tutor-search-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 24px;
    height: 24px;
    margin: -12px 0 0 -12px;
    border: 3px solid rgba(59, 130, 246, 0.2);
    border-top: 3px solid var(--tutor-search-primary);
    border-radius: 50%;
    animation: tutor-search-spin 1s linear infinite;
    z-index: 10;
}

@keyframes tutor-search-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* No Results State */
.tutor-no-search-results {
    text-align: center;
    padding: 60px 20px;
    color: var(--tutor-search-secondary);
    background: var(--tutor-search-light);
    border-radius: var(--tutor-search-radius);
    margin: 20px 0;
}

.tutor-no-search-results::before {
    content: "🔍";
    font-size: 48px;
    display: block;
    margin-bottom: 16px;
}

.tutor-no-search-results h3 {
    font-size: 18px;
    color: var(--tutor-search-secondary);
    margin: 0 0 8px 0;
}

.tutor-no-search-results p {
    font-size: 14px;
    margin: 0;
    opacity: 0.8;
}

/* Loading State */
.tutor-search-loading {
    opacity: 0.6;
    pointer-events: none;
}

.tutor-search-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007cba;
    border-radius: 50%;
    animation: tutor-search-spin 1s linear infinite;
}

@keyframes tutor-search-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* No Results Message */
.tutor-no-search-results {
    text-align: center;
    padding: 40px 20px;
    color: #666;
    font-style: italic;
}

.tutor-no-search-results .tutor-icon {
    font-size: 48px;
    color: #ddd;
    margin-bottom: 16px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .tutor-advanced-search-wrapper {
        padding: 20px 16px;
        margin-bottom: 24px;
    }

    .tutor-search-title {
        font-size: 20px;
    }

    .tutor-search-subtitle {
        font-size: 13px;
    }

    .tutor-search-input-group {
        flex-direction: column;
        gap: 12px;
    }

    .tutor-search-input-wrapper {
        min-width: auto;
    }

    .tutor-search-input {
        padding: 14px 16px 14px 44px;
        font-size: 16px; /* Prevent zoom on iOS */
    }

    .tutor-search-actions {
        width: 100%;
        justify-content: stretch;
    }

    .tutor-search-btn,
    .tutor-reset-btn {
        flex: 1;
        justify-content: center;
        padding: 14px 20px;
    }

    .tutor-active-filters {
        padding: 12px;
        gap: 8px;
    }

    .tutor-filter-tag {
        font-size: 11px;
        padding: 4px 8px;
    }

    .tutor-filters-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .tutor-filters-header {
        padding: 12px 16px;
    }

    .tutor-filters-content {
        padding: 16px;
    }

    .tutor-search-results-info {
        padding: 12px 16px;
        font-size: 13px;
    }
}

@media (max-width: 480px) {
    .tutor-advanced-search-wrapper {
        padding: 16px 12px;
    }

    .tutor-search-title {
        font-size: 18px;
    }

    .tutor-search-input {
        font-size: 16px; /* Prevent zoom on iOS */
        padding: 12px 14px 12px 40px;
    }

    .tutor-search-btn,
    .tutor-reset-btn {
        padding: 12px 16px;
        font-size: 13px;
    }

    .tutor-active-filters {
        flex-direction: column;
        align-items: flex-start;
    }

    .tutor-filter-tag {
        font-size: 10px;
        padding: 3px 6px;
    }
}

/* Accessibility & Focus States */
.tutor-search-input:focus-visible,
.tutor-select:focus-visible,
.tutor-date-input:focus-visible,
.tutor-search-btn:focus-visible,
.tutor-reset-btn:focus-visible,
.tutor-filters-toggle:focus-visible {
    outline: 3px solid var(--tutor-search-primary);
    outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .tutor-advanced-search-wrapper {
        background: white;
        border: 3px solid black;
    }

    .tutor-search-input,
    .tutor-select,
    .tutor-date-input {
        border: 2px solid black;
    }

    .tutor-search-btn {
        background: black;
        border-color: black;
        color: white;
    }

    .tutor-reset-btn {
        border: 2px solid black;
        color: black;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .tutor-advanced-search-wrapper {
        animation: none;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --tutor-search-primary: #60a5fa;
        --tutor-search-primary-hover: #3b82f6;
        --tutor-search-secondary: #9ca3af;
        --tutor-search-light: #1f2937;
        --tutor-search-border: #374151;
    }

    .tutor-advanced-search-wrapper {
        background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
    }

    .tutor-search-input,
    .tutor-select,
    .tutor-date-input,
    .tutor-advanced-filters,
    .tutor-filters-content {
        background: #1f2937;
        border-color: #374151;
        color: #f9fafb;
    }

    .tutor-search-title {
        color: #f9fafb;
    }

    .tutor-filter-tag {
        background: #374151;
        color: #d1d5db;
    }
}

/* Print Styles */
@media print {
    .tutor-advanced-search-wrapper,
    .tutor-search-actions {
        display: none !important;
    }
}

/* Utility Classes */
.tutor-sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.tutor-fade-in {
    animation: tutorFadeIn 0.3s ease-in-out;
}

@keyframes tutorFadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

.tutor-pulse {
    animation: tutorPulse 2s infinite;
}

@keyframes tutorPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}
