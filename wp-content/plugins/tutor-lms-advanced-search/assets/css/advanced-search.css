/**
 * <PERSON><PERSON> Advanced Search Styles
 * 
 * @package TutorLMSAdvancedSearch
 * @version 1.0.0
 */

/* Search Wrapper */
.tutor-advanced-search-wrapper {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    padding: 24px;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
    margin-bottom: 28px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    position: relative;
    overflow: hidden;
}

.tutor-advanced-search-wrapper::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #3b82f6, #06b6d4, #10b981);
    border-radius: 12px 12px 0 0;
}

/* Search Label Enhancements */
.tutor-search-label {
    font-weight: 600;
    color: #374151;
    font-size: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.tutor-search-label-icon {
    font-size: 16px;
}

/* Search Input Wrapper */
.tutor-search-input-wrapper {
    position: relative;
}

/* Clear Input Button */
.tutor-search-clear-input {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    background: #f3f4f6;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    z-index: 3;
}

.tutor-search-clear-input:hover {
    background: #e5e7eb;
    transform: translateY(-50%) scale(1.1);
}

.tutor-search-clear-input .tutor-icon-times {
    font-size: 12px;
    color: #6b7280;
}

/* Search Tips */
.tutor-search-tips {
    margin-top: 8px;
}

.tutor-search-tip-text {
    color: #6b7280;
    font-size: 13px;
    font-style: italic;
    display: flex;
    align-items: center;
    gap: 4px;
}

/* Search Input Styling */
.tutor-advanced-search-input {
    width: 100%;
    padding: 14px 16px 14px 48px !important;
    font-size: 15px;
    font-weight: 400;
    line-height: 1.5;
    color: #374151;
    background: #ffffff;
    border: 2px solid #e5e7eb;
    border-radius: 10px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.tutor-advanced-search-input:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), 0 4px 12px rgba(0, 0, 0, 0.1);
    outline: none;
    background: #ffffff;
    transform: translateY(-1px);
}

.tutor-advanced-search-input::placeholder {
    color: #9ca3af;
    font-weight: 400;
}

/* Form Icon Positioning */
.tutor-form-wrap {
    position: relative;
}

.tutor-form-icon {
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: #6b7280;
    z-index: 2;
    font-size: 18px;
    transition: color 0.3s ease;
}

.tutor-form-wrap:focus-within .tutor-form-icon {
    color: #3b82f6;
}

.tutor-form-icon-reverse {
    right: 16px;
    left: auto;
}

/* Search Results Info */
.tutor-search-results-info {
    margin: 20px 0;
    padding: 16px 20px;
    background: linear-gradient(135deg, #dbeafe 0%, #e0f2fe 100%);
    border: 1px solid #bfdbfe;
    border-left: 4px solid #3b82f6;
    border-radius: 10px;
    font-size: 14px;
    line-height: 1.6;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);
    position: relative;
}

.tutor-search-results-info::before {
    content: '🔍';
    position: absolute;
    left: -2px;
    top: -2px;
    background: #3b82f6;
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

.tutor-search-results-info strong {
    font-weight: 600;
    color: #1e40af;
}

/* Clear Search Button */
.tutor-search-clear-btn {
    display: inline-block;
    margin-left: 12px;
    padding: 6px 16px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    text-decoration: none;
    color: #64748b;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    vertical-align: middle;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.tutor-search-clear-btn:hover {
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    color: #475569;
    text-decoration: none;
    border-color: #cbd5e1;
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.tutor-search-clear-btn:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

.tutor-search-clear-btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Button Enhancements */
.tutor-btn.tutor-btn-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    border: 2px solid transparent;
    color: white;
    font-weight: 600;
    font-size: 14px;
    padding: 12px 24px;
    border-radius: 10px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
    position: relative;
    overflow: hidden;
}

.tutor-btn.tutor-btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.tutor-btn.tutor-btn-primary:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.4);
}

.tutor-btn.tutor-btn-primary:hover::before {
    left: 100%;
}

.tutor-btn.tutor-btn-ghost {
    background: #ffffff;
    border: 2px solid #e5e7eb;
    color: #6b7280;
    font-weight: 500;
    font-size: 14px;
    padding: 12px 20px;
    border-radius: 10px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.tutor-btn.tutor-btn-ghost:hover {
    background: #f9fafb;
    border-color: #d1d5db;
    color: #374151;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Button Icon and Text Layout */
.tutor-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
}

.tutor-btn-icon {
    display: flex;
    align-items: center;
    font-size: 14px;
}

.tutor-btn-text {
    font-weight: inherit;
}

/* Search Actions Container */
.tutor-search-actions {
    gap: 12px;
    flex-wrap: wrap;
}

.tutor-search-submit-btn {
    min-width: 120px;
    justify-content: center;
}

.tutor-clear-all-btn {
    min-width: 100px;
    justify-content: center;
}

/* Button Hover Effects */
.tutor-btn:active {
    transform: translateY(0) !important;
    transition: none;
}

/* Enhanced Button Animations */
@keyframes button-pulse {
    0% { box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4); }
    70% { box-shadow: 0 0 0 10px rgba(59, 130, 246, 0); }
    100% { box-shadow: 0 0 0 0 rgba(59, 130, 246, 0); }
}

@keyframes search-glow {
    0%, 100% { box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), 0 4px 12px rgba(0, 0, 0, 0.1); }
    50% { box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2), 0 6px 16px rgba(0, 0, 0, 0.15); }
}

@keyframes fade-in-up {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.tutor-btn.tutor-btn-primary:focus {
    animation: button-pulse 1.5s infinite;
}

.tutor-advanced-search-input:focus {
    animation: search-glow 2s ease-in-out infinite;
}

.tutor-advanced-search-wrapper {
    animation: fade-in-up 0.5s ease-out;
}

.tutor-search-results-info {
    animation: fade-in-up 0.3s ease-out;
}

/* Micro-interactions */
.tutor-search-label-icon {
    transition: transform 0.2s ease;
}

.tutor-search-label:hover .tutor-search-label-icon {
    transform: scale(1.1) rotate(5deg);
}

.tutor-form-icon {
    transition: all 0.3s ease;
}

.tutor-search-input-wrapper:hover .tutor-form-icon {
    transform: translateY(-50%) scale(1.1);
    color: #3b82f6;
}

/* Loading State */
.tutor-search-loading {
    opacity: 0.6;
    pointer-events: none;
}

.tutor-search-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007cba;
    border-radius: 50%;
    animation: tutor-search-spin 1s linear infinite;
}

@keyframes tutor-search-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* No Results Message */
.tutor-no-search-results {
    text-align: center;
    padding: 40px 20px;
    color: #666;
    font-style: italic;
}

.tutor-no-search-results .tutor-icon {
    font-size: 48px;
    color: #ddd;
    margin-bottom: 16px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .tutor-advanced-search-wrapper {
        padding: 20px 16px;
        margin-bottom: 20px;
        border-radius: 10px;
    }

    .tutor-advanced-search-wrapper .tutor-row {
        margin: 0;
    }

    .tutor-advanced-search-wrapper .tutor-col-12 {
        padding: 0 0 16px 0;
    }

    .tutor-search-actions {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    .tutor-btn {
        width: 100%;
        margin-bottom: 0;
        justify-content: center;
        padding: 14px 20px;
        font-size: 15px;
    }

    .tutor-search-submit-btn,
    .tutor-clear-all-btn {
        min-width: auto;
    }

    .tutor-search-clear-btn {
        display: block;
        margin: 8px 0 0 0;
        text-align: center;
        width: 100%;
    }

    .tutor-search-results-info {
        font-size: 13px;
        padding: 12px 14px;
        border-radius: 8px;
    }

    .tutor-search-tips {
        margin-top: 6px;
    }

    .tutor-search-tip-text {
        font-size: 12px;
    }

    .tutor-advanced-search-input {
        font-size: 16px; /* Prevent zoom on iOS */
        padding: 16px 16px 16px 48px !important;
    }
}

@media (max-width: 480px) {
    .tutor-advanced-search-wrapper {
        padding: 12px;
    }
    
    .tutor-form-label {
        font-size: 14px;
        margin-bottom: 8px;
    }
    
    .tutor-advanced-search-input {
        font-size: 16px; /* Prevent zoom on iOS */
    }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .tutor-advanced-search-wrapper {
        border: 2px solid #000;
        background: #fff;
    }
    
    .tutor-search-results-info {
        border-left-width: 6px;
        background: #fff;
        border: 1px solid #000;
    }
    
    .tutor-btn.tutor-btn-primary {
        background: #000;
        border-color: #000;
        color: #fff;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    .tutor-advanced-search-input,
    .tutor-btn,
    .tutor-search-clear-btn {
        transition: none;
    }
    
    @keyframes tutor-search-spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(0deg); }
    }
}

/* Focus Visible Support */
.tutor-advanced-search-input:focus-visible,
.tutor-btn:focus-visible,
.tutor-search-clear-btn:focus-visible {
    outline: 2px solid #007cba;
    outline-offset: 2px;
}

/* Print Styles */
@media print {
    .tutor-advanced-search-wrapper,
    .tutor-btn {
        display: none;
    }
}
