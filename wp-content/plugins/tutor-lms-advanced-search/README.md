# Tutor LMS Advanced Search

A WordPress plugin that adds advanced search functionality to the Tutor LMS instructor dashboard Quiz Attempts page without modifying core Tutor LMS files.

## Features

- **Advanced Search**: Search quiz attempts by student name, email, quiz title, or course title
- **Enhanced Filters**: Improved filter interface with search integration
- **Real-time Search**: Optional real-time search as you type (configurable)
- **Responsive Design**: Mobile-friendly search interface
- **Keyboard Shortcuts**: Ctrl/Cmd + K to focus search, Escape to clear
- **Search Persistence**: Maintains search terms across pagination
- **Clear Filters**: Easy way to reset all search and filter criteria
- **No Core Modifications**: Uses Tutor LMS hooks and template override system

## Requirements

- WordPress 5.0 or higher
- PHP 7.4 or higher
- Tutor LMS plugin (any recent version)
- Active Tutor LMS instructor account

## Installation

1. **Download/Clone**: Download or clone this plugin to your WordPress plugins directory:
   ```
   wp-content/plugins/tutor-lms-advanced-search/
   ```

2. **Activate Plugin**: Go to WordPress Admin → Plugins and activate "Tutor LMS Advanced Search"

3. **Verify Installation**: Navigate to your instructor dashboard Quiz Attempts page to see the new search functionality

## Usage

### Basic Search
1. Go to your instructor dashboard: `/dashboard/quiz-attempts/`
2. Use the search field to enter student name, email, quiz name, or course name
3. Click "Search" or press Enter
4. Results will be filtered based on your search term

### Advanced Filtering
- **Course Filter**: Select specific courses to filter attempts
- **Date Filter**: Filter by attempt date
- **Sort Order**: Choose ascending or descending order
- **Combined Filters**: Use search with other filters for precise results

### Keyboard Shortcuts
- **Ctrl/Cmd + K**: Focus the search input field
- **Escape**: Clear search when search field is focused
- **Enter**: Submit search form

### Clear Filters
- Click "Clear All" button to reset all filters and search terms
- Click "Clear Search" link in search results to remove only the search term

## Technical Details

### How It Works
The plugin uses Tutor LMS's built-in template override system and hooks:

1. **Template Override**: Uses `load_dashboard_template_part_from_other_location` filter
2. **Search Integration**: Leverages existing `QuizModel::get_quiz_attempts()` search parameter
3. **No Database Changes**: Uses existing Tutor LMS database structure
4. **Hook-Based**: Integrates through WordPress and Tutor LMS action/filter hooks

### File Structure
```
tutor-lms-advanced-search/
├── tutor-lms-advanced-search.php     # Main plugin file
├── includes/
│   ├── class-template-override.php   # Template override handler
│   └── class-search-handler.php      # Search functionality
├── templates/
│   └── dashboard/
│       ├── quiz-attempts.php         # Enhanced quiz attempts template
│       └── elements/
│           └── filters.php           # Enhanced filters template
├── assets/
│   ├── css/
│   │   └── advanced-search.css       # Plugin styles
│   └── js/
│       └── advanced-search.js        # Plugin JavaScript
└── README.md                         # This file
```

### Search Capabilities
The search functionality searches across:
- Student email addresses
- Student display names
- Quiz titles
- Course titles

## Configuration

### Enable Real-time Search
To enable real-time search (search as you type), edit the JavaScript file:

```javascript
// In assets/js/advanced-search.js
config: {
    enableRealTimeSearch: true,  // Change to true
    searchDelay: 500,           // Delay in milliseconds
    minSearchLength: 2          // Minimum characters before search
}
```

### Customize Search Delay
Adjust the search delay for real-time search:

```javascript
config: {
    searchDelay: 300  // Faster response (300ms)
}
```

## Compatibility

### Tutor LMS Versions
- Compatible with Tutor LMS 1.x and 2.x
- Tested with Tutor LMS Pro features
- Works with both free and pro versions

### WordPress Themes
- Compatible with any WordPress theme
- Uses Tutor LMS's existing styling
- Responsive design works with mobile themes

### Other Plugins
- No known conflicts with popular WordPress plugins
- Compatible with caching plugins
- Works with security plugins

## Troubleshooting

### Search Not Working
1. Verify Tutor LMS is active and up to date
2. Check that you're logged in as an instructor
3. Ensure you have quiz attempts to search through
4. Clear browser cache and try again

### Styling Issues
1. Check if your theme overrides Tutor LMS styles
2. Inspect browser console for CSS conflicts
3. Try deactivating other plugins temporarily

### Template Not Loading
1. Verify plugin is activated
2. Check file permissions on plugin directory
3. Look for PHP errors in WordPress debug log

## Development

### Extending the Plugin
The plugin is designed to be extensible:

```php
// Add custom search filters
add_filter('tutor_advanced_search_query_args', function($args) {
    // Modify search arguments
    return $args;
});

// Customize search results
add_filter('tutor_advanced_search_results', function($results) {
    // Modify search results
    return $results;
});
```

### Contributing
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## Support

For support and questions:
1. Check the troubleshooting section above
2. Review Tutor LMS documentation
3. Contact the plugin developer

## License

This plugin is licensed under the GPL v2 or later.

## Changelog

### Version 1.0.0
- Initial release
- Basic search functionality
- Template override system
- Responsive design
- Keyboard shortcuts
- Search persistence
