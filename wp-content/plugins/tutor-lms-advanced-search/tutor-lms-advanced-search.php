<?php
/**
 * Plugin Name: Tutor LMS Advanced Search
 * Plugin URI: https://shakilahamed.com
 * Description: Adds advanced search functionality to Tutor LMS instructor dashboard Quiz Attempts page. Search by student name, email, and more without modifying core files.
 * Version: 1.0.0
 * Author: <PERSON><PERSON><PERSON>
 * Author URI: https://shakilahamed.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: tutor-lms-advanced-search
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * 
 * @package TutorLMSAdvancedSearch
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('TUTOR_LMS_ADVANCED_SEARCH_VERSION', '1.0.0');
define('TUTOR_LMS_ADVANCED_SEARCH_PLUGIN_URL', plugin_dir_url(__FILE__));
define('TUTOR_LMS_ADVANCED_SEARCH_PLUGIN_PATH', plugin_dir_path(__FILE__));
define('TUTOR_LMS_ADVANCED_SEARCH_PLUGIN_FILE', __FILE__);

/**
 * Main Plugin Class
 */
class TutorLMSAdvancedSearch {
    
    /**
     * Plugin instance
     */
    private static $instance = null;
    
    /**
     * Get plugin instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        add_action('plugins_loaded', array($this, 'init'));
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }
    
    /**
     * Initialize plugin
     */
    public function init() {
        // Check if Tutor LMS is active
        if (!$this->is_tutor_lms_active()) {
            add_action('admin_notices', array($this, 'tutor_lms_missing_notice'));
            return;
        }
        
        // Load plugin functionality
        $this->load_dependencies();
        $this->init_hooks();
        
        // Load text domain for translations
        load_plugin_textdomain('tutor-lms-advanced-search', false, dirname(plugin_basename(__FILE__)) . '/languages');
    }
    
    /**
     * Check if Tutor LMS is active
     */
    private function is_tutor_lms_active() {
        return class_exists('TUTOR\\Tutor') || function_exists('tutor');
    }
    
    /**
     * Load plugin dependencies
     */
    private function load_dependencies() {
        require_once TUTOR_LMS_ADVANCED_SEARCH_PLUGIN_PATH . 'includes/class-template-override.php';
        require_once TUTOR_LMS_ADVANCED_SEARCH_PLUGIN_PATH . 'includes/class-search-handler.php';
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Initialize template override
        new TutorLMS_Advanced_Search_Template_Override();
        
        // Initialize search handler
        new TutorLMS_Advanced_Search_Handler();
        
        // Enqueue scripts and styles
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
    }
    
    /**
     * Enqueue scripts and styles
     */
    public function enqueue_scripts() {
        // Only load on Tutor dashboard pages
        if ($this->is_tutor_dashboard_page()) {
            wp_enqueue_style(
                'tutor-lms-advanced-search',
                TUTOR_LMS_ADVANCED_SEARCH_PLUGIN_URL . 'assets/css/advanced-search.css',
                array(),
                TUTOR_LMS_ADVANCED_SEARCH_VERSION
            );
            
            wp_enqueue_script(
                'tutor-lms-advanced-search',
                TUTOR_LMS_ADVANCED_SEARCH_PLUGIN_URL . 'assets/js/advanced-search.js',
                array('jquery'),
                TUTOR_LMS_ADVANCED_SEARCH_VERSION,
                true
            );
            
            // Localize script for AJAX
            wp_localize_script('tutor-lms-advanced-search', 'tutorAdvancedSearch', array(
                'ajaxUrl' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('tutor_advanced_search_nonce'),
                'searchPlaceholder' => __('Search by student name or email...', 'tutor-lms-advanced-search'),
                'clearSearch' => __('Clear Search', 'tutor-lms-advanced-search')
            ));
        }
    }
    
    /**
     * Check if current page is Tutor dashboard
     */
    private function is_tutor_dashboard_page() {
        global $wp_query;
        
        // Check if we're on a Tutor dashboard page
        if (isset($wp_query->query_vars['tutor_dashboard_page'])) {
            return true;
        }
        
        // Check URL pattern as fallback
        $current_url = $_SERVER['REQUEST_URI'] ?? '';
        return strpos($current_url, '/dashboard/') !== false;
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        // Check Tutor LMS dependency
        if (!$this->is_tutor_lms_active()) {
            deactivate_plugins(plugin_basename(__FILE__));
            wp_die(
                __('Tutor LMS Advanced Search requires Tutor LMS to be installed and activated.', 'tutor-lms-advanced-search'),
                __('Plugin Activation Error', 'tutor-lms-advanced-search'),
                array('back_link' => true)
            );
        }
        
        // Set plugin version
        update_option('tutor_lms_advanced_search_version', TUTOR_LMS_ADVANCED_SEARCH_VERSION);
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Clean up if needed
        flush_rewrite_rules();
    }
    
    /**
     * Admin notice for missing Tutor LMS
     */
    public function tutor_lms_missing_notice() {
        ?>
        <div class="notice notice-error">
            <p>
                <?php 
                echo sprintf(
                    __('%1$s requires %2$s to be installed and activated.', 'tutor-lms-advanced-search'),
                    '<strong>Tutor LMS Advanced Search</strong>',
                    '<strong>Tutor LMS</strong>'
                );
                ?>
            </p>
        </div>
        <?php
    }
}

// Initialize the plugin
TutorLMSAdvancedSearch::get_instance();
