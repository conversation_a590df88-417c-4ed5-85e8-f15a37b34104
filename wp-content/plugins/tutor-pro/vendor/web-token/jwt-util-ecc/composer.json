{"name": "web-token/jwt-util-ecc", "description": "ECC Tools for the JWT Framework.", "type": "library", "license": "MIT", "keywords": ["JWS", "JWT", "JWE", "JWA", "JWK", "JWKSet", "<PERSON><PERSON>", "<PERSON>", "RFC7515", "RFC7516", "RFC7517", "RFC7518", "RFC7519", "RFC7520", "Bundle", "Symfony"], "homepage": "https://github.com/web-token", "authors": [{"name": "Florent <PERSON>", "homepage": "https://github.com/Spomky"}, {"name": "All contributors", "homepage": "https://github.com/web-token/jwt-framework/contributors"}], "autoload": {"psr-4": {"Jose\\Component\\Core\\Util\\Ecc\\": ""}}, "require": {"brick/math": "^0.8.17|^0.9"}, "suggest": {"ext-gmp": "GMP or BCMath is highly recommended to improve the library performance", "ext-bcmath": "GMP or BCMath is highly recommended to improve the library performance"}}